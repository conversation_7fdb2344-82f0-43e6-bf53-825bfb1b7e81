﻿<Window x:Class="HDTUninstaller.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:hdtUninstaller="clr-namespace:HDTUninstaller"
        WindowStyle="ToolWindow"
        Title="HDT Uninstaller" Height="150" Width="294">
    <Grid Margin="2">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="30"/>
            <RowDefinition Height="40"/>
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>
        <TextBlock Text="Hearthstone Logging:" Grid.Row="0" Grid.Column="0" Margin="0,0,5,0" VerticalAlignment="Center" FontWeight="SemiBold"/>
        <TextBlock Text="HDT Data:" Grid.Row="1" Grid.Column="0" Margin="0,5" VerticalAlignment="Center" FontWeight="SemiBold"/>
        <TextBlock Text="Autostart Entry:" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center" FontWeight="SemiBold"/>
        <Button Content="{Binding TextLogging, RelativeSource={RelativeSource AncestorType=hdtUninstaller:MainWindow}}" Grid.Row="0" Grid.Column="1" Background="{Binding BackgroundLogging, RelativeSource={RelativeSource AncestorType=hdtUninstaller:MainWindow}}" Click="ButtonLogging_Click" FontWeight="SemiBold"/>
        <Button Content="{Binding TextData, RelativeSource={RelativeSource AncestorType=hdtUninstaller:MainWindow}}" Grid.Row="1" Grid.Column="1" Background="{Binding BackgroundData, RelativeSource={RelativeSource AncestorType=hdtUninstaller:MainWindow}}" Click="ButtonData_Click" Margin="0,5" FontWeight="SemiBold"/>
        <Button Content="{Binding TextAutostart, RelativeSource={RelativeSource AncestorType=hdtUninstaller:MainWindow}}" Grid.Row="2" Grid.Column="1" Background="{Binding BackgroundAutostart, RelativeSource={RelativeSource AncestorType=hdtUninstaller:MainWindow}}" Click="ButtonAutostart_Click" FontWeight="SemiBold"/>
        
    </Grid>
</Window>
