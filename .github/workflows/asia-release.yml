name: Asia Release

on:
  release:
    types: [released]

permissions:
  id-token: write
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup NodeJS
        uses: actions/setup-node@v1

      - name: Download Assets
        run: node ./build-scripts/download-release-assets.js ${{ github.event.release.tag_name }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: ${{ secrets.AWS_ASIA_DEPLOY_ROLE_ARN }}
          aws-region: ap-east-1

      - name: Publish
        run: aws s3 sync tmp_asset_downloads "s3://${{ secrets.AWS_S3_BUCKET }}"

      - name: Publish to legacy bucket
        run: aws s3 sync tmp_asset_downloads "s3://${{ secrets.AWS_LEGACY_S3_BUCKET }}" --acl public-read --region ap-northeast-2
