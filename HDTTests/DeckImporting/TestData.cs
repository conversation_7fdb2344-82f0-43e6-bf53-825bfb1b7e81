using System.Linq;
using <PERSON><PERSON><PERSON>_Deck_Tracker.Hearthstone;
using static HearthDb.CardIds.Collectible;
using HmDeck = HearthMirror.Objects.Deck;

namespace HDTTests.DeckImporting
{
	public class TestData
	{
		public static readonly string[] Deck1Cards =
		{
			Druid.AddledGrizzlyOG,
			Druid.<PERSON><PERSON><PERSON><PERSON><PERSON>,
			Druid.AncientOf<PERSON>ar,
			Druid.AnodizedRobo<PERSON><PERSON>,
			Druid.AstralCommunion,
			Druid.AvianaTGT,
			Druid.<PERSON>e,
			Druid.<PERSON><PERSON>,
			Druid.<PERSON><PERSON>,
			Druid.ClawLegacy,
			Druid.<PERSON>rak<PERSON>a<PERSON>G,
			Druid.<PERSON>,
			Druid.Dar<PERSON>susAspirant,
			Druid.DruidOfTheClaw,
			Druid.DruidOfTheFang,
		};

		public static readonly string[] Deck2Cards =
		{
			Hunter.AcidmawTGT,
			Hunter.Alleycat,
			Hunter.AnimalCompanionLegacy,
			Hunter.ArcaneShotLegacy,
			Hunter.<PERSON><PERSON>f<PERSON><PERSON><PERSON>TGT,
			<PERSON>.<PERSON>,
			<PERSON><PERSON>,
			<PERSON><PERSON>,
			<PERSON>.CallOfThe<PERSON>,
			<PERSON>.<PERSON>,
			<PERSON>,
			<PERSON>.<PERSON>,
			<PERSON>.<PERSON>,
			<PERSON>,
			<PERSON>,
		};

		public static readonly string[] <PERSON>3<PERSON>ards =
		{
			Druid.DruidOfTheFlame,
			Druid.DruidOfTheSaber,
			Druid.EarthenScales,
			Druid.ElderLongneck,
			Druid.EnchantedRaven,
			Druid.EvolvingSpores,
			Druid.FandralStaghelm,
			Druid.FeralRage,
			Druid.ForbiddenAncient,
			Druid.ForceOfNature,
			Druid.GiantAnaconda,
			Druid.GroveTender,
			Druid.HealingTouchLegacy,
			Druid.InnervateLegacy,
			Druid.IronbarkProtectorLegacy,
		};

		public static readonly string[] Deck1Cards_MinorChanges = Deck1Cards.Take(14).Concat(new[] { Druid.VerdantLongneck }).ToArray();
		public static readonly string[] Deck1Cards_MajorChanges = Deck1Cards.Take(12).Concat(new[]
		{
			Druid.VerdantLongneck,
			Druid.VirmenSenseiGANGS,
			Druid.VolcanicLumberer
		}).ToArray();

		public static Deck LocalDeck1 => DataGenerator.GetDeck(1, "Druid", "Druid1", Deck1Cards);
		public static Deck LocalDeck2 => DataGenerator.GetDeck(2, "Hunter", "Hunter2", Deck2Cards);
		public static Deck LocalDeck1_DifferentCards => DataGenerator.GetDeck(1, "Druid", "Druid1", Deck3Cards);
		public static Deck LocalDeck1_MinorChanges => DataGenerator.GetDeck(1, "Druid", "Druid1", Deck1Cards_MinorChanges);
		public static Deck LocalDeck1_MajorChanges => DataGenerator.GetDeck(1, "Druid", "Druid1", Deck1Cards_MajorChanges);

		public static HmDeck RemoteDeck1 => DataGenerator.GetHmDeck(1, Druid.MalfurionStormrageHeroHeroSkins, "Druid1", Deck1Cards);
		public static HmDeck RemoteDeck1_DifferentCards => DataGenerator.GetHmDeck(1, Druid.MalfurionStormrageHeroHeroSkins, "Druid1", Deck3Cards);
		public static HmDeck RemoteDeck1_MinorChanges => DataGenerator.GetHmDeck(1, Druid.MalfurionStormrageHeroHeroSkins, "Druid1", Deck1Cards_MinorChanges);
		public static HmDeck RemoteDeck1_MajorChanges => DataGenerator.GetHmDeck(1, Druid.MalfurionStormrageHeroHeroSkins, "Druid1", Deck1Cards_MajorChanges);
		public static HmDeck RemoteDeck2 => DataGenerator.GetHmDeck(2, Hunter.RexxarHeroHeroSkins, "Hunter2", Deck2Cards);
	}
}
