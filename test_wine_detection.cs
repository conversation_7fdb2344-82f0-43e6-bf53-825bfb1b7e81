using System;
using System.IO;
using Microsoft.Win32;

namespace WineDetectionTest
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Testing Wine Detection Methods...");
            Console.WriteLine();

            // Test 1: Registry check
            Console.WriteLine("1. Checking Wine registry key...");
            try
            {
                using var wineKey = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Wine");
                if (wineKey != null)
                {
                    Console.WriteLine("   ✓ Wine registry key found");
                }
                else
                {
                    Console.WriteLine("   ✗ Wine registry key not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ Error checking registry: {ex.Message}");
            }

            // Test 2: Environment variables
            Console.WriteLine("2. Checking Wine environment variables...");
            var winePrefix = Environment.GetEnvironmentVariable("WINEPREFIX");
            var winearch = Environment.GetEnvironmentVariable("WINEARCH");
            var waylandDisplay = Environment.GetEnvironmentVariable("WAYLAND_DISPLAY");
            var xdgSessionType = Environment.GetEnvironmentVariable("XDG_SESSION_TYPE");

            Console.WriteLine($"   WINEPREFIX: {winePrefix ?? "not set"}");
            Console.WriteLine($"   WINEARCH: {winearch ?? "not set"}");
            Console.WriteLine($"   WAYLAND_DISPLAY: {waylandDisplay ?? "not set"}");
            Console.WriteLine($"   XDG_SESSION_TYPE: {xdgSessionType ?? "not set"}");

            if (!string.IsNullOrEmpty(winePrefix) || !string.IsNullOrEmpty(winearch))
            {
                Console.WriteLine("   ✓ Wine environment detected");
            }
            else
            {
                Console.WriteLine("   ✗ Wine environment not detected");
            }

            // Test 3: Wayland detection
            Console.WriteLine("3. Checking Wayland session...");
            bool isWayland = !string.IsNullOrEmpty(waylandDisplay) || 
                           (xdgSessionType?.Equals("wayland", StringComparison.OrdinalIgnoreCase) == true);
            
            if (isWayland)
            {
                Console.WriteLine("   ✓ Wayland session detected");
            }
            else
            {
                Console.WriteLine("   ✗ Wayland session not detected");
            }

            // Test 4: Wine DLL files
            Console.WriteLine("4. Checking for Wine DLL files...");
            var systemDir = Environment.GetFolderPath(Environment.SpecialFolder.System);
            var wineFiles = new[] { "ntdll.dll.so", "kernel32.dll.so", "user32.dll.so" };
            
            Console.WriteLine($"   System directory: {systemDir}");
            foreach (var file in wineFiles)
            {
                var fullPath = Path.Combine(systemDir, file);
                if (File.Exists(fullPath))
                {
                    Console.WriteLine($"   ✓ Found {file}");
                }
                else
                {
                    Console.WriteLine($"   ✗ Not found {file}");
                }
            }

            Console.WriteLine();
            Console.WriteLine("Summary:");
            Console.WriteLine($"Running under Wine: {IsRunningUnderWine()}");
            Console.WriteLine($"Wayland session: {isWayland}");
            Console.WriteLine($"Requires workarounds: {IsRunningUnderWine() && isWayland}");
        }

        private static bool IsRunningUnderWine()
        {
            try
            {
                // Check for Wine registry key
                using var wineKey = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Wine");
                if (wineKey != null)
                    return true;

                // Check for Wine environment variables
                var winePrefix = Environment.GetEnvironmentVariable("WINEPREFIX");
                var winearch = Environment.GetEnvironmentVariable("WINEARCH");
                if (!string.IsNullOrEmpty(winePrefix) || !string.IsNullOrEmpty(winearch))
                    return true;

                // Check for Wine-specific DLLs
                var systemDir = Environment.GetFolderPath(Environment.SpecialFolder.System);
                var wineFiles = new[] { "ntdll.dll.so", "kernel32.dll.so", "user32.dll.so" };
                foreach (var file in wineFiles)
                {
                    if (File.Exists(Path.Combine(systemDir, file)))
                        return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
