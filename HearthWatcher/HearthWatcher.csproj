﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net472</TargetFramework>
    <AssemblyTitle>HearthWatcher</AssemblyTitle>
    <Company>HearthSim</Company>
    <Product>HearthWatcher</Product>
    <Copyright>Copyright © HearthSim 2022</Copyright>
    <Platforms>x86</Platforms>
    <PlatformTarget>x86</PlatformTarget>
    <Prefer32Bit>true</Prefer32Bit>
    <LangVersion>10</LangVersion>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <OutputPath>bin\$(Platform)\$(Configuration)\</OutputPath>
    <DebugType>full</DebugType>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\$(Platform)\$(Configuration)\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Squirrel|x86'">
    <DefineConstants>TRACE;SQUIRREL</DefineConstants>
    <OutputPath>bin\$(Platform)\$(Configuration)\</OutputPath>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="HearthDb">
      <HintPath>..\lib\HearthDb.dll</HintPath>
    </Reference>
    <Reference Include="HearthMirror">
      <HintPath>..\lib\HearthMirror.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Net.Http" />
  </ItemGroup>
</Project>