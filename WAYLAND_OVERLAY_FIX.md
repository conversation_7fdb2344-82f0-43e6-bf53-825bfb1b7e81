# Hearthstone Deck Tracker - Wayland/Hyprland Overlay Fix

## Problem Analysis

The overlay rendering issues you experienced in your Wayland/Hyprland environment with Wine/Lutris were caused by several fundamental incompatibilities:

### Root Causes Identified

1. **WS_EX_TRANSPARENT Window Style**: This Win32 extended window style causes black screen issues in Wayland compositors when used with WPF's `AllowsTransparency="True"`

2. **Layered Window Conflicts**: The combination of WPF transparency and Win32 layered windows doesn't translate properly through Wine to Wayland

3. **Compositor Interaction Issues**: Wayland compositors handle window transparency and click-through differently than X11/Windows

4. **Missing Platform Detection**: The application had no Wine or Linux environment detection

## Solution Implemented

### 1. Platform Detection (Helper.cs)

Added comprehensive Wine and Wayland detection:

```csharp
public static bool IsRunningUnderWine()
public static bool IsWaylandSession() 
public static bool RequiresOverlayWorkarounds()
```

Detection methods:
- Wine registry key checking
- Wine environment variables (WINEPREFIX, WINEARCH)
- Wine-specific DLL detection
- Wayland environment detection (WAYLAND_DISPLAY, XDG_SESSION_TYPE)

### 2. Configuration Options (Config.cs)

Added three new configuration options:

```csharp
public bool UseLinuxOverlayWorkarounds = false;
public bool DisableOverlayTransparency = false;
public bool ForceOverlayVisible = false;
```

### 3. Overlay Window Modifications (OverlayWindow.xaml.cs)

#### Window Initialization Changes:
- **Conditional Window Styles**: Different extended window styles based on environment
- **Alternative Click-through**: Uses WS_EX_LAYERED instead of WS_EX_TRANSPARENT for Linux
- **Background Handling**: Automatic visible background for Wayland environments

#### Key Changes:
```csharp
// In Window_SourceInitialized_1
if (Helper.RequiresOverlayWorkarounds() || Config.Instance.UseLinuxOverlayWorkarounds)
{
    // Linux-friendly window styles
    User32.SetWindowExStyle(hwnd, User32.WsExToolWindow | User32.WsExNoActivate);
    SetClickthroughForLinux(true);
}
```

### 4. UI Configuration Options

Added Linux compatibility section in overlay settings:
- Enable Linux/Wayland overlay workarounds
- Disable overlay transparency  
- Force overlay background visible

## How to Use the Fix

### Automatic Detection
The application now automatically detects Wine/Wayland environments and applies workarounds.

### Manual Configuration
If automatic detection fails, you can manually enable the workarounds:

1. Open HDT Settings → Overlay → General
2. Enable "Advanced Options" if not visible
3. In the "Linux/Wine Compatibility" section:
   - ✅ Enable Linux/Wayland overlay workarounds
   - ✅ Disable overlay transparency (if black screen persists)
   - ✅ Force overlay background visible (if overlay is invisible)

### Recommended Settings for Your Environment

For Wayland/Hyprland + Wine/Lutris:

1. **UseLinuxOverlayWorkarounds**: `true` (enables alternative window styles)
2. **DisableOverlayTransparency**: `true` (prevents black screen)
3. **ForceOverlayVisible**: `true` (ensures overlay visibility)

## Technical Details

### Window Style Changes
- **Standard Windows**: `WS_EX_TOOLWINDOW | WS_EX_NOACTIVATE | WS_EX_TRANSPARENT`
- **Linux/Wayland**: `WS_EX_TOOLWINDOW | WS_EX_NOACTIVATE` + `WS_EX_LAYERED` for click-through

### Background Handling
- **Transparent Mode**: `Background = null` (causes issues in Wayland)
- **Visible Mode**: `Background = "#4C0000FF"` (semi-transparent blue)
- **Semi-transparent Mode**: `Background = "#80000000"` (semi-transparent black)

### Click-through Implementation
- **Windows**: Uses `WS_EX_TRANSPARENT` directly
- **Linux**: Uses `WS_EX_LAYERED` as alternative approach

## Testing

Compile and run the modified HDT in your environment. The overlay should now:
- ✅ Display properly without black screen
- ✅ Allow click-through to the game
- ✅ Maintain proper transparency/visibility
- ✅ Eliminate flickering issues

## Fallback Options

If issues persist:

1. **Disable Transparency Completely**: Set `DisableOverlayTransparency = true`
2. **Use Capturable Overlay**: Enable `ShowCapturableOverlay = true` 
3. **Manual Window Positioning**: Use the unlock overlay feature to position elements

## Files Modified

1. `Hearthstone Deck Tracker/Utility/Helper.cs` - Platform detection
2. `Hearthstone Deck Tracker/Config.cs` - Configuration options
3. `Hearthstone Deck Tracker/Windows/OverlayWindow.xaml.cs` - Overlay logic
4. `Hearthstone Deck Tracker/Utility/User32.cs` - Window constants
5. `Hearthstone Deck Tracker/FlyoutControls/Options/Overlay/OverlayGeneral.xaml` - UI
6. `Hearthstone Deck Tracker/FlyoutControls/Options/Overlay/OverlayGeneral.xaml.cs` - UI logic

This solution provides both automatic detection and manual override options to ensure the overlay works properly in your Wayland/Hyprland environment with Wine/Lutris.
