<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.DeckSetIcons.DeckSetIconsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls"
             xmlns:lex="http://wpflocalizeextension.codeplex.com" 
             xmlns:system="clr-namespace:System;assembly=mscorlib"
             xmlns:enums="clr-namespace:HearthDb.Enums;assembly=HearthDb"
             xmlns:deckSetIcons="clr-namespace:Hearthstone_Deck_Tracker.Controls.DeckSetIcons"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             Name="DeckSetIcons"
             mc:Ignorable="d"
             d:DesignHeight="20" d:DesignWidth="200">
    <UserControl.DataContext>
        <deckSetIcons:DeckSetIconsViewModel/>
    </UserControl.DataContext>
    <UserControl.Resources>
        <Canvas x:Key="set_brm" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="F 1 M 56.167,105 L 6.605,105 C 6.605,105 3.996,104.223 6.605,99.565 9.214,94.907 36.976,47.938 36.976,47.938 36.976,47.938 39.212,45.415 41.634,47.837 44.056,50.259 70.887,75.663 70.887,75.663 L 45.174,85.015 C 45.174,85.015 41.448,86.53 43.683,89.139 45.919,91.747 56.167,105 56.167,105 Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
            <Path Data="F 1 M 49.337,88.013 L 68.837,105 80.389,105 91.568,88.013 C 91.568,88.013 93.059,84.023 95.294,88.122 97.529,92.221 104.797,105 104.797,105 L 120.262,105 C 120.262,105 123.988,104.223 121.939,99.938 119.89,95.653 103.307,64.891 103.307,64.891 103.307,64.891 100.296,59.25 96.663,64.837 L 93,68.697 93,71.212 49.337,88.013 Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
            <Path Data="F 1 M 92.127,68.045 L 79.643,72.703 48.341,49.786 64,25.377 C 64,25.377 68.091,19.974 70.886,25.377 73.681,30.781 92.127,68.045 92.127,68.045 Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_tgt" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="F 1 M 97.467,19 L 111.765,19 C 116.934,99 64,117.891 64,117.891 64,117.891 11.066,99 16.235,19 L 30.493,19 C 30.493,19 47.803,10.73 64.161,10.832 80.519,10.935 97.467,19 97.467,19 Z M 64.107,25.013 C 54.942,25.149 44.967,28.441 37.826,31.139 34.64,32.342 30.339,33.181 30.339,33.181 28.433,86.542 64.107,102.367 64.107,102.367 64.107,102.367 99.781,86.542 97.875,33.181 97.875,33.181 93.574,32.343 90.388,31.139 83.247,28.441 73.272,25.15 64.107,25.013 Z M 91.749,62 L 64.107,62 64.092,100.388 C 83.127,90.382 91.749,62 91.749,62 Z M 36.618,61 L 62,61 62,28.961 C 62,28.961 56.626,28.961 52.406,30.186 48.186,31.411 38.69,35.196 33.078,36.166 33.078,36.166 32.399,44 36.618,61 Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_loe" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="F 1 M 93.728,69.532 C 93.728,69.532 110.879,57.145 115.485,57.145 120.09,57.145 119.138,59.051 118.979,62.545 118.979,62.545 123.765,56.626 119.167,55.081 116.332,54.128 114.682,54.796 114.056,55.081 111.077,56.438 99.763,61.592 90.235,69.533 80.707,77.474 60.219,95.418 44.02,99.547 27.821,103.676 1.618,100.182 2.888,90.495 4.158,80.808 22.422,71.438 29.092,71.279 L 30.045,80.172 C 30.045,80.172 31.951,82.237 35.762,82.395 39.573,82.554 45.767,81.601 50.849,80.013 55.931,78.425 71.494,70.802 80.705,63.497 89.916,56.192 94.68,59.527 98.65,58.256 102.62,56.986 112.943,51.268 116.754,51.268 120.565,51.268 126.918,52.538 124.694,57.938 122.471,63.338 121.994,66.514 112.148,68.578 102.302,70.642 93.728,69.532 93.728,69.532 Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
            <Path Data="F 1 M 92.616,54.128 L 82.929,33.483 C 82.929,33.483 83.882,29.83 80.388,28.242 76.894,26.654 77.212,26.654 72.606,26.813 68.001,26.972 62.125,28.242 59.425,29.036 56.725,29.83 43.862,35.071 41.48,36.024 39.098,36.977 34.173,36.806 33.063,36.977 28.934,37.612 28.334,39.584 28.334,39.584 28.334,39.584 31.681,40.689 36.917,40.584 40.492,40.512 64.512,28.359 74.989,28.878 80.834,29.167 79.277,30.148 79.277,30.148 79.277,30.148 74.513,29.83 67.843,31.418 61.173,33.006 51.327,36.5 48.309,37.612 45.292,38.724 40.051,41.582 37.51,41.741 34.969,41.9 28.775,40.629 27.664,41.265 26.552,41.9 25.441,43.647 26.552,45.235 27.663,46.823 28.458,46.658 28.617,48.722 28.776,50.787 27.346,58.892 29.093,71.279 L 33.54,73.185 C 33.54,73.185 32.905,71.12 34.652,68.738 34.652,68.738 34.97,72.073 35.446,73.185 35.922,74.297 44.498,73.185 45.61,72.232 45.61,72.232 44.895,69.294 45.769,66.674 46.642,64.054 47.913,69.85 48.945,70.962 48.945,70.962 56.33,70.883 60.697,66.515 60.697,66.515 59.427,62.942 60.062,61.115 60.697,59.289 63.654,64.371 64.002,65.006 64.35,65.641 74.831,61.036 75.387,59.924 75.387,59.924 74.911,56.827 74.355,55.716 73.799,54.604 75.387,54.207 79.199,58.575 79.197,58.575 85.946,54.128 92.616,54.128 Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_og" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="F 1 M 77.256,118 L 112,118 C 112,118 105.47,85.117 91.917,73.658 L 89.946,75.42 89.915,78.888 82.245,86.213 78.759,85.405 73.005,89.631 72.635,93.078 70.356,95.22 C 70.356,95.221 78.241,110 77.256,118 Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
            <Path Data="F 1 M 56.803,71.392 C 56.803,71.392 68.303,66.927 74.422,55.991 74.422,55.991 63.842,43.924 36.243,47.374 36.243,47.374 34.597,46.021 33.925,46.015 31.669,45.996 27.044,48.415 26.063,50.447 25.768,51.057 26.063,53.158 26.063,53.158 26.063,53.158 23.291,56.669 22.428,58.517 22.428,58.517 20.046,57.971 19.594,58.517 17.897,60.565 15.898,65.417 16.76,68.312 16.955,68.965 19.357,69.484 19.357,69.484 19.357,69.484 19.47,74.718 20.579,77.552 20.579,77.552 18.742,79.677 18.9,80.601 19.347,83.204 26.015,90.211 26.847,90.119 27.833,90.009 30.189,88.024 30.189,88.024 30.189,88.024 36.226,92.952 38.074,93.322 38.074,93.322 37.905,95.557 38.32,96.125 39.367,97.557 47.36,99.636 48.793,98.743 49.418,98.353 50.148,96.125 50.148,96.125 50.148,96.125 58.896,95.047 60.128,94.431 60.128,94.431 60.505,95.957 60.898,96.125 63.162,97.095 70.693,93.445 71.278,91.412 L 71.478,87.254 77.715,82.819 81.016,83.343 87,77.429 87,73.486 91.153,69.051 95.139,69.297 C 95.139,69.297 99.381,64.122 99.134,60.549 L 97.559,59.317 99.184,51.432 101.891,49.83 C 101.891,49.83 102.88,43.054 101.894,40.713 L 100.048,39.727 100.295,31.842 101.651,30.733 C 101.651,30.733 102.021,25.065 100.542,22.971 L 98.201,22.232 C 98.201,22.232 93.026,9.911 77.01,9.665 60.994,9.419 54.606,20.005 54.463,26.298 54.34,31.719 55.079,33.074 57.666,33.198 58.921,33.258 59.884,32.089 62.841,25.559 65.798,19.029 74.053,20.138 75.654,20.138 77.255,20.138 86.669,21.5 85.634,33.691 84.525,46.751 70.726,74.596 48.549,75.335 48.549,75.335 41.258,75.376 41.28,70.776 41.292,68.312 42.758,66.71 45.839,67.08 48.92,67.45 51.979,68.221 56.803,71.392 Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_gvg" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="F 1 M 39.618,29.759 L 36.786,20.584 C 35.953,18.264 36.261,17.611 38.581,16.779 L 53.633,11.377 C 55.953,10.544 56.606,10.852 57.438,13.172 L 61.055,22.056" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
            <Path Data="F 1 M 112.096,101.612 L 111.14,101.906 108.308,92.7315 108.786,92.584 108.315,92.753 107.893,91.1921 108.049,89.9102 108.939,88.9746 110.412,88.3083 110.581,88.779 110.412,88.3084 125.464,82.9064 125.633,83.377 125.464,82.9064 127.025,82.484 128.307,82.6399 129.242,83.5301 129.909,85.0032 129.438,85.172 129.901,84.9835 133.518,93.8675 132.592,94.2445 128.975,85.3605 128.971,85.3507 128.967,85.3408 128.395,84.0612 127.88,83.5443 127.154,83.4756 125.802,83.8476 125.802,83.8476 125.802,83.8476 110.75,89.2496 110.75,89.2496 110.75,89.2496 109.47,89.8219 108.953,90.3368 108.885,91.0633 109.257,92.415 109.26,92.4257 109.264,92.4365 112.096,101.612" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
            <Path Data="F 1 M 22.515,61.246 L 13.576,57.74 C 11.243,56.945 10.925,56.297 11.72,53.964 L 16.881,38.828 C 17.676,36.495 18.324,36.177 20.657,36.972 L 29.858,39.683" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
            <Path Data="F 1 M 94.6976,132.781 L 94.3324,133.711 85.3934,130.205 85.576,129.74 85.4147,130.213 83.9312,129.57 83.0263,128.649 82.8497,127.37 83.2467,125.803 83.2467,125.803 83.2468,125.803 88.4078,110.667 88.881,110.828 88.4077,110.667 89.0505,109.183 89.9717,108.278 91.2509,108.102 92.8183,108.499 92.657,108.972 92.7983,108.492 101.999,111.203 101.717,112.163 92.5157,109.452 92.5056,109.449 92.4957,109.445 91.1382,109.095 90.4128,109.176 89.9061,109.701 89.3543,110.989 89.3543,110.989 89.3542,110.989 84.1932,126.125 83.72,125.964 84.1933,126.125 83.8433,127.483 83.9237,128.208 84.4488,128.715 85.7373,129.267 85.748,129.27 85.7586,129.275 94.6976,132.781" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
            <Path Data="F 1 M 36.47,94.249 L 28.156,99.052 C 26.08,100.38 25.375,100.225 24.047,98.149 L 15.43,84.676 C 14.102,82.6 14.257,81.895 16.333,80.567 L 24.189,75.064" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
            <Path Data="F 1 M 108.22,165.816 L 108.72,166.682 100.406,171.485 100.156,171.052 100.425,171.473 98.998,172.232 97.7137,172.365 96.6035,171.706 95.6258,170.418 87.0088,156.945 86.2498,155.518 86.1167,154.234 86.7761,153.124 88.0636,152.146 88.333,152.567 88.0461,152.157 95.9021,146.654 96.4759,147.474 88.6199,152.977 88.6113,152.983 88.6024,152.988 87.4826,153.831 87.0933,154.448 87.188,155.172 87.8512,156.407 96.4682,169.88 97.3112,170.999 97.9283,171.389 98.6518,171.294 99.8866,170.631 99.8961,170.625 99.9059,170.619 108.22,165.816" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
            <Path Data="F 1 M 70.974,103.915 L 69.545,113.41 C 69.289,115.861 68.728,116.316 66.277,116.06 L 50.372,114.397 C 47.921,114.141 47.466,113.58 47.722,111.129 L 48.317,101.556" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
            <Path Data="F 1 M 142.48,175.841 L 143.468,175.989 142.039,185.484 141.545,185.41 142.042,185.462 141.746,187.051 141.049,188.138 139.841,188.596 138.225,188.557 138.225,188.557 138.225,188.557 122.32,186.894 122.372,186.397 122.32,186.894 120.731,186.598 119.644,185.901 119.187,184.693 119.225,183.077 119.722,183.129 119.223,183.098 119.818,173.525 120.816,173.587 120.221,183.16 120.22,183.171 120.219,183.181 120.18,184.582 120.42,185.271 121.045,185.648 122.424,185.9 122.424,185.9 122.424,185.9 138.329,187.563 138.277,188.06 138.329,187.563 139.73,187.602 140.419,187.362 140.796,186.737 141.048,185.358 141.049,185.347 141.051,185.336 142.48,175.841" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
            <Path Data="F 1 M 100.043,82.964 L 106.576,90.001 C 108.333,91.73 108.339,92.452 106.61,94.208 L 95.394,105.607 C 93.665,107.364 92.943,107.37 91.187,105.641 L 84.073,99.207" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
            <Path Data="F 1 M 171.677,155.304 L 172.409,154.624 178.942,161.661 178.576,162.001 178.927,161.645 179.984,162.868 180.4,164.09 180.004,165.319 178.966,166.559 178.61,166.208 178.966,166.559 167.75,177.958 166.527,179.015 165.305,179.431 164.076,179.035 162.836,177.997 163.187,177.641 162.852,178.012 155.738,171.578 156.408,170.836 163.522,177.27 163.53,177.277 163.538,177.285 164.608,178.189 165.297,178.431 165.981,178.178 167.038,177.256 178.254,165.857 178.254,165.857 178.254,165.857 179.158,164.787 179.4,164.098 179.147,163.414 178.225,162.357 178.217,162.349 178.21,162.341 171.677,155.304" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
            <Path Data="F 1 M 101.787,47.174 L 111.362,46.454 C 113.809,46.158 114.377,46.604 114.673,49.051 L 116.592,64.927 C 116.888,67.374 116.442,67.942 113.995,68.238 L 104.529,69.788" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
            <Path Data="F 1 M 173.824,119.673 L 173.75,118.675 183.325,117.955 183.362,118.454 183.302,117.958 184.918,117.893 186.133,118.33 186.847,119.406 187.169,120.991 189.088,136.867 189.153,138.483 188.716,139.698 187.64,140.412 186.055,140.734 185.995,140.238 186.076,140.731 176.61,142.281 176.448,141.295 185.914,139.745 185.925,139.743 185.935,139.742 187.31,139.468 187.929,139.081 188.157,138.387 188.096,136.987 186.177,121.111 185.903,119.736 185.516,119.117 184.822,118.889 183.422,118.95 183.411,118.952 183.399,118.953 173.824,119.673" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
            <Path Data="F 1 M 74.892,23.497 L 80.298,15.562 C 81.592,13.464 82.295,13.298 84.392,14.592 L 98.001,22.99 C 100.099,24.284 100.265,24.987 98.971,27.084 L 94.282,35.451" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
            <Path Data="F 1 M 147.305,95.7785 L 146.479,95.2155 151.885,87.2805 152.298,87.562 151.872,87.2995 152.829,85.9959 153.929,85.3185 155.215,85.4308 156.655,86.1665 170.264,94.5645 170.001,94.99 170.263,94.5644 171.567,95.5212 172.245,96.6207 172.132,97.9071 171.397,99.3466 170.971,99.084 171.407,99.3284 166.718,107.695 165.846,107.207 170.535,98.8396 170.54,98.8304 170.546,98.8214 171.189,97.5761 171.271,96.8511 170.872,96.2402 169.739,95.4156 169.738,95.4155 169.738,95.4155 156.129,87.0175 154.884,86.3745 154.159,86.2915 153.548,86.6909 152.724,87.8245 152.718,87.8341 152.711,87.8435 147.305,95.7785" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
            <Path Data="F 1 M 77.997,58.346 C 80.772,66.079 76.753,74.597 69.02,77.373 61.287,80.149 52.769,76.129 49.993,68.396 47.218,60.663 51.237,52.145 58.97,49.369 66.703,46.594 75.221,50.613 77.997,58.346 Z M 93.924,52.63 C 87.957,36.004 69.643,27.363 53.017,33.33 36.391,39.297 27.75,57.611 33.717,74.237 39.684,90.863 57.998,99.504 74.624,93.537 91.25,87.57 99.89,69.256 93.924,52.63 Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
            <Path Data="F 1 M 96.549,51.688 C 103.036,69.764 93.641,89.676 75.566,96.163 57.49,102.65 37.578,93.255 31.091,75.18 24.604,57.105 33.999,37.192 52.074,30.705 70.149,24.218 90.062,33.612 96.549,51.688 Z M 49.813,24.404 C 28.354,32.105 17.201,55.744 24.902,77.203 32.603,98.662 56.242,109.815 77.701,102.114 99.16,94.413 110.313,70.774 102.612,49.315 94.911,27.856 71.272,16.703 49.813,24.404 Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_kara" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="F 1 M 50,54 L 86,54 86,80.333 93.833,94 41.833,94 49.166,80.667 50,54 Z M 67.957,16.916 L 59,16.833 59,26 48,26 48,17 43.417,17.26 C 43.417,17.26 42,17.395 42,20.062 42,24.009 42.104,24.49 42.438,45.375 42.438,45.375 43.459,48.594 49.292,48.76 51.856,48.834 59.682,49 67.818,49 L 67.421,49 C 75.557,49 83.424,48.834 85.988,48.76 91.821,48.593 92.79,45.333 92.79,45.333 93.124,24.449 93.124,23.988 93.124,20.041 93.124,17.374 91.249,17.249 91.249,17.249 L 86,17 86,26 75,26 75,16.833 66.584,16.916 M 69,98.49 C 53.18,98.657 35.834,98.48 35.5,98.48 33,98.48 33.293,99.795 33.167,102.96 33,107.127 33.167,111.086 33.167,111.086 33.167,111.086 32.334,114.421 35,114.588 36.149,114.658 52.203,115 68.166,115 L 67.877,115 C 83.84,115 99.894,114.658 101.043,114.587 103.71,114.42 102.876,111.292 102.876,111.292 102.876,111.292 103.043,107.229 102.876,103.063 102.749,99.899 103.043,98.532 100.543,98.532 100.209,98.532 82.863,98.684 67.043,98.516" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_classic" Clip="M0,0 V128 H128 V0 H0 Z">
            <Path Data="F1 M128,128z M0,0z M95.514,30.88L97.7501,30.34759 103.9261,39.29209 96.0464,40.25043z M110.456,46.607C110.456,46.607 111.5854,46.23053 112.1125,45.4776 112.6396,44.72467 115.0866,44.9129 116.0278,46.00466 115.04897,47.39756 112.2254,48.22586 110.456,46.60701z M108.5288,92.559C111.0318,92.559 112.7008,88.3873 112.7008,88.3873 112.7008,88.3873 124.9188,104.2593 94.7588,112.1703 69.3068,118.8463 42.1858,101.7393 42.1858,101.7393 42.1858,101.7393 45.3933,108.0633 48.4446,111.3353 51.1348,114.2203 56.3723,117.5953 56.3723,117.5953 56.3723,117.5953 55.8051,120.4843 56.3723,122.1843 56.7895,123.4353 94.0643,128.6473 108.9453,115.9263 117.2423,108.8323 119.7933,101.7403 119.7933,97.5673 119.7933,82.9633 106.8593,75.2363 99.7653,76.2873 90.5858,77.6471 95.1755,86.7193 95.1755,86.7193 95.1755,86.7193 95.9692,83.3919 97.679,82.9641 99.3688,82.5412 101.1659,85.0056 103.0149,87.5423 104.8189,90.0167 106.6729,92.56 108.5279,92.56z M121.0488,43.324C121.0488,43.324 120.5438,45.7788 118.1278,45.4102 117.7398,45.351 117.2178,45.0354 116.6358,44.6835 116.0558,44.3329 115.4158,43.9464 114.7898,43.7413 113.0698,43.1777 111.4518,43.324 111.4518,43.324 111.4518,43.324 108.0468,45.888 105.6108,47.0792 102.8328,48.4372 98.1001,49.5827 98.1001,49.5827 98.1001,49.5827 98.8419,52.187 99.7691,53.7552 100.7818,55.4673 103.1068,57.9277 103.1068,57.9277L112.7038,57.9277C112.7038,57.9277 112.3678,59.7721 111.8688,60.8484 111.3748,61.9146 110.1998,63.3519 110.1998,63.3519 110.1998,63.3519 117.5008,63.3519 121.0488,55.8414 124.0058,49.5827 121.0488,43.3244 121.0488,43.3244z M7.96879999999999,41.655L15.0621,39.986C15.0621,39.986 10.1009,44.6557 7.96889999999999,48.7482 4.57369999999999,55.2657 5.88259999999999,67.5242 5.88259999999999,67.5242L15.0621,52.9202C15.0621,52.9202 12.4718,65.1542 13.3931,72.9482 14.1919,79.7059 18.4,89.6382 18.4,89.6382 18.4,89.6382 18.6854,86.6353 19.6518,85.0486 20.6725,83.3729 23.407,81.7105 23.407,81.7105L22.5725,97.5655C22.5725,97.5655 28.9263,105.8257 34.2555,109.6655 38.1593,112.4785 45.1035,115.5075 45.1035,115.5075 45.1035,115.5075 38.0388,106.4865 35.9241,99.6515 34.0838,93.7035 34.2551,83.7965 34.2551,83.7965 34.2551,83.7965 37.1639,91.6036 40.931,95.0625 45.0139,98.8108 53.866,100.9041 53.866,100.9041L50.528,91.7245C50.528,91.7245 55.9141,99.7501 60.959,102.9905 66.0039,106.2309 89.332,105.4945 89.332,105.4945 89.332,105.4945 86.0642,103.7365 84.3251,102.1565 82.6111,100.5985 80.5698,97.5663 80.5698,97.5663L91.4178,99.6526C91.4178,99.6526 75.5628,91.7249 78.0658,76.7046 81.4566,56.3596 98.5108,56.2596 98.5108,56.2596L91.4175,47.0803C91.4175,47.0803 100.0247,48.1639 105.1865,45.4113 107.4605,44.199 110.6115,41.6559 110.6115,41.6559L114.3665,41.6559C114.3665,41.6559 112.5965,39.2661 111.8625,37.0663 111.1295,34.8666 110.6115,30.3902 110.6115,30.3902L108.5245,32.0592C108.5245,32.0592 105.2285,27.2556 102.6835,24.5488 100.1527,21.8577 95.5902,18.29 95.5902,18.29L95.5902,22.8798C95.5902,22.8798 90.2178,19.5433 86.4108,18.29 83.2539999999999,17.2508 78.0658,16.621 78.0658,16.621L80.5693,21.628C80.5693,21.628 72.5443,21.5954 67.6343,22.8798 63.1839,24.044 56.7863,27.4695 56.7863,27.4695L63.4622,28.7212C63.4622,28.7212 52.3342,32.2347 48.4412,37.9008 45.5744,42.0732 48.4412,64.1868 48.4412,64.1868L38.8445,48.7488 35.9238,65.8558C35.9238,65.8558 33.6581,57.9792 34.2548,52.9208 34.78,48.4685 38.01,42.0728 38.01,42.0728L45.1032,52.9208C45.1032,52.9208 42.1825,37.8998 51.7792,24.5478 62.8692,9.11780000000003 75.5622,5.77180000000003 75.5622,5.77180000000003 75.5622,5.77180000000003 56.7862,-1.81349999999997 33.0032,12.0305 19.9122,19.6505 7.96819999999996,41.6555 7.96819999999996,41.6555z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
        </Canvas>
        <Canvas x:Key="set_naxx" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="F 1 M 64,11.689 L 75.582,25.975 77.879,37.576 90.713,24.598 99.899,36.887 105.573,59.446 C 105.573,59.446 109.761,61.877 119.488,55.122 119.488,55.122 124.892,62.023 109.356,74.317 L 112.598,90 117.191,90 117.596,101 112.868,101 96.927,116.59 83.148,103 44.852,103 31.072,116.59 15.131,101 10.403,101 10.808,90 15.401,90 18.643,74.318 C 3.108,62.024 8.512,55.365 8.512,55.365 18.239,62.12 22.427,59.568 22.427,59.568 L 28.101,36.949 37.287,24.629 50.121,37.591 52.418,25.982 64,11.689 Z M 71.8,98 L 77.744,98 C 77.879,92 75.492,87.571 75.492,87.571 75.492,87.571 77.392,87.198 78.128,87.413 79.097,87.695 79.86,88.241 81.075,87.79 82.291,87.34 83.282,84.755 83.777,83.945 84.272,83.134 81.435,80.619 81.435,80.619 81.435,79.043 88.01,77.816 88.01,67.009 88.01,46.385 64.774,46.666 64.774,46.666 64.774,46.666 41.538,46.368 41.538,66.992 41.538,77.8 48.113,79.001 48.113,80.578 48.113,80.578 45.276,83.048 45.771,83.858 46.266,84.669 47.257,87.164 48.473,87.615 49.689,88.065 50.452,87.344 51.42,87.062 52.156,86.847 54.056,87.571 54.056,87.571 54.056,87.571 51.671,92 51.806,98 L 66.396,98 C 66.396,98 65.63,92.506 69.165,92.506 72.7,92.506 71.8,98 71.8,98 Z M 67.567,72.774 L 71.665,79.123 C 71.665,79.123 79.164,77.381 79.816,71.625 80.221,68.045 77.919,68.22 76.078,68.36 73.715,68.541 68.603,70.793 67.567,72.774 Z M 54.059,68.361 C 52.218,68.221 49.916,68.046 50.321,71.626 50.973,77.382 58.472,79.124 58.472,79.124 L 62.57,72.775 C 61.534,70.793 56.422,68.541 54.059,68.361 Z M 65.281,77.363 C 65.281,77.363 61.982,78 61.802,87 L 65.314,87 68.691,87 C 68.512,78 65.281,77.363 65.281,77.363 Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_msg" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="F 1 M 104.876,54.812 C 111.758,53.051 116.559,48.41 118.8,42.969 120.41,39.059 123.121,32.086 112.238,19.762 101.45,7.545 82.95,22.802 82.95,22.802 73.027,17.841 69.097,18.421 62.944,21.041 54.302,24.723 49.66,36.726 49.66,36.726 46.939,36.566 39.897,35.606 32.855,41.207 25.813,46.808 24.586,61.693 24.586,61.693 21.865,61.533 11.641,62.832 8.688,74.177 5.648,85.86 9.466,94.123 20.052,100.905 30.295,107.467 42.458,97.384 45.179,95.623 47.9,93.862 49.02,94.983 49.02,94.983 49.02,94.983 50.781,96.744 51.901,98.664 51.901,98.664 53.341,100.425 51.421,102.345 49.088,104.678 47.9,105.386 47.9,105.386 46.505,106.888 45.979,108.587 46.46,110.347 46.94,112.108 48.381,114.188 56.703,112.108 61.539,110.899 77.509,104.106 92.713,94.663 107.917,85.22 116.88,70.496 118,67.775 119.12,65.054 116.88,63.134 115.439,62.493 113.999,61.853 112.558,62.653 109.997,64.254 107.436,65.854 105.356,64.254 105.356,64.254 105.356,64.254 102.315,61.213 101.355,59.293 100.136,56.855 101.894,55.575 104.876,54.812 Z M 68.439,28.832 C 75.687,26.879 83.35,31.926 85.554,40.106 87.758,48.286 83.669,56.5 76.421,58.453 69.173,60.406 61.51,55.359 59.306,47.179 57.102,38.999 61.191,30.785 68.439,28.832 Z M 33.443,94.32 C 27.603,98.066 19.605,96.017 15.579,89.741 11.553,83.466 13.411,73.762 19.252,70.016 25.092,66.269 32.703,69.899 36.729,76.174 40.754,82.449 39.283,90.573 33.443,94.32 Z M 35.49,63.686 C 32.26,56.416 34.553,48.341 40.611,45.649 46.669,42.957 54.199,46.669 57.429,53.939 60.659,61.209 58.366,69.284 52.308,71.976 46.25,74.668 38.72,70.956 35.49,63.686 Z M 93.833,78.018 C 86.664,87.576 81.83,89.381 78.629,90.982 75.428,92.582 63.425,96.584 56.863,91.462 50.301,86.341 50.621,80.899 50.621,80.899 50.621,80.899 63.265,74.817 66.786,64.734 66.786,64.734 79.43,68.255 89.993,58.332 89.992,58.333 100.075,69.696 93.833,78.018 Z M 89.888,36.624 C 88.707,29.331 93.025,22.565 99.531,21.511 106.037,20.458 112.269,25.516 113.45,32.809 114.631,40.102 110.313,46.868 103.807,47.922 97.301,48.975 91.069,43.917 89.888,36.624 Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72"/>
        </Canvas>
        <Canvas x:Key="set_ungoro" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="F 1 M 90.6,9.712 C 90.6,9.712 91.449,37.391 78.714,49.786 65.979,62.181 63.468,67.304 63.092,69.314 62.583,72.031 65.809,76.786 70.224,76.616 74.639,76.446 81.771,67.786 86.695,66.597 91.619,65.408 109.21,67.406 118.449,63.71 119.298,63.37 104.864,90.539 84.657,95.804 78.899,97.304 74.555,98.987 70.733,100.898 60.884,105.822 53.244,104.747 43.394,108.03 39.828,109.219 33.375,126.199 19.451,119.407 3.66,111.704 10.961,100.558 12.829,94.785 14.697,89.012 12.829,88.842 11.81,83.917 10.791,78.992 13.848,67.955 12.659,62.012 11.47,56.069 9.263,49.786 11.301,39.597 13.339,29.408 20.64,9.03201 24.036,6.99501 24.036,6.99501 25.225,13.957 27.602,19.391 29.979,24.825 33.715,29.07 33.885,31.957 34.055,34.844 30.748,52.802 33.885,56.919 36.602,60.485 39.998,59.636 42.885,56.749 45.772,53.862 51.375,44.183 54.941,35.863 58.17,28.328 75.148,11.07 90.6,9.71201 Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_icecrown" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="m99.016 83.249c0.129-1.68-3.101-3.876-4.135-5.814s3.359-15.118 3.359-15.118c-0.775-0.258-7.494-2.713-7.753-8.011-0.258-5.298 6.073-6.719 6.073-6.719-10.595-4.522-19.123-27.909-19.123-27.909 2.067 8.786 3.23 22.224-0.129 30.106-3.36 7.882-6.848 2.197-7.494 1.421s-1.292-6.073 0.258-9.691c1.551-3.618 2.584-4.005 3.876-13.696s-5.685-19.898-5.685-19.898c3.231 21.19-4.263 19.64-4.263 19.64s-7.494 1.551-4.264-19.64c0 0-6.977 10.208-5.685 19.898 1.292 9.691 2.326 10.078 3.876 13.696 1.551 3.618 0.904 8.916 0.258 9.691s-4.135 6.461-7.494-1.421-2.197-21.32-0.129-30.106c0 0-8.528 23.387-19.123 27.909 0 0 6.331 1.421 6.073 6.719s-6.977 7.753-7.753 8.011c0 0 4.393 13.179 3.359 15.118-1.034 1.938-4.264 4.135-4.135 5.814 0.129 1.68 6.461 22.87 7.623 24.421l-5.039 4.522s11.241 8.14 13.438 7.882 12.017-11.629 12.792-13.438 0.129-2.843-1.163-4.264-4.393-5.298-4.393-5.298 7.624-9.819 11.759-10.078c4.135 0.258 11.758 10.078 11.758 10.078s-3.101 3.876-4.393 5.298c-1.292 1.421-1.938 2.455-1.163 4.264s10.595 13.179 12.792 13.438c2.197 0.258 13.438-7.882 13.438-7.882l-5.039-4.522c1.162-1.55 7.494-22.741 7.623-24.421zm-44.448 1.809c-2.132 0.646-7.494 2.843-10.983 0s-5.556-13.179-5.298-16.151 0.646-2.455 1.551-1.421l15.634 14.472c0-1e-3 2.205 2.158-0.904 3.1zm29.847 0c-3.489 2.843-8.851 0.646-10.983 0-3.109-0.942-0.904-3.101-0.904-3.101l15.634-14.472c0.904-1.034 1.292-1.551 1.551 1.421 0.258 2.973-1.809 13.31-5.298 16.152z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_loot" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="m94.966 105.92c-7.667-1.833-7.5-9.333-7.5-9.333s0.655-38.687 2.5-43.167c1.167-2.833 2.5-6.333-0.833-6.667-1.891-0.189-5 1-7.667 2.333s-8.833 2.5-8.833 2.5c-0.833 2.167-2.833 13.333 0 22s-2 11.5-5.333 11c-4.121-0.618-4.5-7.333-2.833-11.5 2.237-5.592-0.167-20.833-0.167-20.833-5.5 0.667-17.667-3.333-23.167-5.333s-6.773 2.193-7 3.667c-0.667 4.333 4.167 10.83 4.167 14.5 0 2.833-3.167 3.333-4.5 8.167-1.392 5.045 5.667 3.667 5.833 7 0.305 6.105-0.167 13.114-0.167 15.667 0 4-5.833 8.833-7.667 10-1.833 1.167-3.159 1.173-1 3 3.623 3.066 12.283 3.778 15.312 3.708 0.73-0.017 1.464 0.344 0.5 1.5-0.625 0.75-1.268 1.129-0.75 2.313 1.167 2.667 25.271 1.813 26.438 1.813 0 0 8.651-0.767 6.313-3.625-0.563-0.688-0.875-2.25 1.5-2.125 2.438 0.188 11.585-1.7 14.021-2.417 2.833-0.835 7.359-2.608 0.833-4.168z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
            <Path Data="m58.132 47.425c-1.521-2.667-1.646-5.792-0.896-8.292 1.912-6.374 6.313-8.562 6.313-8.562-0.417 4.521 2.417 5.187 3.583 7.687 1.84 3.943-0.333 9.667-0.333 9.667 0.667-0.333 15-5.333 8.167-21s-20.5-18-19.167-17.167 4 2.5 3.833 7.333-9.833 10.667-11.667 17.5c-1.833 6.834 10.167 12.834 10.167 12.834z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_gilneas" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="m25.345 3.1348-3.9104 2.0919-1.5462-0.09095-1.0609-1.2123-1.3338 1.2728s-7.5169 0.78162-7.8507 1.5766c-0.05511 0.58068-0.31098 0.96788-0.7028 1.1446-0.45003 0.20301-1.0686 0.087056-1.7074-0.3793 0 0-3.4858 1.5155-4.0163 3.2055 6.4264 5.6382 5.0317 13.065 5.0317 13.065s-2.213-1.7886-5.3656-2.6071c4.1832 3.5163 3.9109 5.9114 3.729 6.8208 0.18188 0.60626 2.1821 1.3942 5.0012 2.5461 0.21219-1.6672 1.4248-2.2733 2.4252-3.0918-0.42438 1.0003-0.2727 0.84862-0.42426 3.0009 2.8191 0.6972 5.6382-0.18199 7.0326-0.9095 0.09094-0.93971-1.7582-2.819-3.3042-4.2437 0.75783-0.21219 1.7885 0.18219 2.9404 1.1219 0.06063-1.3944 0.81854-1.9402 2.0614-3.8499-0.36376 2.0613 0.42419 3.759 1.2123 5.4565 0.33344-0.03032 0.8188 0.0302 2.8195-3.668-4.1529-4.6379-4.0013-9.2155-4.0013-9.2155l8.6088-5.2741s-0.21222-0.27332-3.0313 0.090434c1.0913-3.6376-0.27285-3.9104-0.27285-3.9104-2.031 6.4264-2.4555 5.396-5.2746 6.2751-0.6972-3.5163 2.9404-9.2155 2.9404-9.2155zm-8.2992 3.9812c1.4321 5.672e-4 2.9694 0.12342 4.6013 0.41444-0.30618 2.2107-1.896 3.0037-4.1072 4.0318-1.6513 0.76774-5.1563 0.48731-5.1563 0.48731l0.50281 3.1807s-2.6672-2.1825-3.0613-3.9104c-2.3424 0.20179-3.2739 0.42431-5.1837-0.63665 0 0 4.6713-3.5703 12.404-3.5672zm-10.673 11.355c-0.085739 1.0289 0.085451 1.1574-0.55759 1.9291 0 0 1.3292 1.0291 1.5436 1.372 0.21435-1.1146 0.12861-2.7009-0.98599-3.3011z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_boomsday" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="m31.835 4.2163s-3.6972 2.9032-4.5408 2.8536c-0.79829-0.046958-1.1839-0.763-1.8738-2.1616 0.42559 1.0267 0.06253 2.9311 0.06253 2.9311l2.2831 1.3146c0.45355 0.38535-0.1988 1.1416-0.67024 0.9679 0 0-1.2405-0.74455-1.8609-1.0671-0.24813 0.64513-1.712 1.464-1.712 1.464s1.5631-0.22294 1.8113 0.447c0.24813 0.66995-1.3648 3.5481-1.3648 3.5481s2.1339-2.5808 3.2008-2.4815c1.067 0.09925 3.3001 3.052 3.3001 3.052s-1.3896-3.722-1.3648-4.64c0.02481-0.91807 3.2758-1.34 3.2758-1.34s-3.5486 0.074739-3.5982-0.91777c-0.04963-0.99251 3.052-3.9703 3.052-3.9703zm-6.4146 0.69195c-0.03257-0.078558-0.06878-0.15312-0.1111-0.22014 0.04134 0.084019 0.07192 0.1407 0.1111 0.22014zm-1.6066 2.8102c-1.8151-0.036607-3.0444 0.92267-3.5786 1.4009-0.68476-0.50253-1.2656-0.63045-1.2656-0.63045l-1.7896 1.5436 1.3333 0.94775-0.31729 0.41031c-0.92158-0.73614-1.9634-1.3076-3.0794-1.6893-0.15715 0.24949-0.43119 0.40105-0.72605 0.40153-0.45001-1.6e-5 -0.82386-0.34705-0.85731-0.79582-0.5571-0.096926-1.1213-0.14757-1.6867-0.15141-4.6884 0.0044371-8.7757 3.1905-9.9245 7.736 0.19112 0.11539 0.30921 0.33008 0.30954 0.56276-4.577e-4 0.32599-0.22954 0.60107-0.53537 0.64286-0.059804 0.43245-0.091909 0.86828-0.096118 1.3048 0.00329 5.3234 4.0824 9.7575 9.387 10.204-0.0029-0.0092-0.0054-0.01847-0.0072-0.02791-0.04878-0.27804 0.36204-0.61322 0.91777-0.74879 0.5559-0.13562 1.0464-0.02016 1.0955 0.25787 0.02905 0.16588-0.10701 0.3609-0.3638 0.52141 1.0805-0.08083 2.1413-0.33252 3.143-0.74569-0.09144-0.31113 0.01669-0.70933 0.27544-1.0144 0.3597-0.42391 0.89113-0.53519 1.187-0.24856 0.09953 0.09715 0.16308 0.23228 0.18345 0.39016 2.9117-1.8866 4.6709-5.1186 4.6747-8.5881-0.0024-0.72181-0.08102-1.4414-0.23461-2.1466-0.02688 0.0038-0.05398 0.0059-0.08113 0.0062-0.34893 2.05e-4 -0.63192-0.28256-0.632-0.63149 1.93e-4 -0.24762 0.14505-0.47228 0.37052-0.57464-0.32736-0.94115-0.79026-1.8295-1.3741-2.637 0.23671-0.30125 0.72754-0.89497 1.881-2.1926-0.23488-0.45168-0.55173-0.85109-0.89194-1.1968 1.67-1.4828 3.8396-1.1539 3.8396-1.1539s-0.03531 0.21056 0.21032-1.0878c-0.50004-0.061409-0.95415-0.060406-1.3668-0.068729zm-12.704 2.8055 0.06976 1.6495s-1.1907 0.09334-2.5745 0.72347l-0.83199-1.4697c0.96268-0.43592 2.0722-0.74987 3.3367-0.9033zm-4.1346 1.3121 0.91674 1.4289c-2.3775 1.3809-4.8257 4.4807-2.6774 11.401-3.3639-4.1554-2.9217-10.131 1.7606-12.83z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_troll" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="m20.454 4.2526c-2.7317 0.03395-4.6488 0.74517-4.6488 0.74517l-0.9431 4.1682 0.69505 0.89349-0.24805 1.34-1.2904-1.588s-0.3089-1.2755-0.58963-4.2933c-3.7196 0.77199-6.3867 2.1053-6.3867 2.1053 1.7826 2.3033 1.7158 10.574 1.418 12.361 0.74438 0.44663 3.5238 2.4319 3.5238 2.4319s0.34714-0.14884 0.74414 3.0272c4.0693 1.588 6.8978-0.34727 6.8978-0.34727s-0.19844-2.1341-0.19844-2.5807 1.4887-1.1913 4.8633-2.2831c-1.3399-5.0618-0.27283-9.4534 1.2408-15.185-1.5973-0.55053-3.146-0.76188-4.5207-0.7922-0.18902-0.00417-0.37496-0.00485-0.55707-0.00259zm0.31368 10.918 1.2656 1.5632s1.315 2.3576-1.3648 3.5982c-2.467 1.1421-3.4737-0.54622-3.4737-0.54622l-0.9927-1.7368zm-9.5777 1.4392 3.1264 3.0272s-0.54583 1.1415-1.7368 1.1911c-1.464 0.07444-1.5632-2.0345-1.5632-2.0345zm19.18 1.5632c0.1985 1.8858-0.76899 4.094-2.2081 4.9377-1.4391 0.84364-2.1773-0.72065-3.176-0.94258-0.89326-0.1985-3.8215 1.5384-3.8215 1.5384-0.64513 2.5805 1.861 4.7639 4.6152 5.1609 2.4386 0.35152 4.6648-0.64504 6.4513-3.4241 1.7865-2.779-0.39691-6.3275-1.8609-7.2704zm-15.111 2.2086 1.712 1.6376s0.62282 0.62494-0.19844 0.76894c-0.44663 0.04963-1.1559 0.20103-1.6872 0.0248-0.60873-0.2019-0.28166-0.67007-0.19895-1.1911zm-13.449 0.14883c-2.6302 6.0047 1.4392 8.9322 4.4664 9.0811 3.0272 0.14888 4.9109-4.4648 4.516-5.6074-0.42309-1.2242-2.5807-2.2335-2.5807-2.2335-1.588 2.5805-2.5308 2.3575-3.4489 2.3823-0.79401-0.07444-2.0595-0.2976-2.9528-3.6225z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_dalaran" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="m25.079 4.5818s-7.5537 7.0638-10.657 9.3504c-3.1032 2.2866-7.1862 3.7157-10.289 3.7157-0.12249 2.2049-2.4091 5.839-2.4091 5.839 1.8374-1.6741 5.2264-2.695 9.3504-4.8182 1.9599 2.0824 4.9931 2.9981 7.6356 1.7559 3.3998-1.5982 3.5928-6.043 3.062-7.717 0.89829-0.7758 2.7961-1.6358 2.7961-1.6358s0.28098-0.13808 0.79736 2.6566c0.16332 5.6347-3.4708 9.636-8.0031 10.33-4.5323 0.69413-8.6564-2.6131-8.6564-2.6131s-0.81652 0.24486-1.9598 0.8165c1.7558 2.8582 8.5339 3.7567 8.5339 3.7567l2.5724 3.2663s2.0008-2.3683 2.9807-4.1649c1.1433-0.3879 3.9236-1.4079 6.1248-4.8586 2.0902-3.2768 1.5514-10.494 1.5514-10.494s0.65317-0.32658 3.6339-1.3065c-2.4499-2.6949-7.0638-3.8792-7.0638-3.8792zm-6.043 9.7179s1.3167 2.0149-0.44897 2.8992c-1.539 0.77077-2.4502-1.1433-2.4502-1.1433z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_uldum" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="m12.159 4.1712-1.2435 0.14701 0.03267 1.2693-1.2609-0.080617-6.6011 6.3882-0.089574 1.2345-1.2382 0.05058-0.070605 1.2424 3.3643 3.4765 1.587 0.24712 0.032668-1.2451 1.1718 0.07904 6.7881-6.5695-0.09958-1.2851 1.3157 0.10854-0.15017-1.4063zm5.4904 0.63756 5.5178 3.662 0.11697 1.5733 3.5461 3.1804 1.3436-0.26925 4.005 5.0673c-2.1056-13.321-14.529-13.214-14.529-13.214zm5.9967 6.4446-0.6971 0.96951-5.6874 5.4493 2.5713 2.3063 4.8438-6.2075 0.86413-0.81828zm-10.735 1.792-2.0117 1.9469 3.5376 3.7389 1.2319-1.2646s0.41229-0.50269 0.85728-0.24343c0.47675-0.52329 0.2898-0.63861 0.2898-0.63861zm3.1794 5.2601s-5.985 6.3084-7.0305 7.4072c-1.1838 1.1582-0.63598 1.6497-0.63598 1.6497s1.8997 1.6934 2.3305 2.0902c0.43083 0.39682 0.9704 0.3182 1.5317-0.31614 0.44517-0.50308 6.873-8.0785 6.873-8.0785zm4.4845 2.1224c-0.12017 0.0071-0.25573 0.05172-0.39887 0.15333 0.29235 0.47757 0.17752 0.60383 0.07429 0.75874l-1.802 2.4148s2.8486 3.3631 3.9081 4.4244c0.96173 1.0253 1.5844 0.47211 1.5844 0.47211s1.2012-1.1548 2.0966-2.0244c0.64907-0.6304-0.59277-1.6724-0.59277-1.6724l-4.4292-4.3375c-0.08219-0.10821-0.24021-0.20098-0.44049-0.18916z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_dragons" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="m28.607 4.1073c-13.575-0.056292-16.06 8.3971-22.936 12.347-2.493 0.73236-1.7093-4.9556-1.7093-4.9556-3.4177 4.6383-2.9783 7.4211 0 9.9111 0.83001-2.0994 1.8554-1.6598 2.9783-0.63451 1.123 1.0253 1.7091 2.9292-0.78093 5.2728-2.49 2.3436-2.8807 3.7109-2.8807 3.7109s2.8807-1.5626 6.0542-0.73263c-0.43942-1.7088-0.14677-6.0541 4.0033-6.5423 4.1501-0.48824 12.206 5.0867 12.206 5.0867s-6.4503-7.8478-13.553-9.6991c6.6183-0.04464 9.8918 4.0145 9.8918 4.0145-0.12206-0.75678-0.96725-1.9674-0.29285-3.2102 0.93873-1.7299 5.2243-1.758 8.9838-2.2462-1.6112-1.123-10.156-2.4902-14.257-2.6855 2.2459-1.3183 9.423-0.29285 9.423-0.29285s-0.82969-1.2694-0.97616-3.1735c-0.14647-1.9041 2.514-2.6652 3.5152-2.7343 0 0-0.49755-0.26935-2.2243-0.61112 0.91771-1.0526 3.4483-2.418 6.2546-2.6204-1.3191-0.13492-2.5494-0.20063-3.6998-0.2054zm-8.0228 3.3001c0.33877-0.00935 0.3198 0.8694 0.3198 0.8694l-1.7332 1.1963s-0.83008-0.75703-1.3427-0.78144c-0.51265-0.024412-0.31725-0.39047-0.31725-0.39047s2.3924-0.48848 2.9051-0.83025c0.06408-0.042721 0.11989-0.062217 0.16829-0.063552zm-3.293 2.6514s0.31721 0.14636 0.43927 0.34166c0.12206 0.19529 0.6594-0.14655 0.70823 0.24404 0.04882 0.39059-0.36645 0.39095-0.73263 0.43978-0.36618 0.04882-0.73204-0.12241-1.0006-0.48859-0.26853-0.36618 0.5857-0.53689 0.5857-0.53689z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_dhi" Clip="M0,0 V98 H68 V0 H0 Z">
            <Path Data="F1 M68,98z M0,0z M32.0299,47.5L62.0299,26.5C62.0299,26.5 57.2736,22.7298 55.5299,18.5 52.9953,12.3515 55.5299,0.5 55.5299,0.5 55.5299,0.5 52.6856,0.765488 51.0508,1.5 49.2693,2.30041 47.0493,4.5 47.0493,4.5L45.5493,18.5 21.0493,46 28.5493,54C28.5493,54 38.1684,54.7752 42.5299,58.5 50.1411,65 49.0493,70.5 49.0493,70.5 49.0493,70.5 47.7925,75.8445 44.5479,78.5 41.2481,81.2006 33.5493,83 33.5493,83 33.5493,83 26.4273,81.8138 23.5479,79 21.174,76.6801 19.7342,74.7165 20.0493,71.5 20.3504,68.4273 22.0508,65 24.5493,65 36.1482,65 34.5493,76.5 34.5493,76.5L39.0493,76.5C39.0493,76.5 40.155,73.3411 40.0508,70.5 39.8472,64.9485 37.2186,61.672 32.0299,58.5 26.2046,54.9388 20.2142,53.5172 13.048,55.5 7.54765,57.0219 -2.44977,68.3768 0.549816,80 5.32435,98.501 42.5299,90.5 42.5299,90.5L55.5299,97.5C55.5299,97.5 59.9427,92.6202 62.0299,89 64.4387,84.822 66.5299,77.5 66.5299,77.5L57.0299,86 53.5299,83.5C53.5299,83.5 72.6118,62.5536 65.5299,51.5 58.6628,40.7816 34.0299,50 34.0299,50L32.0299,47.5z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
        </Canvas>
        <Canvas x:Key="set_blacktemple" Clip="M0,0 V212 H200 V0 H0 Z">
            <Path Data="F0 M200,212z M0,0z M29.2423,3.28736C29.2423,1.52217,30.7589,0.1385,32.5167,0.29993L75.5167,4.24891C77.0607,4.39071,78.2423,5.68579,78.2423,7.23634L78.2423,9.92242C78.2423,11.4045 79.3214,12.6622 80.7898,12.8628 84.9348,13.4292 93.2754,14.4617 99.2423,14.4992 105.719,14.54 114.813,13.4429 119.191,12.8567 120.662,12.6597 121.742,11.401 121.742,9.91636L121.742,7.23113C121.742,5.68276,122.921,4.38875,124.462,4.24423L166.462,0.306726C168.222,0.14175,169.742,1.52617,169.742,3.29363L169.742,3.99922C169.742,5.65608,171.085,6.99922,172.742,6.99922L182.273,6.99922C183.708,6.99922,184.941,8.01448,185.217,9.42198L189.303,30.2594C189.563,31.5829,188.907,32.9168,187.701,33.52L178.134,38.3035C177.261,38.7397,176.655,39.5729,176.509,40.5374L174.383,54.574C174.291,55.1765,174.019,55.7369,173.601,56.1807L167.046,63.1453C166.529,63.6944 166.238,64.4167 166.234,65.1706 166.201,70.4432 166.141,92.3531 167.242,106.999 168.346,121.682 171.69,143.352 172.543,148.748 172.67,149.553 172.466,150.368 171.977,151.02L168.104,156.184C166.22,158.696 169.105,162.337 172.057,161.267 175.576,159.992 179.3,158.863 182.242,158.499 184.297,158.246 186.28,158.261 187.691,158.331 188.93,158.393 189.934,159.295 190.225,160.501L198.921,196.589C199.352,198.38 198.092,200.14 196.259,200.33 190.32,200.945 178.069,202.44 169.742,204.999 164.586,206.584 157.59,209.605 154.219,211.106 153.034,211.634 151.636,211.375 150.62,210.568 146.011,206.909 131.063,197.499 99.2423,197.499 65.5635,197.499 53.6506,205.805 49.8484,209.844 48.7952,210.963 47.1364,211.637 45.6724,211.17 42.5449,210.174 37.1932,208.51 33.2423,207.499 23.4522,204.994 9.18073,203.149 2.68811,202.386 0.871487,202.172 -0.355626,200.409 0.0927959,198.636L9.93649,159.709C10.1323,158.934 10.6319,158.266 11.3989,158.043 12.763,157.648 15.2898,157.307 18.7423,158.499 20.0352,158.946 21.8069,159.612 23.7519,160.367 26.8124,161.553 29.4004,157.657 27.0792,155.336L26.8064,155.063C26.1368,154.394 25.8203,153.463 25.9538,152.525 26.7998,146.585 29.9568,123.972 31.2423,108.499 32.5677,92.5472 33.0949,68.7563 33.2146,62.5331 33.2333,61.5608 32.7763,60.6575 31.992,60.0823L26.6257,56.147C26.0552,55.7287,25.6509,55.1222,25.4842,54.4346L22.1458,40.6638C21.8958,39.6322,21.1192,38.8096,20.1038,38.5005L12.8263,36.2856C11.3575,35.8386,10.4632,34.3547,10.7541,32.8472L15.2731,9.43076C15.5455,8.01914,16.7811,6.99922,18.2187,6.99922L26.2423,6.99922C27.8992,6.99922,29.2423,5.65608,29.2423,3.99922L29.2423,3.28736z M125.552,54.3924C124.733,53.8336,124.242,52.9057,124.242,51.9137L124.242,51.3737C124.242,49.9822 123.292,48.7769 121.931,48.4864 117.306,47.4987 106.715,45.4349 99.2423,45.4992 92.45,45.5577 82.8706,47.5065 78.5341,48.4703 77.1852,48.7701 76.2423,49.971 76.2423,51.3528L76.2423,51.8007C76.2423,52.8544,75.6894,53.831,74.7858,54.3731L65.0652,60.2055C64.235,60.7036 63.6972,61.5602 63.6219,62.5255 63.1809,68.1832 61.6581,88.3349 61.2423,101.999 60.8312,115.511 61.1142,135.074 61.2124,140.841 61.23,141.873 61.7798,142.822 62.6653,143.353L75.7901,151.228C76.0893,151.407 76.3503,151.633 76.5761,151.899 78.2358,153.854 85.797,162.411 92.7423,164.499 98.9132,166.354 103.054,166.297 109.242,164.499 117.268,162.168 117.571,155.446 124.242,151.499 127.975,149.291 133.688,147.203 136.708,146.171 137.922,145.756 138.764,144.651 138.792,143.367 138.934,136.888 139.295,117.044 138.742,103.499 138.156,89.1344 135.974,67.8978 135.388,62.3596 135.296,61.4871 134.825,60.7146 134.1,60.2204L125.552,54.3924z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
            <Path Data="F1 M200,212z M0,0z M76.7509,71.3007C74.603,71.5084 73.0236,69.628 74.6241,68.1806 76.6255,66.3705 80.2497,64.7501 86.7423,65.4992 92.1378,66.1218 98.2795,70.9912 100.921,73.2966 101.752,74.0223 102.909,74.3067 103.885,73.7906 104.971,73.2157 106.278,72.2884 106.742,70.9992 107.622,68.5581 106.225,65.1787 104.559,62.3516 103.253,60.135 104.751,57.5153 107.076,58.6176 109.925,59.969 113.119,62.4488 115.242,66.9992 117.74,72.3519 117.691,78.4687 117.459,81.5325 117.368,82.7307 117.901,83.9411 119.044,84.3118 120.067,84.6436 121.408,84.8718 122.742,84.4992 124.22,84.0867 125.292,82.5857 126.054,80.8674 127.1,78.512 129.965,77.9595 130.222,80.5237 130.284,81.1452 130.297,81.8053 130.242,82.4992 129.702,89.3484 121.499,98.8887 118.408,102.26 117.694,103.039 117.4,104.128 117.779,105.115 118.339,106.574 119.334,108.622 120.742,109.499 121.91,110.226 123.371,110.153 124.844,109.696 127.301,108.933 130.242,110.909 128.742,112.999L128.742,112.999C125.594,117.388 118.059,121.561 114.795,123.232 113.838,123.721 113.183,124.659 113.184,125.733 113.185,127.99 113.468,131.894 115.242,133.999L115.242,133.999C117.162,136.277 117.028,140.241 114.058,140.02 112.329,139.892 110.656,139.584 109.242,138.999 105.408,137.412 102.139,132.505 100.184,128.948 99.1657,127.094 96.6178,126.62 95.3099,128.283 94.2868,129.584 93.2644,131.1 92.7423,132.499 91.7415,135.181 92.3052,138.802 93.1485,141.814 93.8182,144.206 91.9424,146.039 90.124,144.347 88.6045,142.932 86.9017,140.902 85.2423,137.999 82.5406,133.273 82.3472,125.131 82.5068,120.338 82.5719,118.384 80.8635,116.796 78.9917,117.359 77.1473,117.914 75.046,118.766 73.7423,119.999 73.3729,120.349 73.0329,120.77 72.7209,121.238 71.106,123.664 68.2601,123.776 68.4049,120.866 68.5162,118.63 68.8992,116 69.7423,112.999 71.1302,108.059 75.7624,102.034 77.5398,99.8451 77.9891,99.2917 78.25,98.6068 78.188,97.8967 78.0613,96.4459 77.6403,93.9671 76.2423,92.9992 74.7683,91.9787 72.9616,93.1598 71.4383,94.7361 69.8119,96.4191 67.805,96.0048 68.4684,93.7603 68.7477,92.815 69.1563,91.7308 69.7423,90.4992 72.4106,84.891 81.6231,80.9024 85.956,79.291 87.2815,78.7981 88.1616,77.4761 87.6691,76.1505 87.1374,74.7196 86.1298,72.9829 84.2423,71.9992 82.3937,71.0358 79.3231,71.0519 76.7509,71.3007z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
        </Canvas>
        <Canvas x:Key="set_scholomance" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="m13.35 6.3913c-2.6523-0.045075-9.249 4.3084-10.445 4.3041-1.1964-0.0043-1.5813 0.02842-1.5813 0.02842l0.019637 1.3803 2.9905 3.39-0.5519-0.12868-2.3776-2.2784 0.05116 0.90796 5.1599 6.734s3.1871-0.65375 4.1047-1.1317c0.91758-0.47796 2.6838-1.6679 3.5088-1.7038 0.82499-0.03583 1.5402 0.3027 1.5803 0.90744 0.68498 0.01837 1.1141 0.01912 1.1141 0.01912s0.42916-7.4e-4 1.1141-0.01912c0.04008-0.60474 0.75527-0.94327 1.5803-0.90744s2.5913 1.2258 3.5088 1.7038c0.91758 0.47796 4.1047 1.1317 4.1047 1.1317l5.1599-6.734 0.09663-1.5141-1.5038 1.5932-0.5519 0.12867 2.1006-2.6701-0.0098-0.80874s-0.38491-0.03272-1.5813-0.02842-7.793-4.3492-10.445-4.3041-3.253 1.5208-3.253 1.5208 0.01824 7.7837 0.01964 8.4041c0.0014 0.6204-0.33951 0.56327-0.33951 0.56327s-0.34093 0.05713-0.33951-0.56327c0.0014-0.6204 0.01964-8.4041 0.01964-8.4041s-0.60069-1.4758-3.253-1.5208zm19.665 8.0341-5.5562 7.2244-7.3386-1.7343 0.03772 1.3426 7.3262 1.6118 5.7025-6.9722zm-32.164 0.0041-0.17157 1.4717 5.703 6.9727 7.3257-1.6123 0.03772-1.3426-7.3386 1.7343zm16.072 5.0581-2.3027 0.05581-0.6258 7.7758 2.9285-2.2758 2.9285 2.2758-0.6258-7.7758z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_darkmoon" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Data="m17.833 1.897s-2.6504 1.691-3.4416 2.5895c-0.7913 0.8985-1.3756 6.6104-1.3756 6.6104l0.07752-0.0098a6.6444 6.8801 0 0 0-2.2707 2.5234c-3.8107 1.0463-5.501 3.7238-5.501 3.7238l-2.9667-0.64492 1.4981 2.7073s1.4792 0.77739 3.2747 1.249c1.3141-0.30619 3.8186 0.47111 4.4566 0.68006a6.6444 6.8801 0 0 0 2.0831 1.7048c0.0058 0.03493 0.01017 0.06815 0.01809 0.10594 0.74488 3.5528 3.1838 5.3175 3.1838 5.3175l0.05994 3.5155 2.991-3.0696 0.55449-2.912-1.0924-2.82a6.6444 6.8801 0 0 0 3.2189-3.19l4.2483-1.3333 2.3105-1.6356 2.3513 0.38189s-1.2844-1.6693-2.159-2.5606c-0.84603-0.86212-6.5526-1.3453-6.9319-1.3767a6.6444 6.8801 0 0 0-2.5761-2.6045l0.07855 0.0062-1.4175-4.0096-1.3265-1.3813zm8.2744 3.9114-1.3508 2.6867s-2.3027-0.23616-4.2204 0.6997l0.31678 0.77721 2.6102 2.6148 1.5234 0.14831s1.9726-2.7608 1.8764-4.1558c-0.08249-1.1961-0.75551-2.7709-0.75551-2.7709zm-17.141 0.89969c-0.9112-0.00787-4.3336 0.73277-4.3336 0.73277l3.3972 1.8821 0.17777 2.3037 0.84026 1.4593 1.5198-1.0583s0.87129-1.2388 1.1684-1.5529c0.11195-0.87847 0.32143-2.3332 0.32143-2.3332s-2.3781-1.4273-3.0913-1.4335zm7.7794 4.5062a5.2844 5.7083 0 0 1 0.01705 0 5.2844 5.7083 0 0 1 5.2844 5.7082 5.2844 5.7083 0 0 1-5.2844 5.7082 5.2844 5.7083 0 0 1-5.2844-5.7082 5.2844 5.7083 0 0 1 5.2674-5.7082zm-0.0067 1.3689s-1.7025 4.1663-1.6392 5.148c0.0637 0.98809 1.8051 3.9011 1.8051 3.9011s1.3901-1.9136 1.4945-2.9011c0.10038-0.9494-0.67452-4.8765-1.6604-6.1479zm7.9577 8.1468-1.2475 0.40101s-2.0098 2.3185-2.8112 2.85c0.64035 1.3591 1.3366 2.0798 3.2329 2.8267 2.1732-0.07845 3.8695-0.10232 3.8695-0.10232l-2.4278-2.2717 0.03979-2.1689zm-16.166 0.9865-1.5043 2.9714 0.35502 3.2701s1.0236-1.5247 1.7048-2.2748c1.3515-0.13474 2.7449-0.46364 3.7677-1.4392-0.82826-0.70319-2.6153-2.3508-2.6153-2.3508z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" RenderTransform="1,0,0,1,72,72" />
        </Canvas>
        <Canvas x:Key="set_barrens" Clip="M0,0 V33.867 H33.867 V0 H0 Z">
            <Path Data="F1 M33.867,33.867z M0,0z M17.45,0.55049C16.78131,0.83707 13.3424,3.19349 13.0876,3.79849 12.2597,3.734805 8.7439,3.21209 8.7439,3.21209L8.94774,6.79119 5.06314,11.08989 8.27904,14.75199 8.40695,15.83489C8.41131,15.83599 7.56521,15.94914 6.62385,15.77122 5.97047,15.64774 5.25215,14.97489 4.36295,13.79712 4.20374,15.77132 6.71925,20.86582 7.99295,20.83392 9.10745,19.8468 9.48865,19.56732 10.15805,19.56012 11.62285,19.54421 13.08855,21.34362 13.31025,23.50902 13.47339,25.10272 13.25751,25.36902 12.45051,25.54682 11.77134,25.69643 10.76311,25.10118 10.76311,25.10118 10.76311,25.10118 12.73741,32.20218 14.67981,33.31668 14.67981,33.31668 16.65401,28.34918 16.46301,24.97388 16.27195,21.59858 14.99801,19.62398 13.66071,17.52238 12.32331,15.42078 12.13221,13.82878 12.25951,12.04558 12.38688,10.26238 14.18951,7.56518 16.99161,7.56518 19.79371,7.56518 22.54761,10.92078 22.06011,13.45488 21.17945,18.03258 18.62491,19.57318 17.96111,22.02308 17.36114,24.23768 17.70726,27.86438 19.39261,30.89618 22.10401,27.75438 22.89501,24.90958 22.86311,23.85878 22.16257,23.82694 20.61441,24.26424 20.53851,22.74438 20.4426,20.82358 22.04641,19.26828 23.69131,19.52848 25.17891,19.7638 25.31491,20.45162 25.63331,21.21588 27.06621,20.10138 29.88461,16.37578 29.47061,14.46528 28.53126,15.53198 27.44061,16.16858 26.20681,15.99378 25.42861,15.88353 25.11391,15.6445 24.99661,15.00671 24.80598,13.97031 25.88791,12.87321 27.54371,12.68211 27.6074,11.40841 27.08205,9.70510999999999 25.69691,8.41550999999999 25.53619,7.00500999999999 25.90882,6.26470999999999 26.14313,5.13540999999999L23.02243,5.29485999999999C23.02243,5.29485999999999,18.15043,0.709759999999994,17.44983,0.550459999999994z M16.97224,8.98849L13.66064,12.10919 17.09964,16.12109 20.22034,12.36379z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
        </Canvas>
        <Canvas x:Key="set_caverns" Clip="M0,0 V33.867 H33.867 V0 H0 Z">
            <Path Data="F1 M33.867,33.867z M0,0z M17.45,0.55049C16.78131,0.83707 13.3424,3.19349 13.0876,3.79849 12.2597,3.734805 8.7439,3.21209 8.7439,3.21209L8.94774,6.79119 5.06314,11.08989 8.27904,14.75199 8.40695,15.83489C8.41131,15.83599 7.56521,15.94914 6.62385,15.77122 5.97047,15.64774 5.25215,14.97489 4.36295,13.79712 4.20374,15.77132 6.71925,20.86582 7.99295,20.83392 9.10745,19.8468 9.48865,19.56732 10.15805,19.56012 11.62285,19.54421 13.08855,21.34362 13.31025,23.50902 13.47339,25.10272 13.25751,25.36902 12.45051,25.54682 11.77134,25.69643 10.76311,25.10118 10.76311,25.10118 10.76311,25.10118 12.73741,32.20218 14.67981,33.31668 14.67981,33.31668 16.65401,28.34918 16.46301,24.97388 16.27195,21.59858 14.99801,19.62398 13.66071,17.52238 12.32331,15.42078 12.13221,13.82878 12.25951,12.04558 12.38688,10.26238 14.18951,7.56518 16.99161,7.56518 19.79371,7.56518 22.54761,10.92078 22.06011,13.45488 21.17945,18.03258 18.62491,19.57318 17.96111,22.02308 17.36114,24.23768 17.70726,27.86438 19.39261,30.89618 22.10401,27.75438 22.89501,24.90958 22.86311,23.85878 22.16257,23.82694 20.61441,24.26424 20.53851,22.74438 20.4426,20.82358 22.04641,19.26828 23.69131,19.52848 25.17891,19.7638 25.31491,20.45162 25.63331,21.21588 27.06621,20.10138 29.88461,16.37578 29.47061,14.46528 28.53126,15.53198 27.44061,16.16858 26.20681,15.99378 25.42861,15.88353 25.11391,15.6445 24.99661,15.00671 24.80598,13.97031 25.88791,12.87321 27.54371,12.68211 27.6074,11.40841 27.08205,9.70510999999999 25.69691,8.41550999999999 25.53619,7.00500999999999 25.90882,6.26470999999999 26.14313,5.13540999999999L23.02243,5.29485999999999C23.02243,5.29485999999999,18.15043,0.709759999999994,17.44983,0.550459999999994z M16.97224,8.98849L13.66064,12.10919 17.09964,16.12109 20.22034,12.36379z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
        </Canvas>
        <Canvas x:Key="set_legacy" Clip="M0,0 V128 H128 V0 H0 Z">
            <Path Data="F1 M128,128z M0,0z M96.92,97.413L92.643,84.385C94.235,82.125,95.568,79.671,96.603,77.065L119.09,63.64 96.332,50.232C95.391,47.999,94.224,45.885,92.866,43.913L96.621,29.121 82.282,33.828C80.575,32.771,78.771,31.858,76.887,31.099L63.64,8.91 50.621,31.008C48.498,31.847,46.477,32.883,44.578,34.096L28.328,29.419 33.158,45.871C32.227,47.434,31.416,49.077,30.73,50.783L8.91,63.64 30.455,76.503C31.187,78.445,32.085,80.304,33.131,82.066L28.628,97.712 43.67,93.296C45.824,94.767,48.151,96,50.61,96.972L63.64,119.09 76.898,96.883C78.969,96.047,80.942,95.022,82.797,93.828L96.92,97.413z M47.728,82.261C47.431,81.857,46.998,80.395,46.998,80.395L46.186,78.691C46.186,78.691 45.456,77.474 46.998,77.717 48.54,77.96 54.139,80.151 57.222,80.233 60.306,80.314 61.929,78.691 62.497,78.529 63.065,78.367 66.392,77.068 67.366,76.906 68.34,76.744 74.345,73.07 75.237,64.236 76.13,55.402 68.177,50.209 63.633,50.128 63.633,50.128 60.793,50.047 60.144,50.453 59.495,50.859 55.275,52.806 53.409,53.05 53.409,53.05 51.867,54.511 52.111,56.296 52.354,58.081 52.354,64.226 58.846,64.237 58.846,64.237 60.396,63.4 60.23,57.333 60.23,57.333 68.31,56.661 68.52,64.237 68.73,71.813 61.28,72.862 56.663,72.653 52.046,72.444 42.286,66.04 43.021,55.862L43.59,52.237 45.213,50.371C45.213,50.371 48.946,42.824 57.466,40.552 57.466,40.552 58.196,39.335 59.089,39.335 59.982,39.335 60.712,40.309 62.01,40.471 63.308,40.633 63.795,39.254 65.012,39.335 66.229,39.416 66.31,40.309 67.609,40.552 68.907,40.795 75.237,41.526 80.349,48.504 80.349,48.504 80.592,50.289 81.323,50.614 82.053,50.939 83.757,51.669 84.001,52.237 84.244,52.805 85.949,58.891 85.786,61.488 85.623,64.085 84.893,76.176 76.86,79.908L76.698,82.099C76.698,82.099 70.693,87.455 61.523,86.806 52.354,86.156 49.885,85.191 47.728,82.261z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
        </Canvas>
        <Canvas x:Key="set_core" Clip="M0,0 V128 H128 V0 H0 Z">
            <Path Data="F1 M128,128z M0,0z M95.514,30.88L97.7501,30.34759 103.9261,39.29209 96.0464,40.25043z M110.456,46.607C110.456,46.607 111.5854,46.23053 112.1125,45.4776 112.6396,44.72467 115.0866,44.9129 116.0278,46.00466 115.04897,47.39756 112.2254,48.22586 110.456,46.60701z M108.5288,92.559C111.0318,92.559 112.7008,88.3873 112.7008,88.3873 112.7008,88.3873 124.9188,104.2593 94.7588,112.1703 69.3068,118.8463 42.1858,101.7393 42.1858,101.7393 42.1858,101.7393 45.3933,108.0633 48.4446,111.3353 51.1348,114.2203 56.3723,117.5953 56.3723,117.5953 56.3723,117.5953 55.8051,120.4843 56.3723,122.1843 56.7895,123.4353 94.0643,128.6473 108.9453,115.9263 117.2423,108.8323 119.7933,101.7403 119.7933,97.5673 119.7933,82.9633 106.8593,75.2363 99.7653,76.2873 90.5858,77.6471 95.1755,86.7193 95.1755,86.7193 95.1755,86.7193 95.9692,83.3919 97.679,82.9641 99.3688,82.5412 101.1659,85.0056 103.0149,87.5423 104.8189,90.0167 106.6729,92.56 108.5279,92.56z M121.0488,43.324C121.0488,43.324 120.5438,45.7788 118.1278,45.4102 117.7398,45.351 117.2178,45.0354 116.6358,44.6835 116.0558,44.3329 115.4158,43.9464 114.7898,43.7413 113.0698,43.1777 111.4518,43.324 111.4518,43.324 111.4518,43.324 108.0468,45.888 105.6108,47.0792 102.8328,48.4372 98.1001,49.5827 98.1001,49.5827 98.1001,49.5827 98.8419,52.187 99.7691,53.7552 100.7818,55.4673 103.1068,57.9277 103.1068,57.9277L112.7038,57.9277C112.7038,57.9277 112.3678,59.7721 111.8688,60.8484 111.3748,61.9146 110.1998,63.3519 110.1998,63.3519 110.1998,63.3519 117.5008,63.3519 121.0488,55.8414 124.0058,49.5827 121.0488,43.3244 121.0488,43.3244z M7.96879999999999,41.655L15.0621,39.986C15.0621,39.986 10.1009,44.6557 7.96889999999999,48.7482 4.57369999999999,55.2657 5.88259999999999,67.5242 5.88259999999999,67.5242L15.0621,52.9202C15.0621,52.9202 12.4718,65.1542 13.3931,72.9482 14.1919,79.7059 18.4,89.6382 18.4,89.6382 18.4,89.6382 18.6854,86.6353 19.6518,85.0486 20.6725,83.3729 23.407,81.7105 23.407,81.7105L22.5725,97.5655C22.5725,97.5655 28.9263,105.8257 34.2555,109.6655 38.1593,112.4785 45.1035,115.5075 45.1035,115.5075 45.1035,115.5075 38.0388,106.4865 35.9241,99.6515 34.0838,93.7035 34.2551,83.7965 34.2551,83.7965 34.2551,83.7965 37.1639,91.6036 40.931,95.0625 45.0139,98.8108 53.866,100.9041 53.866,100.9041L50.528,91.7245C50.528,91.7245 55.9141,99.7501 60.959,102.9905 66.0039,106.2309 89.332,105.4945 89.332,105.4945 89.332,105.4945 86.0642,103.7365 84.3251,102.1565 82.6111,100.5985 80.5698,97.5663 80.5698,97.5663L91.4178,99.6526C91.4178,99.6526 75.5628,91.7249 78.0658,76.7046 81.4566,56.3596 98.5108,56.2596 98.5108,56.2596L91.4175,47.0803C91.4175,47.0803 100.0247,48.1639 105.1865,45.4113 107.4605,44.199 110.6115,41.6559 110.6115,41.6559L114.3665,41.6559C114.3665,41.6559 112.5965,39.2661 111.8625,37.0663 111.1295,34.8666 110.6115,30.3902 110.6115,30.3902L108.5245,32.0592C108.5245,32.0592 105.2285,27.2556 102.6835,24.5488 100.1527,21.8577 95.5902,18.29 95.5902,18.29L95.5902,22.8798C95.5902,22.8798 90.2178,19.5433 86.4108,18.29 83.2539999999999,17.2508 78.0658,16.621 78.0658,16.621L80.5693,21.628C80.5693,21.628 72.5443,21.5954 67.6343,22.8798 63.1839,24.044 56.7863,27.4695 56.7863,27.4695L63.4622,28.7212C63.4622,28.7212 52.3342,32.2347 48.4412,37.9008 45.5744,42.0732 48.4412,64.1868 48.4412,64.1868L38.8445,48.7488 35.9238,65.8558C35.9238,65.8558 33.6581,57.9792 34.2548,52.9208 34.78,48.4685 38.01,42.0728 38.01,42.0728L45.1032,52.9208C45.1032,52.9208 42.1825,37.8998 51.7792,24.5478 62.8692,9.11780000000003 75.5622,5.77180000000003 75.5622,5.77180000000003 75.5622,5.77180000000003 56.7862,-1.81349999999997 33.0032,12.0305 19.9122,19.6505 7.96819999999996,41.6555 7.96819999999996,41.6555z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
        </Canvas>
        <Canvas x:Key="set_stormwind" Clip="M0,0 V80 H80 V0 H0 Z">
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M80,80z M0,0z M26,59.18C23.25,55.67,20.12,52.51,19.74,47.83L15.46,49.64A17.44,17.44,0,0,0,15.16,46.21A21.3,21.3,0,0,1,15.41,33.81A8.45,8.45,0,0,0,15.41,31.56C11.62,35.35 10.47,39.65 12.04,44.56 12.85,47.08 13.21,49.39 10.32,51.17A5.6,5.6,0,0,0,16.92,52.3L26.8,66.43C28.19,65.17,28.54,60.08,27.34,57.95z" />
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M80,80z M0,0z M14.83,26.23A11.76,11.76,0,0,1,17.33,22.8C18.8,21.63,20.72,21.03,22.25,19.92A7.22,7.22,0,0,0,24.74,16.92C24.92,16.45,23.6,15.4,22.96,14.61A3.31,3.31,0,0,1,22.73,14.16C25.43,11.63 28.98,11.39 32.44,10.53 31.05,9.77 29.55,8.8 28.22,9 24.54,9.54 21.59,11.67 19.05,14.32A1.74,1.74,0,0,0,18.91,15.94C19.12,16.33,19.83,16.5,20.36,16.62A1.44,1.44,0,0,0,21.1,16.54A79.68,79.68,0,0,0,14.43,22.11A8.32,8.32,0,0,0,12.3,30.58A50,50,0,0,0,14.83,26.23z" />
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M80,80z M0,0z M33,26C31.94,25.39 31.29,25.9 31,26.94 30.49,28.61 32.61,31.51 34.92,31.56A5.91,5.91,0,0,0,36.7,30C36.93,29.57,36.36,28.46,35.85,28A14.11,14.11,0,0,0,33,26z" />
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M80,80z M0,0z M58.29,28.85A7,7,0,0,1,57.5,27.46A5.37,5.37,0,0,1,59.5,27.46A12.31,12.31,0,0,1,61.76,29.1C62.45,26.74,62,24.97,60.45,23.59A7.77,7.77,0,0,0,57.31,21.59C54,20.86 52.8,18.05 51,16.17 51.64,15.37 52.17,14.7 52.71,14.04A14.39,14.39,0,0,0,50,13A10.47,10.47,0,0,0,42.68,14.13C41.13,15.04,40.68,16.74,41.81,18.65A1.81,1.81,0,0,1,41.5,21.19C41.03,21.65 40.57,22.12 40.03,22.12 39.49,22.12 39.03,21.65 38.56,21.19A1.81,1.81,0,0,1,38.25,18.65C39.4,16.74,38.93,15.04,37.38,14.13A10.47,10.47,0,0,0,30,13A14.39,14.39,0,0,0,27.27,14C27.81,14.66 28.34,15.33 28.98,16.13 27.17,18.01 25.98,20.82 22.7,21.53A7.77,7.77,0,0,0,19.56,23.53C18.01,24.91,17.56,26.68,18.25,29.04A12.31,12.31,0,0,1,20.51,27.4A5.37,5.37,0,0,1,22.51,27.4A7,7,0,0,1,21.72,28.79C19.14,31.64 16.87,34.68 16.82,38.73 16.82,40.88 17.12,43.02 17.31,45.41 19.44,45.41 19.75,43.63 20.64,42.48 21.19,41.77 21.14,40.08 22.64,40.86 24.14,41.64 23.18,42.86 22.73,43.86A4.83,4.83,0,0,1,22.36,44.48A4.73,4.73,0,0,0,22.55,50.19C23.17,51.08,23.86,51.92,24.55,52.77A1.65,1.65,0,0,0,27.13,53.26A3.06,3.06,0,0,1,30.98,53.54A6.28,6.28,0,0,1,30.24,54.31C28.24,55.77,28.3,56.37,30.35,57.78A6.92,6.92,0,0,1,31.53,59.21C32.98,61.03 34.35,62.9 35.9,64.63 37.04,65.9 38.58,65.2 39.98,65.06L40.1,65.06C41.5,65.2 43.04,65.9 44.18,64.63 45.73,62.9 47.1,61.03 48.55,59.21A6.92,6.92,0,0,1,49.73,57.78C51.78,56.37,51.82,55.78,49.84,54.31A6.28,6.28,0,0,1,49.1,53.54A3.06,3.06,0,0,1,52.95,53.26A1.65,1.65,0,0,0,55.53,52.77C56.19,51.92,56.88,51.08,57.53,50.19A4.73,4.73,0,0,0,57.72,44.48A4.83,4.83,0,0,1,57.35,43.86C56.9,42.86 55.96,41.65 57.44,40.86 58.92,40.07 58.89,41.77 59.44,42.48 60.33,43.63 60.64,45.48 62.77,45.41 62.96,43.02 63.28,40.88 63.26,38.73 63.14,34.74 60.87,31.7 58.29,28.85z M43,52.66A6.75,6.75,0,0,1,41.45,53.85A2.8,2.8,0,0,1,38.53,53.85A6.75,6.75,0,0,1,37,52.66C36.31,51.89 35.71,51.03 35.07,50.21 36.23,49.56 37.38,48.86 38.58,48.29 38.97,48.11 39.26,48.01 39.77,47.8L40.25,47.8C40.76,48.01 41.05,48.11 41.44,48.29 42.65,48.86 43.79,49.56 44.95,50.21 44.3,51 43.7,51.89 43,52.66z M51.35,29.27C47.76,32.11,47.75,32.68,49.96,36.8A7.52,7.52,0,0,1,49.7,44.8C47.07,49.1,44.38,49,42,44.51A3,3,0,0,1,42.22,40.86A13.78,13.78,0,0,0,43.81,38.25C44.81,36.39,44.5,35.94,42.44,35.96L37.58,35.96C35.52,35.96,35.23,36.39,36.21,38.25A13.78,13.78,0,0,0,37.8,40.86A3,3,0,0,1,38,44.51C35.6,48.99,32.91,49.1,30.27,44.83A7.52,7.52,0,0,1,30.01,36.83C32.23,32.71 32.21,32.14 28.62,29.3 27.15,28.14 27.4,27.4 28.34,26.3A17.81,17.81,0,0,1,30.28,24.18C32.19,22.62 33.34,22.77 35.11,24.44 36.52,25.77 37.67,27.32 39.98,27.32 42.29,27.32 43.44,25.77 44.85,24.44 46.62,22.77 47.77,22.62 49.68,24.18A17,17,0,0,1,51.62,26.3C52.58,27.37,52.83,28.11,51.36,29.27z" />
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M80,80z M0,0z M54,59.18C56.75,55.67,59.88,52.51,60.26,47.83L64.54,49.64A17.44,17.44,0,0,1,64.84,46.21A21.3,21.3,0,0,0,64.59,33.81A8.45,8.45,0,0,1,64.59,31.56C68.38,35.35 69.53,39.65 67.96,44.56 67.15,47.08 66.79,49.39 69.68,51.17A5.6,5.6,0,0,1,63.08,52.3L53.19,66.39C51.8,65.13,51.45,60.04,52.65,57.91z" />
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M80,80z M0,0z M65.17,26.23A11.76,11.76,0,0,0,62.67,22.8C61.2,21.63,59.28,21.03,57.75,19.92A7.22,7.22,0,0,1,55.26,16.92C55.08,16.45,56.4,15.4,57.04,14.61A3.31,3.31,0,0,0,57.27,14.16C54.57,11.63 51.02,11.39 47.56,10.53 49,9.77 50.45,8.8 51.78,9 55.46,9.54 58.41,11.67 60.95,14.32A1.74,1.74,0,0,1,61.09,15.94C60.88,16.33,60.17,16.5,59.64,16.62A1.44,1.44,0,0,1,58.9,16.54A79.68,79.68,0,0,1,65.57,22.11A8.32,8.32,0,0,1,67.7,30.58A50,50,0,0,1,65.17,26.23z" />
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M80,80z M0,0z M49.13,63L42,69.11A3.07,3.07,0,0,1,40,70A3.07,3.07,0,0,1,38,69.07L30.87,63C30.09,65.47 31.23,68 33.76,70 35.6,71.43 37.3,73 39.06,74.58L39.99,75.39 39.99,75.39 40.93,74.58C42.69,73.04 44.39,71.43 46.23,70 48.77,68 49.91,65.44 49.13,63z" />
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M80,80z M0,0z M44.66,6.46A2.23,2.23,0,0,1,41.66,5.75A2.83,2.83,0,0,0,40,4.67L40,4.67A2.84,2.84,0,0,0,38.38,5.75A2.23,2.23,0,0,1,35.38,6.46C33.54,5.75,31.38,4.92,29.38,6.77A5.93,5.93,0,0,0,30.14,7.19A23.89,23.89,0,0,1,38.62,11.81A2.52,2.52,0,0,0,40.01,12.25L40.01,12.25A2.53,2.53,0,0,0,41.41,11.81A23.89,23.89,0,0,1,49.89,7.19A5.93,5.93,0,0,0,50.65,6.77C48.63,4.92,46.5,5.75,44.66,6.46z" />
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M80,80z M0,0z M47,26C48.06,25.39 48.71,25.9 49,26.94 49.51,28.61 47.39,31.51 45.08,31.56A5.91,5.91,0,0,1,43.3,30C43.07,29.57,43.64,28.46,44.15,28A14.11,14.11,0,0,1,47,26z" />
        </Canvas>
        <Canvas x:Key="set_alterac" Clip="M0,0 V30 H30 V0 H0 Z">
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M30,30z M0,0z M12.15,2.4L12,1.91A0.66,0.66,0,0,1,12.33,1.19L14.01,0.19A1.39,1.39,0,0,1,15.62,0.35L17.18,1.7A0.66,0.66,0,0,1,17.36,2.44L17.26,2.71A0.38,0.38,0,0,1,16.6,2.79L16.4,2.5A7,7,0,0,0,13.1,2.4L12.71,2.64A0.37,0.37,0,0,1,12.15,2.4z" />
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M30,30z M0,0z M12.57,26.06A10.88,10.88,0,0,1,12.21,29.44C12.21,29.44 12.84,30 15,30 17.16,30 17.66,29.58 17.66,29.58A11.45,11.45,0,0,1,17.48,27L16,25.85 14.2,27z" />
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M30,30z M0,0z M23.3,10.61L23.88,10.92A4.46,4.46,0,0,0,25.43,9A5.41,5.41,0,0,0,23.77,6.38L22.67,7.25A24.46,24.46,0,0,0,16.51,4.73L16.38,3.73A4.21,4.21,0,0,0,14.91,3.15A4.69,4.69,0,0,0,13.28,3.15L13.41,3.67 13.26,4.38 10.13,3.67 9.43,3.67 8.72,4.38 8.17,4.46 8.27,2.83 8.06,2.57A0.56,0.56,0,0,0,7.31,2.45A8.61,8.61,0,0,0,4.57,4.68A9.26,9.26,0,0,0,6.66,7.6A0.55,0.55,0,0,0,7.48,7.5L8.09,6.6 8.51,7 8.51,11.14C8.51,14.63,6.62,20.4,6.62,20.4A22.38,22.38,0,0,0,14.33,26.09L16,25 19.86,27 24.19,21C23.19,20,22.93,17.12,22.93,17.12L22.93,10.92z M12.3,5.15L18.7,6.57A1.35,1.35,0,0,1,19.57,7.79L19.57,16C19.57,16,19.64,19.31,20.39,20L18.91,22.7C18.91,22.7,14.78,20.14,13.68,18.57L13.68,8.89C13.68,8.89 13.58,6.33 12.32,5.21 12.29,5.18 12.31,5.14 12.35,5.15z M19.5,25.38C19.5,25.38 14.15,23.53 11.16,19.55 11.16,19.55 11.51,14.71 11.39,12.08 11.3,10.08 10.97,7.59 10.81,6.48A3.88,3.88,0,0,0,10.48,5.38L10.37,5.15 10.13,4.84 10.72,4.84C10.72,4.84,12.31,6.37,12.31,12.43L12.31,18.92C12.31,18.92,16.08,23.13,19.22,24.35L21.82,21.05A7,7,0,0,1,21.11,18L21.11,9.21 20.64,7.55 21.5,7.83C21.5,7.83,21.82,8.14,21.82,9.83L21.82,18.21C21.82,18.21,22.03,20.72,22.88,21.21z" />
        </Canvas>
        <Canvas x:Key="set_sunken_city" Clip="M0,0 V30 H30 V0 H0 Z">
            <Path Data="F1 M30,30z M0,0z M13.23,15.69C13.08,16.14 12.91,16.55 12.8,16.97 12.65,17.54 12.29,17.72 11.8,17.38A3.08,3.08,0,0,1,10.5,15.38A1.52,1.52,0,0,1,11,14.26A5.42,5.42,0,0,1,14.13,12.81A0.91,0.91,0,0,1,14.87,13.11A27.07,27.07,0,0,1,16.87,15.98A2.27,2.27,0,0,1,16.95,17.4A6.68,6.68,0,0,1,16.3,19.2A3,3,0,0,1,15.3,20.27A17.33,17.33,0,0,1,12.64,21.5A4.58,4.58,0,0,1,11.11,21.62A4,4,0,0,1,10.28,21.45A4.55,4.55,0,0,1,6.78,18.18C6.55,17.43,6.31,16.67,6.11,15.9A3.2,3.2,0,0,0,5.11,14.22A3.09,3.09,0,0,1,3.81,12.4A1,1,0,0,1,3.93,11.62C4.07,11.48,4.43,11.56,4.69,11.62A2.16,2.16,0,0,1,5.12,11.76C5.85,11.94,6.12,11.69,6.12,10.95A25.46,25.46,0,0,1,6.59,8A2.64,2.64,0,0,1,7.1,7C7.88,5.92,8.15,6,8.94,7A8.76,8.76,0,0,0,10.3,8.43C10.92,8.96 11.13,8.87 11.6,8.21 12.47,6.98 13.33,5.74 14.27,4.56A6.24,6.24,0,0,1,15.79,3.23C16.59,2.71 17.08,3.02 17.05,3.97 17.05,5.58 16.94,7.18 16.89,8.79A1.7,1.7,0,0,0,16.89,9.18C17.1,10.73 17.65,11.18 19.15,10.72 20.4,10.38 21.62,9.93 22.86,9.54A9.45,9.45,0,0,1,24.22,9.17C25.1,9.04,25.49,9.45,25.22,10.27A11.86,11.86,0,0,1,24,12.69C23.37,13.62 22.57,14.44 21.88,15.34 21.19,16.24 21.2,17.08 22.25,17.77A17,17,0,0,0,24.52,18.88C24.86,19.04 25.24,19.14 25.57,19.31 26.37,19.74 26.42,20.31 25.72,20.91A8.71,8.71,0,0,1,22.44,22.5C21.88,22.66 21.3,22.72 20.72,22.83 20.14,22.94 20.04,23.27 20.41,23.7A5.44,5.44,0,0,0,21.14,24.41C21.51,24.73,21.44,25.08,21.08,25.21A4.87,4.87,0,0,1,19.19,25.59A4.1,4.1,0,0,1,17.53,25A2.06,2.06,0,0,0,15.15,25A6.05,6.05,0,0,1,11.3,25.86A7.56,7.56,0,0,0,7.52,26.59A7,7,0,0,1,6.18,27A0.93,0.93,0,0,1,4.93,26A5,5,0,0,1,5.7,23.42C6.7,21.66 7.33,21.33 9.24,21.99 12.09,22.99 14.52,22.18 16.77,20.39A3.3,3.3,0,0,0,17.91,18.49C17.97,18.23 17.97,17.96 18.04,17.71 18.77,15.09 17.04,13.71 15.32,12.38A2.32,2.32,0,0,0,12.74,12A4,4,0,0,1,12.17,12.24C10.11,12.86,9.56,14.44,9.55,16.36A1.51,1.51,0,0,0,10.77,18.03A5.62,5.62,0,0,0,11.48,18.19C12.86,18.43 13.03,18.34 13.68,17.13 14,16.51 14,16.24 13.23,15.69z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
        </Canvas>
        <Canvas x:Key="set_nathria" Clip="M0,0 V30 H30 V0 H0 Z">
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M30,30z M0,0z M9.67,3.41L9.79,3.41C9.91,3.48 10.04,3.57 10.16,3.62 11.63,4.19 12.8,5.11 13.59,6.47 13.83,6.88 13.87,7.41 14.01,7.88L13.81,7.97C13.71,7.9 13.59,7.86 13.52,7.78 13.13,7.38 12.77,7.42 12.52,7.89 12.24,8.43 11.92,8.99 11.79,9.57 11.55,10.65 11.95,11.58 12.69,12.44 13.18,10.91 13.22,10.83 14.17,9.85 14.82,9.17 15.02,9.17 15.66,9.85 16.36,10.58 17.06,11.31 17.04,12.51 18.28,11.29 18.47,9.39 17.54,8.15 17.04,7.48 16.93,7.19 16.15,7.9 16.11,7.93 16.04,7.92 15.81,7.96 15.96,7.42 15.99,6.89 16.24,6.51 16.65,5.9 17.12,5.29 17.7,4.84 18.4,4.28 19.23,3.89 20.01,3.43L20.13,3.43C19.98,4.06 19.78,4.7 19.69,5.34 19.62,5.76 19.86,5.96 20.33,5.94 20.57,5.94 20.92,6.06 21.04,6.25 22.03,7.81 22.85,9.46 23.24,11.3 23.51,12.62 23.77,13.95 24.06,15.26 24.29,16.25 24.36,16.27 25.25,15.8 25.32,15.76 25.41,15.8 25.49,15.8L25.49,16.18C25.32,16.62 25.13,17.05 24.99,17.5 24.62,18.68 23.93,19.68 23.18,20.73 21.8,18.57 20.43,16.5 18.47,14.89 17.71,16.36 16.37,17.1 15.16,18 15.04,18.09 14.7,18.06 14.56,17.94 13.37,17.05 12.05,16.3 11.33,14.89 9.37,16.53 8,18.6 6.65,20.69 5.47,19.28 4.39,16.8 4.53,15.79 5.45,16.31 5.55,16.25 5.8,15.19 5.89,14.78 5.98,14.38 6.06,13.97 6.52,11.29 7.18,8.7 8.71,6.38 8.93,6.06 9.12,5.83 9.54,5.92 10.01,6.01 10.24,5.75 10.16,5.32 10.05,4.68 9.84,4.05 9.68,3.42z" />
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M30,30z M0,0z M18.89,16.14L22.86,21.49C22.35,22.14 21.87,22.67 21.5,23.26 21.21,23.72 21.06,24.26 20.84,24.77 20.81,24.83 20.81,24.89 20.78,24.95 20.35,25.76 18.29,26.56 17.38,26.23 18.38,24.99 18.88,23.59 18.88,21.82 18.53,22.08 18.26,22.24 18.07,22.45 16.94,23.77 16.01,25.2 15.6,26.93 15.54,27.18 15.23,27.36 15.04,27.58 14.83,27.38 14.49,27.2 14.44,26.97 14.09,25.33 13.22,23.98 12.2,22.72 11.95,22.41 11.61,22.16 11.25,21.94 11.14,23.56 11.65,24.95 12.7,26.26 12.42,26.31 12.2,26.38 12,26.36 11.01,26.24 10.14,25.81 9.41,25.11 9.35,25.05 9.26,24.99 9.25,24.92 9.07,23.52 8.07,22.63 7.16,21.5L11.15,16.13C11.42,16.52 11.58,16.73 11.73,16.95 12.46,18.14 13.54,18.87 14.81,19.36 14.96,19.42 15.17,19.39 15.33,19.33 16.64,18.82 17.73,18.03 18.44,16.77 18.53,16.61 18.66,16.47 18.9,16.14L18.9,16.14z" />
        </Canvas>
        <Canvas x:Key="set_lichking" Clip="M0,0 V30 H30 V0 H0 Z">
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M30,30z M0,0z M12.32,3.98C14.82,5.48,15.62,9.08,14.22,10.98L13.92,10.68C13.72,10.38 13.42,10.28 13.02,10.38 12.72,10.38 12.42,10.68 12.42,11.08 12.42,11.48 12.42,11.98 12.52,12.48 12.72,13.38 13.12,14.28 13.32,15.28 13.32,15.28 13.42,15.28 13.42,15.38 14.02,14.78 14.52,14.28 15.02,13.68 15.22,13.38 15.42,13.38 15.62,13.68 16.22,14.28 16.82,14.78 17.42,15.28 17.52,15.18 17.62,15.18 17.62,15.08 18.22,14.08 18.62,12.98 18.42,11.78 18.42,11.68 18.42,11.58 18.32,11.48 18.02,10.58 17.52,10.38 16.72,10.88 16.62,10.88 16.62,10.98 16.52,10.98 15.42,9.48 16.72,5.08 18.52,4.48L18.52,4.78C18.52,5.38 18.42,5.88 18.42,6.48 18.32,7.78 18.82,8.88 19.42,9.88 20.12,11.28 20.82,12.68 21.12,14.28 21.52,16.18 21.72,17.98 21.52,19.88L21.52,20.08Q21.42,20.88,20.62,20.58C19.92,20.38 19.22,20.68 19.02,21.38 18.82,21.98 18.72,22.68 18.72,23.28 18.72,23.58 18.62,23.88 18.32,24.08 17.92,24.48 17.42,24.78 17.02,25.18L16.92,25.08 16.92,23.48Q16.82,23.48,16.72,23.38C16.62,23.48 16.52,23.68 16.52,23.78 16.42,24.08 16.42,24.48 16.42,24.78 16.42,25.18 16.32,25.38 16.02,25.58 15.22,26.18 15.32,26.18 14.32,25.58 14.02,25.38 13.82,25.08 13.82,24.68L13.82,23.58C13.82,23.38,13.72,23.18,13.62,22.98L13.52,22.98 13.52,24.38C13.52,24.48 13.52,24.68 13.42,24.68 13.32,24.68 13.12,24.68 13.02,24.58 12.62,24.28 12.22,23.98 11.82,23.58 11.72,23.48 11.72,23.28 11.72,23.08 11.82,22.28 11.72,21.58 11.22,20.88 10.82,20.28 9.91999999999999,19.88 9.22,20.28 8.81999999999999,20.58 8.62,20.48 8.52,19.98 8.32,18.98 8.42,17.98 8.62,16.88 9.02,14.28 10.02,11.98 11.32,9.77999999999999 12.02,8.57999999999999 12.42,7.27999999999999 12.42,5.87999999999998 12.42,5.27999999999999 12.22,4.77999999999999 12.22,4.17999999999998 12.32,4.27999999999998 12.32,4.17999999999998 12.32,3.97999999999998z M14.22,18.78C13.02,18.08 11.72,17.38 10.42,16.68 10.92,18.58 12.72,19.58 14.22,18.78z M16.52,18.88C17.42,19.18 18.32,19.18 19.12,18.68 19.82,18.18 20.22,17.58 20.12,16.98 19.02,17.58 17.82,18.18 16.52,18.88z M16.02,21.58C15.92,20.98 15.72,20.38 15.42,19.98 15.42,19.98 15.12,19.88 15.12,19.98 14.82,20.48 14.62,20.88 14.42,21.48Q15.42,21.18,16.02,21.58z" />
        </Canvas>
        <Canvas x:Key="set_path_of_arthas" Clip="M0,0 V30 H30 V0 H0 Z">
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M30,30z M0,0z M14.51,3.43L15.44,3.43C17.06,4.24 17.28,4.88 16.62,6.55 16.48,6.91 16.42,7.45 16.6,7.76 16.98,8.44 17,8.98 16.48,9.54 16.34,9.69 16.33,9.95 16.26,10.15 16.67,10.03 16.94,9.84 17.21,9.67 18.23,9.02 18.98,9.07 19.92,9.83 20.13,10 20.33,10.2 20.57,10.32 20.86,10.46 21.19,10.52 21.5,10.62L21.56,9.7C21.56,9.66 21.58,9.63 21.61,9.55 22.24,9.49 22.76,9.73 23.21,10.16 24.5,11.37 24.8,12.7 24.13,14.38 23.99,14.73 23.71,15.03 23.49,15.36 23.23,15.12 22.94,14.9 22.72,14.63 21.72,13.41 20.52,12.98 19.16,13.42 19.31,13.69 19.46,13.96 19.6,14.23 20.01,15.03 19.98,15.1 19.19,15.5 19.17,15.51 19.17,15.57 19.16,15.6 22.05,16.19 24.54,17.32 25.68,20.32L25.68,21.02C25.46,20.98 25.2,20.99 25.02,20.88 23.36,19.85 21.61,19.19 19.62,19.72 19.25,19.82 18.98,19.83 18.67,19.57 18.28,19.25 17.82,19.12 17.21,19.53 17.56,19.81 17.83,20.13 18.17,20.28 19.41,20.81 20.09,21.8 20.55,22.99 20.6,23.13 20.57,23.31 20.57,23.47 20.41,23.46 20.24,23.49 20.09,23.43 19.42,23.16 18.98,23.24 18.6,23.86 18.34,24.28 18.16,24.76 18,25.23 17.86,25.66 17.79,26.12 17.69,26.59L12.87,26.59C12.55,24.8,11.62,23.35,10.4,22.07A1.47,1.47,0,0,1,10.07,21.47C9.96,21.09 10.04,20.87 10.54,20.85 11.03,20.83 11.54,20.69 11.98,20.48 12.59,20.19 12.64,19.73 12.23,19.13 11.87,19.38 11.54,19.77 11.15,19.84 10.73,19.92 10.25,19.7 9.8,19.61L9.74,19.61C7.99,19.59 6.36,19.98 4.89,20.96 4.77,21.04 4.53,21.1 4.44,21.03 4.34,20.96 4.28,20.72 4.32,20.59 4.45,20.15 4.58,19.69 4.79,19.29 5.49,17.99 6.63,17.14 7.92,16.53 8.83,16.1 9.83,15.86 10.8,15.53A0.68,0.68,0,0,0,10.66,15.44C9.99,15.11 9.92,14.87 10.3,14.22 10.46,13.94 10.63,13.67 10.85,13.3 9.25000000000001,13.49 7.89000000000001,13.88 6.92000000000001,15.12 6.61000000000001,15.51 6.37000000000001,15.51 6.14000000000001,15.06 5.97000000000001,14.74 5.84000000000001,14.39 5.73,14.04 5.23,12.49 5.7,10.96 7.03,10.02 7.41,9.74999999999999 7.92,9.65999999999999 8.50000000000001,9.42999999999999 8.43000000000001,9.75999999999999 8.38000000000001,9.86999999999999 8.39000000000001,9.97 8.42000000000001,10.2 8.39000000000001,10.54 8.53000000000001,10.63 8.67000000000001,10.72 9.00000000000001,10.63 9.20000000000001,10.52 9.50000000000001,10.36 9.77000000000001,10.12 10.04,9.9 11.05,9.08 11.81,9.04 12.9,9.77 13.14,9.93 13.38,10.11 13.62,10.28 13.68,10.23 13.74,10.17 13.8,10.12 13.66,9.90999999999999 13.45,9.71 13.39,9.48 13.27,8.99999999999999 13,8.3 13.22,8.06 13.79,7.43 13.54,6.9 13.27,6.37 12.75,5.33 13.05,4.24 14.07,3.71 14.22,3.63 14.37,3.55 14.51,3.47z M15.53,21.25C16.26,19.84 16.46,18.38 16.59,16.92 16.63,16.42 16.8,16.06 17.2,15.76 17.88,15.25 18.29,14.57 18.32,13.69 18.34,13.11 18.23,13.03 17.69,13.19 16.89,13.43 16.31,13.28 15.88,12.7 15.74,12.51 15.6,12.24 15.62,12.02 15.65,11.66 15.81,11.32 15.95,10.84L13.91,10.84C14.46,11.5 14.57,12.1 14.15,12.66 13.53,13.47 12.68,13.47 11.75,13.04 11.56,14.09 11.66,15.04 12.48,15.62 13.22,16.14 13.45,16.78 13.48,17.59 13.48,17.78 13.52,17.98 13.56,18.16 13.79,19.18 14.04,20.19 14.28,21.21 14.34,21.21 14.4,21.2 14.46,21.19 14.63,20.97 14.79,20.74 14.98,20.5 15.17,20.77 15.33,20.98 15.51,21.24z" />
            <Path Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" Data="F1 M30,30z M0,0z M14.06,15.65C13.1,15.63 12.36,14.98 12.26,14.18 12.21,13.81 12.41,13.65 12.68,13.82 13.25,14.2 13.77,14.64 14.3,15.09 14.38,15.16 14.4,15.4 14.35,15.5 14.3,15.6 14.11,15.63 14.07,15.65z M16.06,15.55C16.06,15.55 15.77,15.47 15.7,15.33 15.65,15.23 15.79,14.96 15.92,14.85 16.29,14.52 16.69,14.21 17.11,13.93 17.24,13.84 17.53,13.82 17.62,13.9 17.72,14 17.75,14.25 17.71,14.42 17.54,15.14 16.99,15.55 16.07,15.55z" />
        </Canvas>
        <Canvas x:Key="set_festival_of_legends" Clip="M0,0 V30 H30 V0 H0 Z">
            <Path Data="F1 M30,30z M0,0z M2.004,11.214L2.004,10.462C2.117,10.424 2.229,10.375 2.35,10.377 2.836,10.382 3.323,10.356 3.81,10.392 3.89,10.398 3.968,10.387 4.005,10.302 4.05,10.198 4.112,10.094 4.116,9.978 4.128,9.6 4.125,9.223 4.225,8.85 4.269,8.686 4.277,8.51 4.33,8.34 4.676,7.229 5.402,6.456 6.329,5.866 6.69,5.636 7.079,5.46 7.48,5.308 8.018,5.102 8.546,4.871 9.103,4.735 9.897,4.541 10.693,4.357 11.508,4.293 11.578,4.288 11.65,4.27 11.718,4.251A1.74,1.74,0,0,1,12.156,4.183A1.16,1.16,0,0,0,12.535,4.112A0.99,0.99,0,0,1,12.815,4.062C13.111,4.045 13.406,4.028 13.702,4.025 14.96,4.008 16.219,3.936 17.47,4.155 17.944,4.239 18.423,4.295 18.898,4.379 19.566,4.499 20.215,4.705 20.871,4.882 21.371,5.017 21.858,5.192 22.334,5.394 22.849,5.612 23.354,5.866 23.837,6.163 24.407,6.513 24.893,6.946 25.257,7.528 25.497,7.912 25.692,8.31 25.793,8.763 25.885,9.173 25.979,9.582 25.99,10.004 26,10.375 25.991,10.375 26.35,10.377 26.819,10.377 27.286,10.375 27.754,10.377 27.944,10.379 27.991,10.432 27.996,10.628 28,10.748 27.997,10.868 27.996,10.989 27.996,11.261 27.959,11.299 27.696,11.301L26.15,11.302C25.96,11.302,25.912,11.34,25.887,11.54A4.82,4.82,0,0,1,25.752,12.2C25.649,12.59 25.598,12.996 25.442,13.371 25.374,13.533 25.417,13.583 25.592,13.577 26.331,13.55 27.071,13.572 27.81,13.564 27.915,13.564 27.967,13.615 27.983,13.721 28.009,13.901 27.996,14.081 27.997,14.261 27.997,14.471 27.977,14.491 27.773,14.491L25.626,14.491C25.487,14.491 25.349,14.491 25.211,14.496 25.109,14.5 25.019,14.541 24.996,14.658 24.935,14.965 24.792,15.24 24.684,15.528 24.558,15.869 24.42,16.206 24.274,16.537 24.164,16.785 24.155,16.78 24.419,16.78L27.74,16.778C27.964,16.778 27.996,16.812 27.998,17.048 28,17.178 27.998,17.308 27.998,17.438 27.998,17.697 27.99,17.706 27.738,17.706 26.483,17.706 25.228,17.708 23.973,17.703 23.814,17.703 23.711,17.746 23.653,17.907 23.493,18.35 23.202,18.719 23.003,19.14 22.92,19.315 22.814,19.477 22.72,19.645 22.671,19.732 22.535,19.805 22.58,19.902 22.632,20.019 22.777,19.963 22.883,19.963 24.505,19.966 26.128,19.965 27.75,19.966 27.954,19.966 27.994,20.004 27.998,20.218 28.002,20.383 27.998,20.548 27.998,20.713 27.998,20.811 27.951,20.863 27.858,20.863 27.753,20.865 27.648,20.866 27.542,20.866 25.772,20.866 24.002,20.868 22.231,20.862A0.476,0.476,0,0,0,21.773,21.122C21.62,21.38 21.44,21.622 21.27,21.87 20.815,22.535 20.308,23.152 19.8,23.77 19.516,24.114 19.235,24.463 18.91,24.767 18.642,25.016 18.38,25.271 18.114,25.522 17.484,26.114 16.786,26.568 15.967,26.829 14.994,27.139 14.065,27.014 13.172,26.569 12.425,26.196 11.778,25.663 11.192,25.052 10.657,24.492 10.169,23.889 9.69,23.279A18.894,18.894,0,0,1,8.273,21.223C8.124,20.971 7.94,20.855 7.641,20.857 5.967,20.87 4.292,20.864 2.616,20.864 2.411,20.864 2.206,20.871 2.002,20.834L2.002,19.993C2.145,19.983,2.287,19.967,2.43,19.967L7.18,19.967 7.322,19.966C7.37,19.963 7.422,19.963 7.445,19.908 7.465,19.858 7.432,19.823 7.405,19.788A5.872,5.872,0,0,1,6.874,18.864A16.29,16.29,0,0,0,6.399,17.939A0.418,0.418,0,0,0,5.991,17.704C4.78,17.711 3.57,17.707 2.358,17.708 2.238,17.708 2.12,17.704 2.002,17.681L2.002,16.87A0.637,0.637,0,0,1,2.385,16.78C2.652,16.785 2.919,16.78 3.185,16.78 4.015,16.78 4.845,16.781 5.675,16.78 5.849,16.78 5.874,16.74 5.799,16.576 5.736,16.436 5.651,16.306 5.596,16.162 5.408,15.67 5.21,15.181 5.068,14.671 5.03,14.535 4.959,14.489 4.831,14.491 4.531,14.496 4.23,14.498 3.931,14.491 3.287,14.48 2.643,14.525 2,14.463L2,13.622C2.285,13.538 2.576,13.568 2.865,13.567 3.423,13.564 3.98,13.567 4.538,13.567L4.595,13.567C4.665,13.564 4.688,13.528 4.669,13.459 4.595,13.201 4.526,12.941 4.447,12.684 4.337,12.321 4.279,11.946 4.21,11.574 4.169,11.35 4.138,11.305 3.914,11.305 3.404,11.305 2.894,11.302 2.385,11.305A0.706,0.706,0,0,1,2,11.216L2.004,11.214z M18.3,13.233L18.24,14.227C18.184,15.209 17.83,16.091 17.426,16.959 17.226,17.389 16.906,17.724 16.635,18.097 16.161,18.747 15.649,19.36 15.039,19.875 14.625,20.225 14.188,20.537 13.738,20.831 13.023,21.299 12.283,21.712 11.454,21.921 11.368,21.944 11.214,21.927 11.22,22.036 11.225,22.146 11.371,22.163 11.474,22.186L11.531,22.193C11.754,22.216 11.979,22.233 12.201,22.265 13.108,22.397 14.004,22.385 14.889,22.091 15.394,21.924 15.91,21.79 16.389,21.549A7.984,7.984,0,0,0,18.884,19.592A7.733,7.733,0,0,0,20.005,17.91C20.372,17.146 20.69,16.365 20.801,15.508 20.81,15.443 20.808,15.354 20.885,15.331 20.956,15.311 20.992,15.385 21.035,15.427 21.322,15.717 21.535,16.057 21.702,16.433 21.726,16.489 21.751,16.568 21.825,16.563 21.904,16.558 21.929,16.481 21.946,16.413 22.113,15.783 22.256,15.15 22.247,14.493A11.269,11.269,0,0,0,22.142,13.162C22.076,12.674 21.985,12.192 21.812,11.735 21.687,11.405 21.523,11.092 21.382,10.77 20.92,9.72 20.269,8.817 19.415,8.093A6.302,6.302,0,0,0,16.098,6.616A14.36,14.36,0,0,0,15.571,6.558C15.519,6.553 15.461,6.558 15.436,6.618 15.409,6.683 15.463,6.716 15.502,6.752A0.473,0.473,0,0,0,15.582,6.815C15.915,7.023 16.206,7.288 16.503,7.55 17,7.988 17.511,8.408 17.934,8.928 18.179,9.228 18.487,9.483 18.658,9.835 19.018,10.572 19.448,11.281 19.626,12.104 19.637,12.154 19.69,12.214 19.634,12.254 19.582,12.291 19.537,12.23 19.495,12.201A4.521,4.521,0,0,0,18.291,11.582C18.228,11.562 18.163,11.526 18.106,11.592 18.054,11.65 18.059,11.722 18.086,11.789 18.266,12.249 18.262,12.739 18.299,13.231L18.299,13.233z M8.558,14.623C8.558,14.693 8.556,14.763 8.558,14.833 8.56,14.882 8.554,14.945 8.606,14.965 8.672,14.989 8.694,14.923 8.724,14.879 8.88,14.645 9.034,14.41 9.19,14.177 9.417,13.84 9.68,13.536 9.946,13.235 10.055,13.112 10.102,13.121 10.186,13.265 10.267,13.406 10.346,13.55 10.42,13.695 10.703,14.247 10.979,14.805 11.128,15.415 11.281,16.044 11.463,16.669 11.456,17.331 11.456,17.435 11.479,17.537 11.592,17.568 11.912,17.656 12.238,17.751 12.564,17.722 13.352,17.652 14.106,17.446 14.774,16.972 15.778,16.256 16.474,15.325 16.661,14.028 16.738,13.492 16.644,12.982 16.498,12.476 16.466,12.367 16.415,12.263 16.366,12.16 16.34,12.106 16.3,12.056 16.236,12.056 16.162,12.056 16.149,12.126 16.129,12.181 15.999,12.541 15.79,12.851 15.567,13.151 15.102,13.776 14.506,14.211 13.827,14.535 13.716,14.588 13.644,14.562 13.6,14.445L13.56,14.332C13.16,13.251 12.641,12.252 11.787,11.482 11.67,11.377 11.67,11.326 11.784,11.214 11.934,11.067 12.102,10.943 12.248,10.789 12.786,10.219 13.442,9.892 14.188,9.736 15.001,9.565 15.792,9.701 16.582,9.886A5.39,5.39,0,0,1,17.948,10.415C17.978,10.432 18.018,10.473 18.049,10.429 18.075,10.391 18.036,10.355 18.011,10.325 17.852,10.133 17.694,9.939 17.532,9.747A6.949,6.949,0,0,0,16.747,8.938C16.338,8.588 15.873,8.329 15.437,8.025 14.616,7.452 13.697,7.141 12.732,6.975 12.092,6.865 11.451,6.846 10.815,7.015 10.742,7.034 10.627,7.021 10.619,7.126 10.613,7.221 10.723,7.229 10.789,7.258 11.131,7.406 11.425,7.62 11.671,7.911 11.741,7.993 11.784,8.073 11.676,8.164A5.263,5.263,0,0,0,11.019,8.854C10.189,9.846 9.567,10.972 9.076,12.182 8.76,12.959 8.626,13.782 8.562,14.621L8.558,14.622z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
        </Canvas>
        <Canvas x:Key="set_titans" Clip="M0,0 V30 H30 V0 H0 Z">
            <Path Data="F1 m 0.00443,28.116 c 0.139,-0.369 0.278,-0.737 0.418,-1.111 0.184,-0.494 0.364,-0.989 0.548,-1.483 0.02,-0.06 0.08,-0.113 0.12597,-0.153 0.207,-0.171 0.441,-0.202 0.706,-0.202 8.8476,0 17.6996,0 26.5466,0 0.171,0 0.31,0.04 0.459,0.126 0.148,0.09 0.233,0.193 0.292,0.35 0.288,0.8 0.589,1.601 0.886,2.401 0.02,0.06 0.02,0.108 -0.02,0.162 -0.18,0.278 -0.356,0.562 -0.531,0.845 -0.04,0.06 -0.07,0.09 -0.139,0.08 H 0.76643 c -0.108,0 -0.166,-0.03 -0.22,-0.125 -0.158,-0.261 -0.319,-0.522 -0.486,-0.778 -0.02,-0.03 -0.04,-0.05 -0.0599985,-0.08 v -0.04 z M 4.9894,17.443 c -0.04,0.02 -0.07,0.03 -0.1,0.05 -0.356,0.189 -0.715,0.373 -1.07,0.562 -0.126,0.07 -0.248,0.05 -0.342,-0.05 -0.06,-0.06 -0.112,-0.126 -0.171,-0.184 -0.126,-0.126 -0.166,-0.279 -0.135,-0.445 0.149,-0.85 0.297,-1.695 0.445,-2.54 0.05,-0.293 0.104,-0.589 0.153,-0.882 0.04,-0.229 0.157,-0.413 0.342,-0.553 0.993,-0.75 2.108,-1.285 3.277,-1.703 1.245,-0.441 2.531,-0.733 3.8346,-0.931 1.461,-0.22 2.927,-0.297 4.401,-0.265 2.1,0.04 4.168,0.328 6.186,0.93 1.241,0.369 2.424,0.868 3.516,1.569 0.184,0.117 0.364,0.243 0.54,0.378 0.09,0.06 0.148,0.153 0.22,0.229 0.139,0.153 0.153,0.351 0.189,0.54 0.188,1.07 0.373,2.14 0.562,3.21 0.03,0.179 0,0.337 -0.135,0.467 -0.05,0.04 -0.09,0.1 -0.135,0.144 -0.144,0.157 -0.229,0.171 -0.414,0.07 -0.368,-0.198 -0.742,-0.391 -1.133,-0.602 0.01,0.148 0.03,0.269 0.04,0.391 0.09,0.773 0.175,1.546 0.265,2.315 l 0.283,2.464 0.149,1.281 c 0.01,0.108 -0.104,0.238 -0.216,0.238 h -2.176 c -0.112,0 -0.261,-0.13 -0.27,-0.243 l -0.189,-2.18 c -0.07,-0.769 -0.135,-1.538 -0.197,-2.302 -0.04,-0.413 -0.06,-0.832 -0.117,-1.245 -0.08,-0.58 -0.243,-1.138 -0.562,-1.641 -0.07,-0.112 -0.158,-0.22 -0.252,-0.315 -0.351,-0.341 -0.742,-0.287 -0.989,0.14 -0.184,0.319 -0.274,0.669 -0.292,1.034 -0.02,0.35 0,0.701 0.02,1.047 0.04,0.665 0.09,1.331 0.135,1.996 0.05,0.769 0.108,1.538 0.157,2.307 0.03,0.409 0.06,0.818 0.09,1.227 0,0.09 -0.07,0.171 -0.162,0.171 h -3.043 c -0.126,0 -0.247,-0.122 -0.251,-0.248 L 17.321,19.23 c -0.02,-0.8 -0.03,-1.596 -0.07,-2.396 -0.03,-0.62 -0.337,-1.119 -0.814,-1.501 -1.344,-1.079 -3.34,-0.333 -3.637,1.366 -0.05,0.31 -0.05,0.634 -0.06,0.953 -0.06,2.073 -0.113,4.145 -0.167,6.218 0,0.139 -0.126,0.256 -0.265,0.256 H 9.2874 c -0.108,0 -0.18,-0.08 -0.171,-0.184 0.09,-1.299 0.18,-2.599 0.265,-3.898 0.05,-0.764 0.117,-1.528 0.153,-2.297 0.02,-0.454 -0.06,-0.899 -0.261,-1.313 -0.03,-0.06 -0.07,-0.126 -0.117,-0.184 -0.251,-0.333 -0.57,-0.36 -0.885,-0.08 -0.189,0.166 -0.324,0.377 -0.436,0.602 -0.274,0.562 -0.409,1.16 -0.459,1.781 -0.06,0.782 -0.135,1.564 -0.202,2.346 l -0.175,2.055 -0.08,0.935 c 0,0.108 -0.158,0.238 -0.27,0.238 h -2.176 c -0.112,0 -0.225,-0.13 -0.211,-0.238 l 0.404,-3.529 c 0.108,-0.944 0.216,-1.888 0.319,-2.837 z m 1.138,-9.8896 c -0.229,0.166 -0.441,0.314 -0.647,0.472 -0.203,0.152 -0.396,0.314 -0.598,0.472 -0.153,0.121 -0.306,0.09 -0.459,0 -0.09,-0.06 -0.153,-0.18 -0.13,-0.324 0.07,-0.427 0.148,-0.854 0.22,-1.277 0.09,-0.548 0.18,-1.097 0.283,-1.645 0.04,-0.239 0.122,-0.463 0.283,-0.648 0.756,-0.872 1.664,-1.551 2.671,-2.099 1.367,-0.742 2.8276,-1.205 4.3516,-1.47 0.373,-0.06 0.75,-0.10803 1.124,-0.15803 0.152,-0.02 0.278,0.02 0.386,0.13503 0.104,0.108 0.207,0.216 0.315,0.319 0.144,0.135 0.198,0.302 0.189,0.495 -0.03,0.863 -0.06,1.726 -0.09,2.594 0,0.148 -0.184,0.319 -0.337,0.328 -0.18,0 -0.36,0.03 -0.539,0.05 -0.07,0 -0.09,0.04 -0.09,0.113 -0.03,1.164 -0.06,2.324 -0.09,3.488 0,0.158 0,0.31 -0.01,0.468 0,0.108 -0.08,0.184 -0.179,0.202 -0.684,0.1077 -1.363,0.2197 -2.046,0.3327 -0.135,0.02 -0.265,0.05 -0.396,0.08 -0.108,0.02 -0.229,-0.08 -0.22,-0.189 0.02,-0.3907 0.04,-0.7817 0.08,-1.1737 0.04,-0.485 0.03,-0.971 -0.1,-1.447 -0.05,-0.211 -0.1436,-0.423 -0.2606,-0.607 -0.202,-0.31 -0.458,-0.301 -0.732,-0.09 -0.23,0.17 -0.365,0.427 -0.486,0.683 -0.225,0.476 -0.328,0.984 -0.36,1.51 -0.04,0.589 -0.09,1.1777 -0.148,1.7627 0,0.1209 -0.09,0.2159 -0.202,0.2599 -0.67,0.257 -1.322,0.558 -1.96,0.891 -0.08,0.05 -0.126,0 -0.171,-0.04 a 0.13937,0.13937 0 0 1 -0.04,-0.108 c 0.1,-0.89 0.198,-1.7799 0.301,-2.6696 0.03,-0.22 0.05,-0.441 0.08,-0.688 z M 16.935,4.7884 c -0.216,-0.02 -0.423,-0.04 -0.634,-0.05 -0.126,0 -0.315,-0.185 -0.32,-0.315 -0.03,-0.89 -0.06,-1.785 -0.09,-2.675 0,-0.144 0.05,-0.274 0.144,-0.378 0.112,-0.116 0.229,-0.229 0.342,-0.35 0.116,-0.13103 0.26,-0.16703 0.431,-0.14903 1.29,0.14403 2.549,0.41403 3.768,0.86803 1.299,0.481 2.508,1.128 3.551,2.055 0.297,0.265 0.571,0.557 0.841,0.854 0.18,0.198 0.229,0.463 0.27,0.719 l 0.323,1.915 c 0.05,0.297 0.104,0.594 0.153,0.89 0.02,0.144 -0.03,0.27 -0.126,0.324 -0.198,0.113 -0.332,0.108 -0.467,0 -0.373,-0.288 -0.747,-0.566 -1.12,-0.85 -0.03,-0.03 -0.07,-0.04 -0.117,-0.08 0.02,0.211 0.05,0.405 0.07,0.598 0.09,0.773 0.175,1.5457 0.265,2.3146 0.02,0.14 0.04,0.274 0.05,0.414 0,0.04 0,0.09 -0.04,0.121 -0.05,0.05 -0.103,0.08 -0.193,0.04 -0.621,-0.288 -1.241,-0.567 -1.861,-0.855 -0.06,-0.03 -0.131,-0.06 -0.185,-0.107 -0.09,-0.07 -0.1,-0.1709 -0.108,-0.2789 -0.06,-0.7277 -0.1,-1.4607 -0.193,-2.1897 -0.07,-0.553 -0.27,-1.07 -0.629,-1.51 -0.09,-0.104 -0.212,-0.189 -0.338,-0.252 -0.184,-0.09 -0.346,-0.05 -0.49,0.104 -0.139,0.148 -0.22,0.328 -0.278,0.517 -0.104,0.319 -0.158,0.651 -0.153,0.984 0.01,0.517 0.05,1.03 0.07,1.547 0,0.07 0,0.1387 0.01,0.2057 0.03,0.113 -0.04,0.176 -0.112,0.239 -0.02,0.02 -0.06,0.02 -0.09,0.01 -0.75,-0.13 -1.501,-0.261 -2.256,-0.3907 l -0.198,-0.03 c -0.131,-0.02 -0.194,-0.09 -0.198,-0.22 -0.04,-1.308 -0.07,-2.617 -0.103,-3.925 0,-0.03 0,-0.06 0,-0.117 z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
        </Canvas>
        <Canvas x:Key="set_wonders" Clip="M0,0 V30 H30 V0 H0 Z">
            <Path Data="F1 m 15.135,0.86913 c -0.09,-0.011 -0.17,-0.002 -0.223,0.0332 C 13.342,1.9611 11.487,2.2594 9.7402,2.8594 8.4167,3.3182 7.2175,3.9362 6.5469,5.2598 v 0.3711 c 0.088,0.1941 0.2127,0.3879 0.248,0.582 0.2294,1.0941 0.6689,2.0123 1.4278,2.8594 1.0764,1.1997 2.1023,2.4717 3.0723,3.7597 0.388,0.529 0.616,1.217 0.775,1.834 0.194,0.794 -0.04,1.552 -0.529,2.188 -1.076,1.376 -2.1871,2.719 -3.2812,4.078 -0.053,0.07 -0.1419,0.122 -0.3184,0.281 l -0.1582,0.301 c -0.2118,0.46 -0.4252,0.883 -0.584,1.306 -0.1941,0.512 -0.4235,1.024 -0.4941,1.571 -0.053,0.316 0.1067,0.706 0.2832,0.988 0.547,0.88 1.377,1.36 2.3652,1.66 1.8705,0.563 3.7765,1.094 5.4705,2.1 h 0.74 c 0.724,-0.371 1.431,-0.794 2.19,-1.078 1.147,-0.44 2.327,-0.794 3.492,-1.182 0.953,-0.3 1.625,-0.952 2.207,-1.729 v -1.396 c -0.547,-2.01 -2.118,-3.334 -3.283,-4.922 -0.6,-0.831 -1.234,-1.661 -1.816,-2.508 -0.584,-0.865 -0.584,-1.747 0,-2.594 0.6,-0.865 1.216,-1.712 1.851,-2.541 0.635,-0.847 1.377,-1.6231 1.977,-2.5054 0.494,-0.7412 0.848,-1.5887 1.254,-2.3828 V 4.9961 C 22.959,4.1843 22.147,3.7247 21.406,3.2129 21.23,3.107 20.928,3.1781 20.734,3.0898 18.969,2.3134 17.013,2.0139 15.371,0.95507 15.309,0.91095 15.221,0.88016 15.135,0.86913 Z M 19.48,4.8398 c 0.718,-0.026 1.391,0.3939 1.749,1.3204 0.511,1.3235 -0.09,2.2776 -0.741,3.2128 -0.935,1.359 -1.976,2.611 -2.947,3.934 -1.006,1.377 -1.023,1.994 -0.05,3.388 0.9,1.254 1.887,2.454 2.752,3.725 0.459,0.671 0.831,1.43 1.131,2.207 0.282,0.706 0,1.359 -0.512,1.906 -0.723,0.759 -1.623,1.181 -2.646,1.305 -1.13,0.139 -2.278,0.175 -3.514,0.264 C 13.57,25.98 12.317,25.907 11.117,25.645 10.517,25.521 9.9355,25.113 9.459,24.707 8.5766,23.932 8.3999,22.926 9.0352,21.938 9.9528,20.49 10.977,19.113 11.947,17.701 c 0.441,-0.652 0.899,-1.306 1.375,-1.959 0.3,-0.441 0.302,-0.882 0.02,-1.304 C 12.671,13.45 12.019,12.46 11.295,11.525 10.395,10.361 9.5296,9.1772 8.9473,7.8184 8.5061,6.7948 9.0878,5.4374 10.111,4.9609 c 0.565,-0.247 1.112,-0.1242 1.588,0.1758 0.865,0.5294 1.712,1.0952 2.506,1.7305 0.935,0.7235 0.831,0.6865 1.731,-0.055 0.846,-0.7235 1.818,-1.34 2.824,-1.7988 0.238,-0.1059 0.481,-0.165 0.72,-0.1739 z m -5.304,3.961 c -0.975,0.029 -1.948,0.1387 -2.918,0.3769 -0.847,0.2118 -0.881,0.2999 -0.369,1.0233 0.776,1.077 1.728,1.96 2.787,2.772 0.335,0.247 0.653,0.512 0.953,0.83 0.123,0.141 0.23,0.368 0.23,0.562 0.04,1.306 0.04,2.631 0.02,3.936 0,0.196 -0.124,0.443 -0.283,0.566 -0.547,0.459 -1.129,0.866 -1.694,1.287 -1.111,0.831 -2.224,1.659 -3.3004,2.543 -0.5294,0.424 -0.529,0.756 -0.1055,1.305 0.5119,0.671 1.1649,1.113 2.0119,1.201 2.312,0.247 4.606,0.21 6.9,0.02 0.812,-0.07 1.5,-0.442 2.1,-0.971 0.618,-0.529 0.653,-0.99 0.05,-1.553 -0.512,-0.476 -1.076,-0.937 -1.641,-1.359 -0.971,-0.741 -1.942,-1.447 -2.912,-2.152 -0.282,-0.195 -0.371,-0.46 -0.371,-0.797 0,-1.43 0,-2.876 -0.02,-4.305 0,-0.335 0.106,-0.546 0.406,-0.74 1.306,-0.865 2.348,-1.995 3.231,-3.283 0.126,-0.2142 0.231,-0.4623 0.39,-0.7446 C 19.358,9.2478 19.182,9.1778 18.988,9.1602 18.37,9.0719 17.734,8.967 17.117,8.9141 16.129,8.8258 15.151,8.7721 14.176,8.8008 Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
        </Canvas>
        <Canvas x:Key="set_badlands" Clip="M0,0 V30 H30 V0 H0 Z">
            <Path Data="F1 M25 9.2c-.13-.65-.69-1.08-1.37-1.05-.6.02-1.04.3-1.29.86 0 .01-.01.02-.02.03h-.05c-.67-.15-1.34-.3-2.01-.44-1.21-.26-2.43-.52-3.64-.77a.49.49 0 0 1-.36-.26c-.77-1.34-1.55-2.68-2.32-4.02-.2-.34-.4-.68-.61-1.03l.03-.03c.4-.35.54-.79.47-1.31-.11-.79-.84-1.31-1.6-1.13-.5.12-.83.44-.97.93-.15.52-.08 1 .31 **********.09.09.14.13l-.03.06c-.33.57-.67 1.14-1 1.71-.66 1.15-1.32 2.3-1.99 3.44a.21.21 0 0 1-.11.07c-.22.05-.45.08-.67.13-1.57.34-3.15.68-4.72 1.03-.16.04-.32.07-.48.1-.05-.1-.09-.19-.15-.28-.24-.37-.6-.55-1.03-.61C.7 8.04-.04 8.68 0 9.5c.04.84.78 1.29 1.37 *********.57-.06.82-.23l.06.06c.45.51.9 1.02 1.36 ********** 1.74 1.93 2.61 *********.03.07.03.11l-.27 2.41c-.05.44-.08.89-.13 1.33-.05.48-.09.95-.14 1.43-.03.27-.05.55-.08.82-.04 0-.07 0-.1.01-.03 0-.07 0-.1.01-.57.12-.91.49-1.08 1.03-.21.68.14 1.39.79 ********** 1.65-.37 1.81-.96.07-.25.07-.5 0-.75.04-.02.08-.04.12-.05 1.71-.76 3.43-1.51 5.14-2.27.1-.05.21-.14.31-.13.11 0 .21.09.32.14 1.5.66 2.99 1.32 4.49 1.99.25.11.51.22.76.33v.03c-.13.57.04 1.04.47 1.42.42.37 1.01.46 1.5.2.49-.25.76-.77.69-1.33-.07-.53-.44-1.12-1.13-1.25l-.21-.03c-.02-.18-.03-.35-.05-.52-.1-1.01-.19-2.03-.3-3.04-.08-.81-.18-1.62-.27-2.43 0-.03.01-.07.03-.1.09-.1.18-.19.27-.28l3.35-3.74c.14-.15.27-.31.41-.46.02 0 .03.01.04.02.44.26.89.28 1.35.06.59-.28.9-.87.78-1.47Zm-8.4 5.66c-.1.29-.22.58-.34.87a.38.38 0 0 1-.09.13c-.69.57-1.41 1.11-2.18 1.57-.2.12-.42.15-.63.21-.66.17-1.33.31-2.01.39-.27.03-.54-.02-.81-.08s-.54-.12-.8-.18c-.41-.09-.75-.31-1.04-.6-.02-.01-.03-.03-.05-.06.05 0 .08-.02.12-.02L10 17c1.49-.14 2.82-.67 3.96-1.64.21-.18.41-.38.61-.58.13-.13.19-.28.17-.46-.05-.48-.09-.96-.14-1.44-.02-.23-.06-.45-.11-.67a.958.958 0 0 0-.12-.32c-.29-.48-.62-.92-.99-1.34-.05-.05-.1-.09-.15-.15-.12-.14-.27-.16-.44-.11-.8.19-1.55.48-2.25.92-.05.03-.1.05-.15.08-.14.09-.21.21-.24.37-.07.34-.14.68-.14 1.03 0 .3.1.54.37.7.25.15.51.26.79.33.25.06.47-.11.47-.37 0-.15-.04-.3-.08-.44-.04-.13-.11-.26-.16-.39a.401.401 0 0 1-.03-.15c0-.12.08-.18.18-.13.36.15.72.31 1.08.47.06.03.12.09.15.15.13.28.24.56.36.84.08.19.05.37-.07.54-.16.23-.31.45-.48.67-.1.13-.2.28-.32.38-.2.17-.44.29-.72.3-.28.01-.55.02-.83.02-.15 0-.3-.01-.44-.05a.766.766 0 0 1-.32-.14c-.53-.45-1.02-.93-1.5-1.43-.23-.25-.32-.54-.34-.86-.05-.65-.08-1.3-.04-1.96 0-.16.06-.28.18-.4.81-.78 1.72-1.42 2.72-1.93.49-.25 1-.47 1.51-.7.12-.05.23-.06.36-.02.87.26 1.71.58 2.45 1.14.19.15.37.31.56.47.25.21.39.49.46.82.17.8.29 1.6.38 2.41.03.24.05.48.07.73.04.4-.02.79-.15 1.17Z" Fill="{Binding Path=Fill, ElementName=DeckSetIcons}" />
        </Canvas>
    </UserControl.Resources>
    <WrapPanel Orientation="Horizontal">
        <WrapPanel.Resources>
            <Style TargetType="Rectangle">
                <Setter Property="Width" Value="16"/>
                <Setter Property="Height" Value="16"/>
                <Setter Property="Margin" Value="2,0"/>
            </Style>
            <deckSetIcons:SetIconVisibilityConverter x:Key="SetIconVisibilityConverter"/>
        </WrapPanel.Resources>
        <Rectangle Name="RectIconNaxx" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Naxx}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.NAXX}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_naxx}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconGvg" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Gvg}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.GVG}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_gvg}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconBrm" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Brm}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.BRM}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_brm}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconTgt" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Tgt}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.TGT}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_tgt}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconLoe" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Loe}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.LOE}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_loe}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconOg" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Wotog}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.OG}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_og}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconKara" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Kara}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.KARA}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_kara}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconMsg" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Msg}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.GANGS}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_msg}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconUngoro" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Ungoro}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.UNGORO}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_ungoro}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconIcecrown" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Icecrown}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.ICECROWN}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_icecrown}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconLoot" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Loot}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.LOOTAPALOOZA}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_loot}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconGilneas" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Gilneas}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.GILNEAS}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_gilneas}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconBoomsday" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Boomsday}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.BOOMSDAY}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_boomsday}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconTroll" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Trolls}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.TROLL}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_troll}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconDalaran" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Dalaran}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.DALARAN}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_dalaran}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconUldum" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Uldum}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.ULDUM}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_uldum}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconDragons" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Dragons}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.DRAGONS}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_dragons}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconYOTD" ToolTip="{lex:Loc DeckSetIcons_Tooltip_YOTD}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.YEAR_OF_THE_DRAGON}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_dragons}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconBlackTemple" ToolTip="{lex:Loc DeckSetIcons_Tooltip_BlackTemple}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.BLACK_TEMPLE}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_blacktemple}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconDemonHunterInitiate" ToolTip="{lex:Loc DeckSetIcons_Tooltip_DemonHunterInitiate}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.DEMON_HUNTER_INITIATE}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_dhi}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconScholomance" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Scholomance}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.SCHOLOMANCE}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_scholomance}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconDarkmoonFaire" ToolTip="{lex:Loc DeckSetIcons_Tooltip_DarkmoonFaire}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.DARKMOON_FAIRE}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_darkmoon}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconTheBarrens" ToolTip="{lex:Loc DeckSetIcons_Tooltip_TheBarrens}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.THE_BARRENS}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_barrens}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconWailingCaverns" ToolTip="{lex:Loc DeckSetIcons_Tooltip_WailingCaverns}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.WAILING_CAVERNS}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_caverns}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconLegacy" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Legacy}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.LEGACY}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_legacy}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconCore" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Core}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.CORE}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_core}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconStormwind" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Stormwind}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.STORMWIND}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_stormwind}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconAlterac" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Alterac}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.ALTERAC_VALLEY}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_alterac}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconSunkenCity" ToolTip="{lex:Loc DeckSetIcons_Tooltip_SunkenCity}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.THE_SUNKEN_CITY}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_sunken_city}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconNathria" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Nathria}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.REVENDRETH}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_nathria}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconLichKing" ToolTip="{lex:Loc DeckSetIcons_Tooltip_LichKing}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.RETURN_OF_THE_LICH_KING}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_lichking}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconPathOfArthas" ToolTip="{lex:Loc DeckSetIcons_Tooltip_PathOfArthas}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.PATH_OF_ARTHAS}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_path_of_arthas}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconFestivalOfLegends" ToolTip="{lex:Loc DeckSetIcons_Tooltip_FestivalOfLegends}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.BATTLE_OF_THE_BANDS}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_festival_of_legends}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconTitans" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Titans}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.TITANS}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_titans}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconWonders" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Wonders}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.WONDERS}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_wonders}" />
            </Rectangle.Fill>
        </Rectangle>
        <Rectangle Name="RectIconBadlands" ToolTip="{lex:Loc DeckSetIcons_Tooltip_Badlands}">
            <Rectangle.Visibility>
                <Binding Path="Deck" Converter="{StaticResource SetIconVisibilityConverter}" ConverterParameter="{x:Static enums:CardSet.WILD_WEST}"/>
            </Rectangle.Visibility>
            <Rectangle.Fill>
                <VisualBrush Stretch="Uniform" Visual="{StaticResource set_badlands}" />
            </Rectangle.Fill>
        </Rectangle>
    </WrapPanel>
</UserControl>
