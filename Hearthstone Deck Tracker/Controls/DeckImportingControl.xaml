<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.DeckImportingControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="300">
    <Grid>
        <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Visibility="{Binding TextVisibility}">
            <Label Content="{Binding Text}" FontWeight="SemiBold" FontSize="18" HorizontalAlignment="Center" Foreground="White"/>
            <Button Name="BtnStartHearthstone" Click="BtnStartHearthstone_Click" Margin="10,6,10,-4" Visibility="{Binding StartButtonVisibility}" Style="{StaticResource AccentedSquareButtonStyle}">
                <TextBlock Name="TextBlockBtnStartHearthstone" Text="{Binding ButtonStartHearthstoneText}" />
            </Button>
        </StackPanel>
        <DockPanel Visibility="{Binding ContentVisibility}">
            <StackPanel Orientation="Horizontal" DockPanel.Dock="Bottom" HorizontalAlignment="Center" Margin="0,10,0,30">
                <Button Name="ButtonImport" Width="150" Style="{DynamicResource AccentedSquareButtonStyle}" Click="ButtonImport_OnClick">
                    <TextBlock Text="{lex:Loc Importing_Constructed_Button_Import}"/>
                </Button>
                <Button Name="ButtonAutoImport" Width="150" Margin="10,0,0,0" Style="{DynamicResource SquareButtonStyle}" Click="ButtonAutoImport_OnClick"
                        Visibility="{Binding AutoImportingVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}">
                    <TextBlock Text="{lex:Loc Importing_Constructed_Button_AutoImporting}"/>
                </Button>
            </StackPanel>
            <Label Content="{lex:Loc Importing_Constructed_Label_Title}" DockPanel.Dock="Top" FontSize="18" HorizontalAlignment="Center" Foreground="White"/>
            <Border BorderThickness="1" BorderBrush="{DynamicResource AccentColorBrush}" HorizontalAlignment="Center" Margin="5">
                <DockPanel Margin="0,0,0,5">
                    <StackPanel Orientation="Horizontal" DockPanel.Dock="Top">
                        <CheckBox Name="CheckBoxImportAll" IsChecked="True" Margin="5,0,0,0" Click="CheckBoxImportAll_OnClicked"/>
                        <Label Content="{lex:LocTextUpper Importing_Constructed_Label_Deck}" Margin="90,0,0,0" FontWeight="SemiBold"/>
                        <Label Content="{lex:LocTextUpper Importing_Constructed_Label_SaveTo}" Margin="160,0,0,0" FontWeight="SemiBold"/>
                    </StackPanel>
                    <Separator DockPanel.Dock="Top"/>
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <ItemsControl Name="ItemsControl" ItemsSource="{Binding Decks}" Margin="0,0,5,0">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <CheckBox Name="CheckBoxImport" IsChecked="{Binding Import}" Margin="5,0,0,0" Unchecked="CheckBoxImport_OnUnchecked" Checked="CheckBoxImport_OnChecked">
                                        <DockPanel Height="30" IsEnabled="{Binding IsChecked, ElementName=CheckBoxImport}" >
                                            <Image Source="{Binding ClassImage}" Width="32" Height="32" RenderOptions.BitmapScalingMode="Fant"/>
                                            <TextBlock Text="{Binding Deck.Name}" VerticalAlignment="Center" Margin="5,0,0,0" FontWeight="SemiBold" FontSize="16" Width="200" TextTrimming="CharacterEllipsis"/>
                                            <ComboBox ItemsSource="{Binding ImportOptions}" SelectedIndex="{Binding SelectedIndex}" Width="170">
                                                <ComboBox.ItemTemplate>
                                                    <DataTemplate>
                                                        <TextBlock Text="{Binding DisplayName}"/>
                                                    </DataTemplate>
                                                </ComboBox.ItemTemplate>
                                            </ComboBox>
                                        </DockPanel>
                                    </CheckBox>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </DockPanel>
            </Border>
        </DockPanel>
    </Grid>
</UserControl>
