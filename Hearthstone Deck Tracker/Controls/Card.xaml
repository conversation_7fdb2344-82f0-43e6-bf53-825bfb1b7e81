﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Card"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hs="clr-namespace:Hearthstone_Deck_Tracker.Hearthstone"
             xmlns:tooltips="clr-namespace:Hearthstone_Deck_Tracker.Controls.Tooltips"
             xmlns:extensions="clr-namespace:Hearthstone_Deck_Tracker.Utility.Extensions"
             Loaded="Card_OnLoaded"
             Unloaded="Card_OnUnloaded"
             ToolTipService.Placement="Right"
             ToolTipService.InitialShowDelay="100"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance hs:Card}"
             extensions:OverlayExtensions.ToolTip="{x:Type tooltips:CardTooltip}"
             d:DesignHeight="300" d:DesignWidth="300">
    <Rectangle Fill="{Binding Background}" Height="34" Width="217" />
</UserControl>
