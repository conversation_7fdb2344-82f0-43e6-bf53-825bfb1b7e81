﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.CardTile"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:tooltips="clr-namespace:Hearthstone_Deck_Tracker.Controls.Tooltips"
             xmlns:extensions="clr-namespace:Hearthstone_Deck_Tracker.Utility.Extensions"
             xmlns:controls="clr-namespace:Hearthstone_Deck_Tracker.Controls"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             xmlns:battlegrounds="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds"
             xmlns:relatedCardsSystem="clr-namespace:Hearthstone_Deck_Tracker.Hearthstone.RelatedCardsSystem"
             ToolTipService.Placement="Right"
             ToolTipService.InitialShowDelay="100"
             mc:Ignorable="d"
             Loaded="CardTile_OnLoaded"
             Unloaded="CardTile_OnUnloaded"
             MaxHeight="34"
             d:DataContext="{d:DesignInstance controls:CardTileViewModel}"
             extensions:OverlayExtensions.ToolTip="{x:Type tooltips:CardTooltip}"
             d:DesignHeight="34" d:DesignWidth="217">
    <Grid>
        <Grid.Resources>
            <controls:MultiplyConverter x:Key="MultiplyConverter"/>
            <controls:MulliganMarginConverter x:Key="MulliganMarginConverter"/>
            <CubicEase x:Key="Ease" EasingMode="EaseOut"></CubicEase>
            <Storyboard x:Key="HighlightFadeIn">
                <DoubleAnimation Storyboard.TargetProperty="Opacity" To="1" BeginTime="0:0:0.2" Duration="0:0:0.2" EasingFunction="{StaticResource Ease}"/>
            </Storyboard>
            <Storyboard x:Key="HighlightFadeOut">
                <DoubleAnimation Storyboard.TargetProperty="Opacity" To="0" Duration="0:0:0.2" EasingFunction="{StaticResource Ease}" FillBehavior="Stop"/>
            </Storyboard>
            <Style x:Key="HighlightAnimation" TargetType="FrameworkElement">
                <Setter Property="Opacity" Value="0"/>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Highlight}" Value="{x:Static relatedCardsSystem:HighlightColor.Teal}">
                        <DataTrigger.EnterActions>
                            <BeginStoryboard Storyboard="{StaticResource HighlightFadeIn}"/>
                        </DataTrigger.EnterActions>
                        <DataTrigger.ExitActions>
                            <BeginStoryboard Storyboard="{StaticResource HighlightFadeOut}"/>
                        </DataTrigger.ExitActions>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding Highlight}" Value="{x:Static relatedCardsSystem:HighlightColor.Orange}">
                        <DataTrigger.EnterActions>
                            <BeginStoryboard Storyboard="{StaticResource HighlightFadeIn}"/>
                        </DataTrigger.EnterActions>
                        <DataTrigger.ExitActions>
                            <BeginStoryboard Storyboard="{StaticResource HighlightFadeOut}"/>
                        </DataTrigger.ExitActions>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding Highlight}" Value="{x:Static relatedCardsSystem:HighlightColor.Green}">
                        <DataTrigger.EnterActions>
                            <BeginStoryboard Storyboard="{StaticResource HighlightFadeIn}"/>
                        </DataTrigger.EnterActions>
                        <DataTrigger.ExitActions>
                            <BeginStoryboard Storyboard="{StaticResource HighlightFadeOut}"/>
                        </DataTrigger.ExitActions>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </Grid.Resources>

        <!-- Pre-rendered fallback for non-default card themes -->
        <Viewbox Visibility="{Binding IsPreRendered, Converter={StaticResource BoolToVisibility}}" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" StretchDirection="DownOnly" Stretch="Fill">
            <Grid Width="217" Height="34">
                <Rectangle Fill="{Binding PreRenderedCard}" />
                <Rectangle Fill="{Binding PreRenderedHighlight}" Style="{StaticResource HighlightAnimation}" />
            </Grid>
        </Viewbox>

        <Grid Visibility="{Binding IsPreRendered, Converter={StaticResource InverseBoolToVisibility}}">
            <Border BorderBrush="#000000" BorderThickness="1" Background="#313131">
                <Border.CacheMode>
                    <BitmapCache RenderAtScale="2"/>
                </Border.CacheMode>
                <DockPanel Name="Content">
                    <!-- Cost -->
                    <Border DockPanel.Dock="Left" BorderThickness="0,0,1,0" BorderBrush="#000000"
                            Visibility="{Binding IsCostVisible, Converter={StaticResource BoolToVisibility}}"
                            Width="{Binding ActualHeight, ElementName=Content}">
                        <Grid>
                            <Rectangle Fill="{Binding GemColorBrush}" Opacity=".5"/>
                            <hearthstoneDeckTracker:HearthstoneTextBlock
                                Text="{Binding Cost}" Margin="2" FontSize="20" HorizontalAlignment="Center"
                                FontStretch="Condensed" FontWeight="Normal" VerticalAlignment="Center"/>
                        </Grid>
                    </Border>

                    <!-- Count -->
                    <Border Name="CountContainer" DockPanel.Dock="Right" BorderThickness="1,0,0,0" BorderBrush="#000000"
                            Visibility="{Binding IsCountVisible, Converter={StaticResource BoolToVisibility}}"
                            Width="{Binding ActualHeight, ElementName=Content, Converter={StaticResource MultiplyConverter}, ConverterParameter=0.7}">
                        <Grid Margin="0,0,0,0">
                            <hearthstoneDeckTracker:HearthstoneTextBlock
                                Text="{Binding Count}" FontSize="17" TextAlignment="Center" Fill="#f0c348" HorizontalAlignment="Center" VerticalAlignment="Center"
                                FontStretch="Condensed" FontWeight="Normal" Visibility="{Binding IsCountTextVisible, Converter={StaticResource BoolToVisibility}}"/>
                            <Image Source="{StaticResource LegendaryStar}" RenderOptions.BitmapScalingMode="Fant"
                                   Visibility="{Binding IsLegendaryIconVisible, Converter={StaticResource BoolToVisibility}}"
                                   VerticalAlignment="Center" HorizontalAlignment="Center"
                                   Height="{Binding ActualHeight, ElementName=Content, Converter={StaticResource MultiplyConverter}, ConverterParameter=0.85}"
                                   Width="{Binding ActualHeight, ElementName=Content, Converter={StaticResource MultiplyConverter}, ConverterParameter=0.85}" />
                        </Grid>
                    </Border>

                    <!-- Art & Everything on top of it-->
                    <Grid ClipToBounds="True">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="8*"/>
                        </Grid.ColumnDefinitions>
                        <Grid Grid.Column="1">
                            <Grid.OpacityMask>
                                <LinearGradientBrush StartPoint="0,1" EndPoint="0.5,0">
                                    <GradientStop Color="#00000000" Offset="0.45"/>
                                    <GradientStop Color="#000000" Offset="1"/>
                                </LinearGradientBrush>
                            </Grid.OpacityMask>
                            <Grid ClipToBounds="True">
                                <Image Source="{Binding Asset}" RenderOptions.BitmapScalingMode="Fant"
                                       Stretch="UniformToFill" HorizontalAlignment="Right" VerticalAlignment="Center">
                                    <Image.Style>
                                        <Style TargetType="Image">
                                            <!-- Some card tile images have a white strip on the right edge. Negative right margin hides it. -->
                                            <Setter Property="Margin" Value="0,0,-10,0"></Setter>
                                            <!-- Battlegrounds Cards are not meant to be rendered as card tiles and have more white on the left -->
                                            <!-- Mind Muck is one of the worst examples -->
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Card.BaconCard}" Value="True">
                                                    <Setter Property="Margin" Value="-38,0,-10,0"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Image.Style>
                                </Image>
                            </Grid>
                        </Grid>
                        <DockPanel Grid.Column="0" Grid.ColumnSpan="2">

                            <!-- Mulligan Winrate -->
                            <Grid DockPanel.Dock="Right" VerticalAlignment="Center" Visibility="{Binding MulliganText, Converter={StaticResource NullableToVisibility}}">
                                <Grid.Style>
                                    <Style TargetType="Grid">
                                        <Setter Property="Margin" Value="0,0,4,0"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsVisible, ElementName=CountContainer}" Value="False">
                                                <Setter Property="Margin" Value="{Binding ActualHeight, ElementName=Content, Converter={StaticResource MulliganMarginConverter}, ConverterParameter=0.7}"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Grid.Style>
                                <Border CornerRadius="7" Background="{Binding MulliganBackground}" VerticalAlignment="Stretch"
                                        BorderBrush="{Binding MulliganBorderColor}" BorderThickness="{Binding MulliganBorderThickness}" />
                                <hearthstoneDeckTracker:HearthstoneTextBlock
                                    Text="{Binding MulliganText}" Margin="6,4" Fill="{Binding MulliganTextColor}" VerticalAlignment="Center"
                                    FontFamily="{Binding NameFontFamily}" FontSize="15" FontStretch="Condensed" FontWeight="{Binding NameFontWeight}" />
                            </Grid>

                            <!-- This icon is probably small enough that we don't need to scale it -->
                            <Image DockPanel.Dock="Right" Source="{StaticResource IconCreated}" Visibility="{Binding IsCreatedIconVisible, Converter={StaticResource BoolToVisibility}}" Height="15" Margin="4,0"/>

                            <!-- Battlegrounds Spell Cost -->
                            <Grid DockPanel.Dock="Right" Visibility="{Binding IsBaconSpell, Converter={StaticResource BoolToVisibility}}">
                                <Image Source="{StaticResource CoinCost}"
                                    Width="{Binding ActualHeight, ElementName=Content, Converter={StaticResource MultiplyConverter}, ConverterParameter=.75}"
                                    Height="{Binding ActualHeight, ElementName=Content, Converter={StaticResource MultiplyConverter}, ConverterParameter=.75}" />

                                <hearthstoneDeckTracker:HearthstoneTextBlock Text="{Binding Cost}" FontSize="17" VerticalAlignment="Center" HorizontalAlignment="Center" />
                            </Grid>

                            <!-- Card Name -->
                            <hearthstoneDeckTracker:HearthstoneTextBlock
                                Text="{Binding Name}" Margin="4,0,8,0" Fill="{Binding NameColor}"  VerticalAlignment="Center"
                                FontFamily="{Binding NameFontFamily}" FontSize="13" FontStretch="Condensed" FontWeight="{Binding NameFontWeight}" />
                        </DockPanel>
                    </Grid>
                </DockPanel>
            </Border>

            <Border BorderBrush="{Binding BorderColorBrush}" BorderThickness="1" Opacity="{Binding BorderOpacity}" Margin="1"/>

            <!-- "Faded out" darken overlay -->
            <Grid Background="#000000" Opacity="0.6" Visibility="{Binding IsDarkened, Converter={StaticResource BoolToVisibility}}"/>

            <!-- Related Cards Highlight -->
            <Grid ClipToBounds="True"  Style="{StaticResource HighlightAnimation}">
                <Border BorderBrush="{Binding HighlightBrush}" BorderThickness="1" Opacity="0.6" />
                <Grid Margin="1" ClipToBounds="True">
                    <Rectangle Fill="{Binding HighlightBrush}" Opacity="0.08" />
                    <Border BorderBrush="{Binding HighlightBrush}" BorderThickness="6" Margin="-2" CornerRadius="5" Opacity="0.5">
                        <Border.Effect>
                            <BlurEffect Radius="7"/>
                        </Border.Effect>
                    </Border>
                    <Border BorderBrush="{Binding HighlightBrush}" BorderThickness="3" Margin="-2" CornerRadius="4"/>
                </Grid>
            </Grid>
        </Grid>
    </Grid>
</UserControl>
