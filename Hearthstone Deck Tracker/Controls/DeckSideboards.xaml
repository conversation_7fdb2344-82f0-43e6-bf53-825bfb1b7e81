﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.DeckSideboards"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <StackPanel Name="Container"  Width="221">
        <StackPanel Name="ETCContainer" Width="221">
            <Border Background="#23272A" BorderBrush="#141617" BorderThickness="2,2,2,0" CornerRadius="3,3,0,0" Width="Auto" HorizontalAlignment="Left" Panel.ZIndex="1" Margin="2,5,2,0">
                <StackPanel Orientation="Horizontal" Margin="5,2" HorizontalAlignment="Left">
                    <TextBlock Text="{lex:Loc DeckSideboard_Label_ETCBand}"
                        FontWeight="SemiBold" Foreground="White" FontSize="14" Margin="5,0,0,0" />
                </StackPanel>
            </Border>
            <Border Background="#23272A" BorderBrush="#141617" BorderThickness="2" Margin="2,-2,2,5" Padding="1">
                <local:AnimatedCardList x:Name="CardList" Width="209"/>
            </Border>
        </StackPanel>
    </StackPanel>
</UserControl>
