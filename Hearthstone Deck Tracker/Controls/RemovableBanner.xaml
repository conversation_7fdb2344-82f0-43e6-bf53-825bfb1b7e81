﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.RemovableBanner"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="300">
    <UserControl.Template>
        <ControlTemplate TargetType="UserControl">
            <Button Name="Banner" DockPanel.Dock="Top" BorderThickness="1" HorizontalContentAlignment="Stretch"
                    Style="{DynamicResource SquareButtonStyle}" Cursor="Hand" Padding="1"
                    Click="Banner_Click">
                <Grid Background="{Binding Background, RelativeSource={RelativeSource TemplatedParent}}">
                    <ContentPresenter/>
                    <Button VerticalAlignment="Center" Height="24" Width="24" HorizontalAlignment="Right" Opacity="0.5"
                            Style="{DynamicResource MetroCircleButtonStyle}" Background="Transparent"
                            Margin="0,0,5,0" BorderThickness="0" Click="CloseButton_Click"
                            Visibility="{Binding Removable, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BoolToVisibility}}">
                        <TextBlock Text="❌" Foreground="{Binding Foreground, RelativeSource={RelativeSource TemplatedParent}}"/>
                    </Button>
                </Grid>
            </Button>
        </ControlTemplate>
    </UserControl.Template>
</UserControl>
