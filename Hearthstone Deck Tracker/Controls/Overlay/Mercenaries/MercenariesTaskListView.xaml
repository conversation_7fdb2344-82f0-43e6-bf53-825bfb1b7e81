﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Mercenaries.MercenariesTaskListView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Mercenaries"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <StackPanel>
        <ItemsControl ItemsSource="{Binding Tasks}">
            <ItemsControl.ItemTemplate>
                <DataTemplate>
                    <local:MercenariesTaskView Margin="0,4,0,0" />
                </DataTemplate>
            </ItemsControl.ItemTemplate>
            <ItemsControl.ItemsPanel>
                <ItemsPanelTemplate>
                    <StackPanel />
                </ItemsPanelTemplate>
            </ItemsControl.ItemsPanel>
        </ItemsControl>
        <Border Background="#221717" BorderBrush="#110C0C" BorderThickness="2" CornerRadius="3" Margin="55,4,0,0" Visibility="{Binding GameNoticeVisibility}">
            <TextBlock Text="{lex:Loc MercenariesTaskList_GameNotice}" Foreground="White" Opacity="0.7" TextAlignment="Center" Margin="8" FontSize="14"/>
        </Border>
    </StackPanel>
</UserControl>
