﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Mercenaries.MercenariesTaskView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Mercenaries"
             xmlns:hearthstone_deck_tracker="clr-namespace:Hearthstone_Deck_Tracker"
             mc:Ignorable="d">
     <UserControl.Resources>
        <local:ProgressBarFillWidthConverter x:Key="ProgressBarFillWidthConverter"/>
    </UserControl.Resources>
        <Grid>
            <Border Background="#221717" BorderBrush="#110C0C" BorderThickness="2" CornerRadius="3" Margin="50,0,0,0">
                <DockPanel Margin="50,8,8,8" MinWidth="280">
                    <TextBlock Text="{Binding Title}" Foreground="White" FontWeight="SemiBold" FontSize="18" DockPanel.Dock="Top" />
                    <Grid DockPanel.Dock="Bottom" Margin="0,4,0,0">
                        <Border Name="ProgressBar" Background="#110C0C" CornerRadius="3">
                            <Border Background="#6E1E1E" CornerRadius="3" HorizontalAlignment="Left">
                                <Border.Width>
                                    <MultiBinding Converter="{StaticResource ProgressBarFillWidthConverter}">
                                        <Binding Path="Progress"/>
                                        <Binding Path="ActualWidth" ElementName="ProgressBar"/>
                                    </MultiBinding>
                                </Border.Width>
                            </Border>
                        </Border>
                        <hearthstone_deck_tracker:HearthstoneTextBlock Text="{Binding ProgressText}" Fill="#FFF" FontSize="16" Margin="0,2" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Grid>
                    <TextBlock Text="{Binding Description}" Foreground="White" TextWrapping="Wrap" Margin="0,4" FontSize="14"/>
                </DockPanel>
            </Border>
            <Ellipse Width="80" Height="104" Fill="#221717" Margin="10,0,0,0" HorizontalAlignment="Left"/>
            <Grid Width="100" Height="100" HorizontalAlignment="Left" Margin="0,2">

            <Image Source="{Binding CardPortrait.Asset}" Height="110" Width="110" Margin="-5,-5,0,0" RenderOptions.BitmapScalingMode="Fant">
                    <Image.Clip>
                        <EllipseGeometry Center="55,55" RadiusX="35" RadiusY="47"/>
                    </Image.Clip>
               </Image>
                <Image Source="{StaticResource MercsFrame}" Height="100" Width="100" RenderOptions.BitmapScalingMode="Fant"/>
            </Grid>
        </Grid>
</UserControl>
