﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.WotogCounter"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             mc:Ignorable="d" Height="100" Width="{Binding IconWidth, RelativeSource={RelativeSource AncestorType=UserControl}}">
    <UserControl.Resources>
        <Style TargetType="TextBlock">
            <Setter Property="Foreground" Value="#3F3424"/>
            <Setter Property="FontFamily" Value="/HearthstoneDeckTracker;component/Resources/#Chunkfive"/>
            <Setter Property="FontSize" Value="30"/>
            <Setter Property="Width" Value="50"/>
            <Setter Property="Height" Value="45"/>
            <Setter Property="TextAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>
    <StackPanel Orientation="Horizontal">
        <Grid Visibility="{Binding JadeVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="2,0,2,0">
            <Image Source="/HearthstoneDeckTracker;component/Images/icon-jade.png" RenderOptions.BitmapScalingMode="Fant"/>
            <TextBlock Text="{Binding Jade, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="48,15,0,40"/>
            <TextBlock Text="{Binding Jade, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="48,53,0,2"/>
        </Grid>
        <Grid Visibility="{Binding CthunVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="2,0,2,0">
            <Image Source="/HearthstoneDeckTracker;component/Images/icon-cthun.png" RenderOptions.BitmapScalingMode="Fant"/>
            <TextBlock Text="{Binding Attack, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="48,15,0,40"/>
            <TextBlock Text="{Binding Health, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="48,53,0,2"/>
        </Grid>
        <Grid Visibility="{Binding SpellsVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="2,0,2,0">
            <Image Source="/HearthstoneDeckTracker;component/Images/icon-spells.png" RenderOptions.BitmapScalingMode="Fant"/>
            <TextBlock Text="{Binding Spells, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="48,34,0,21"/>
        </Grid>
        <Grid Visibility="{Binding PogoHopperVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="2,0,2,0">
            <Image Source="/HearthstoneDeckTracker;component/Images/icon-pogo.png" RenderOptions.BitmapScalingMode="Fant"/>
            <TextBlock Text="{Binding PogoHopper, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="48,15,0,40"/>
            <TextBlock Text="{Binding PogoHopper, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="48,53,0,2"/>
        </Grid>
        <Grid Visibility="{Binding GalakrondVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="2,0,2,0">
            <Image Source="/HearthstoneDeckTracker;component/Images/icon-galakrond.png" RenderOptions.BitmapScalingMode="Fant"/>
            <TextBlock Text="{Binding Galakrond, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="64,34,0,21"/>
        </Grid>
        <Grid Visibility="{Binding LibramVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="2,0,2,0">
            <Image Source="/HearthstoneDeckTracker;component/Images/icon-libram.png" RenderOptions.BitmapScalingMode="Fant"/>
            <TextBlock Text="{Binding Libram, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="64,34,0,21"/>
        </Grid>
        <Grid Visibility="{Binding AbyssalVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="2,0,2,0">
            <Image Source="/HearthstoneDeckTracker;component/Images/icon-abyssal.png" RenderOptions.BitmapScalingMode="Fant"/>
            <TextBlock Text="{Binding AbyssalCurse, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="64,34,0,21"/>
        </Grid>
        <Grid Visibility="{Binding ExcavateVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="2,0,2,0">
            <Image Source="/HearthstoneDeckTracker;component/Images/icon-excavate.png" RenderOptions.BitmapScalingMode="Fant"/>
            <TextBlock Text="{Binding Excavate, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="64,34,0,21"/>
        </Grid>
        <Grid Visibility="{Binding ExcavateTierVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="2,0,2,0">
            <Image Source="/HearthstoneDeckTracker;component/Images/icon-excavate-tier.png" RenderOptions.BitmapScalingMode="Fant"/>
            <hearthstoneDeckTracker:CounterTextBlock Text="{Binding ExcavateTierLabel, RelativeSource={RelativeSource AncestorType=UserControl}}" Width="120" TextAlignment="Center" Margin="83,28,28,27"/>
        </Grid>
        <Grid Visibility="{Binding SpellSchoolsVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}" Margin="2,0,2,0">
            <Image Source="/HearthstoneDeckTracker;component/Images/icon-spell-schools.png" RenderOptions.BitmapScalingMode="Fant"/>
            <hearthstoneDeckTracker:CounterTextBlock Text="{Binding SpellSchoolsLabel, RelativeSource={RelativeSource AncestorType=UserControl}}" Width="166" TextAlignment="Center" Margin="92,19,36,17"/>
        </Grid>
    </StackPanel>
</UserControl>
