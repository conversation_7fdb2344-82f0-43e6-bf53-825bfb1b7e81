﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.LinkOpponentDeckPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             DataContext="{Binding RelativeSource={RelativeSource Self}}"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d">
    <Border Background="#23272A" BorderBrush="#141617" BorderThickness="1" CornerRadius="3" Margin="0,5,0,0" MaxWidth="352">
        <Grid>
            <StackPanel Margin="10">
                <TextBlock Text="{lex:Loc LinkOpponentDeck_Panel_Title}" Foreground="White" FontSize="14" FontWeight="Bold"/>

                <TextBlock Foreground="White" FontSize="14" TextWrapping="Wrap" Visibility="{Binding DescriptorVisibility, Mode=OneWay}" Margin="0 3 0 0" Opacity=".7">
                        <Run Text="{lex:Loc LinkOpponentDeck_Panel_Description}"/>
                </TextBlock>

                <Button Content="{lex:Loc LinkOpponentDeck_Panel_LinkDeckButton}" Margin="0 6 0 0"
                    Click="LinkOpponentDeck_Click"  DockPanel.Dock="Right" />

                <TextBlock FontSize="14" TextWrapping="Wrap" Margin="0 10 0 0" Visibility="{Binding LinkMessageVisibility}" Opacity=".7" HorizontalAlignment="Center">
                    <Hyperlink Foreground="White">
                       <Run Text="{Binding LinkMessage, Mode=OneWay}" MouseDown="Hyperlink_MouseDown"/>
                    </Hyperlink>
                </TextBlock>

                <TextBlock Foreground="Red" FontSize="14" TextWrapping="Wrap" Visibility="{Binding ErrorMessageVisibility}" Margin="0 10 0 0" >
                        <Run Text="{Binding ErrorMessage, Mode=OneWay}"/>
                </TextBlock>

            </StackPanel>
        </Grid>
    </Border>
</UserControl>
