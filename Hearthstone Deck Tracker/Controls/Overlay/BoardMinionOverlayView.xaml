﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.BoardMinionOverlayView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:utility="clr-namespace:Hearthstone_Deck_Tracker.Utility"
             xmlns:mercenaries="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Mercenaries"
             mc:Ignorable="d" 
             x:Name="BoardMinionOverlay"
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid Visibility="{Binding Visibility}">
        <Ellipse Name="Ellipse" Margin="{Binding Margin}" Width="{Binding Width}" Height="{Binding Height}" />
        <utility:IgnoreSizeDecorator>
            <Canvas VerticalAlignment="{Binding VerticalAlignment}" HorizontalAlignment="Center" Width="{Binding AbilityPanelWidth}" Visibility="{Binding AbilitiesVisibility}">
                <ItemsControl ItemsSource="{Binding MercenariesAbilities}" Canvas.Top="{Binding AbilityPanelTopMargin}" Canvas.Left="0">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <mercenaries:MercenariesAbilityView Width="{Binding DataContext.AbilitySize, Source={x:Reference BoardMinionOverlay}}"
                                                                Height="{Binding DataContext.AbilitySize, Source={x:Reference BoardMinionOverlay}}"/>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Top"/>
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                </ItemsControl>
            </Canvas>
        </utility:IgnoreSizeDecorator>
    </Grid>
</UserControl>
