﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Constructed.Mulligan.OverlayMessage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Constructed.Mulligan"
             mc:Ignorable="d"
             Visibility="{Binding Visibility}">
    <d:UserControl.DataContext>
        <local:OverlayMessageViewModel Text="Loading..." />
    </d:UserControl.DataContext>
    <Border BorderBrush="{StaticResource HSReplayNetBlue}" Background="{StaticResource Tier7Black}" BorderThickness="1"
            CornerRadius="5">
        <StackPanel Orientation="Horizontal">
            <Border Background="{StaticResource HSReplayNetBlue}"
                    BorderThickness="1" CornerRadius="5 0 0 5" Margin="-1" Padding="5 5 4 4">
                <Image Source="{StaticResource HsReplayIconWhite}" Width="13" Height="16"
                       HorizontalAlignment="Left" RenderOptions.BitmapScalingMode="Fant"
                       Margin="1,0,0,0" />
            </Border>
            <TextBlock Foreground="White" Text="{Binding Text}" Margin="8 4 8 5" VerticalAlignment="Center" />
        </StackPanel>
    </Border>
</UserControl>
