﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Constructed.Mulligan.ConstructedMulliganSingleCardHeader"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:extensions="clr-namespace:Hearthstone_Deck_Tracker.Utility.Extensions"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d"
             d:DesignHeight="53" d:DesignWidth="212">
    <UserControl.Resources>
        <Style x:Key="DefaultTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="FontSize" Value="10" />
        </Style>
        <Style x:Key="StatsTextStyle" TargetType="TextBlock" BasedOn="{StaticResource DefaultTextStyle}">
            <Setter Property="FontFamily" Value="/HearthstoneDeckTracker;component/Resources/#Chunkfive"/>
            <Setter Property="FontSize" Value="16" />
        </Style>
        <Style x:Key="BoldTextStyle" TargetType="TextBlock" BasedOn="{StaticResource DefaultTextStyle}">
            <Setter Property="FontFamily" Value="/HearthstoneDeckTracker;component/Resources/#Chunkfive"/>
            <Setter Property="FontSize" Value="20" />
        </Style>
        <Style TargetType="TextBlock" BasedOn="{StaticResource DefaultTextStyle}"/>
    </UserControl.Resources>
    <Grid Height="53" Width="212">
        <Grid.RowDefinitions>
            <RowDefinition Height="19"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="82"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <Image Grid.RowSpan="2" Grid.ColumnSpan="3">
            <Image.Source>
                <DrawingImage>
                    <DrawingImage.Drawing>
                        <!--<DrawingGroup ClipGeometry="M0,0 V61 H243 V0 H0 Z">
                            <!- Rank ->
                            <GeometryDrawing Brush="{Binding RankGradient}" Geometry="F1 M243,61z M0,0z M95,4C95,1.79086,96.7909,0,99,0L144,0C146.209,0,148,1.79086,148,4L148,49C148,51.2091,146.209,53,144,53L99,53C96.7909,53,95,51.2091,95,49L95,4z" />
                            <GeometryDrawing Geometry="F1 M243,61z M0,0z M99,0.5L144,0.5C145.933,0.5,147.5,2.067,147.5,4L147.5,49C147.5,50.933,145.933,52.5,144,52.5L99,52.5C97.067,52.5,95.5,50.933,95.5,49L95.5,4C95.5,2.067,97.067,0.5,99,0.5z">
                                <GeometryDrawing.Pen>
                                    <Pen Thickness="1" StartLineCap="Flat" EndLineCap="Flat" LineJoin="Miter">
                                        <Pen.Brush>
                                            <SolidColorBrush Color="#FF000000" Opacity="0.18" />
                                        </Pen.Brush>
                                    </Pen>
                                </GeometryDrawing.Pen>
                            </GeometryDrawing>

                            <!- Left ->
                            <GeometryDrawing Brush="#FF141617" Geometry="F1 M243,61z M0,0z M58.0589,60.5L4,60.5C2.067,60.5,0.5,58.933,0.5,57L0.5,4C0.5,2.067,2.067,0.5,4,0.5L85,0.5C86.933,0.5,88.5,2.067,88.5,4L88.5,37.6269C88.5,40.181 87.1951,42.5617 85.0485,43.9512 73.4888,51.434 65.914,56.6721 62.4836,59.0877 61.1843,60.0026 59.6436,60.5 58.0589,60.5z"/>
                            <GeometryDrawing Brush="{StaticResource HSReplayNetBlue}" Geometry="F1 M243,61z M0,0z M0,4C0,1.79086,1.79086,0,4,0L85,0C87.2091,0,89,1.79086,89,4L89,22 0,22 0,4z" />
                            <GeometryDrawing Geometry="F1 M243,61z M0,0z M58.0589,60.5L4,60.5C2.067,60.5,0.5,58.933,0.5,57L0.5,4C0.5,2.067,2.067,0.5,4,0.5L85,0.5C86.933,0.5,88.5,2.067,88.5,4L88.5,37.6269C88.5,40.181 87.1951,42.5617 85.0485,43.9512 73.4888,51.434 65.914,56.6721 62.4836,59.0877 61.1843,60.0026 59.6436,60.5 58.0589,60.5z">
                                <GeometryDrawing.Pen>
                                    <Pen Brush="{StaticResource HSReplayNetBlue}" Thickness="1" StartLineCap="Flat" EndLineCap="Flat" LineJoin="Miter" />
                                </GeometryDrawing.Pen>
                            </GeometryDrawing>

                            <!- Right ->
                            <GeometryDrawing Brush="#FF141617" Geometry="F1 M243,61z M0,0z M187.075,60.5L239,60.5C240.933,60.5,242.5,58.933,242.5,57L242.5,4C242.5,2.067,240.933,0.5,239,0.5L158,0.5C156.067,0.5,154.5,2.067,154.5,4L154.5,35.6269C154.5,38.1807 155.805,40.5601 157.948,41.9578 169.997,49.817 178.643,56.1079 182.462,58.9542 183.799,59.9508 185.413,60.5 187.075,60.5z"/>
                            <GeometryDrawing Brush="{StaticResource HSReplayNetBlue}" Geometry="F1 M243,61z M0,0z M154,4C154,1.79086,155.791,0,158,0L239,0C241.209,0,243,1.79086,243,4L243,22 154,22 154,4z" />
                            <GeometryDrawing Geometry="F1 M243,61z M0,0z M187.075,60.5L239,60.5C240.933,60.5,242.5,58.933,242.5,57L242.5,4C242.5,2.067,240.933,0.5,239,0.5L158,0.5C156.067,0.5,154.5,2.067,154.5,4L154.5,35.6269C154.5,38.1807 155.805,40.5601 157.948,41.9578 169.997,49.817 178.643,56.1079 182.462,58.9542 183.799,59.9508 185.413,60.5 187.075,60.5z">
                                <GeometryDrawing.Pen>
                                    <Pen Brush="{StaticResource HSReplayNetBlue}" Thickness="1" StartLineCap="Flat" EndLineCap="Flat" LineJoin="Miter" />
                                </GeometryDrawing.Pen>
                            </GeometryDrawing>->
                        </DrawingGroup>-->

                        <DrawingGroup ClipGeometry="M0,0 V53 H212 V0 H0 Z">
                            <GeometryDrawing Brush="#FF141617">
                                <GeometryDrawing.Geometry>
                                    <RectangleGeometry RadiusX="3.5637" RadiusY="3.5637" Rect="147.436,0.4363,64.1274,52.1274" />
                                </GeometryDrawing.Geometry>
                            </GeometryDrawing>
                            <GeometryDrawing>
                                <GeometryDrawing.Pen>
                                    <Pen Brush="{StaticResource HSReplayNetBlue}" Thickness="0.872587" StartLineCap="Flat" EndLineCap="Flat" LineJoin="Miter" />
                                </GeometryDrawing.Pen>
                                <GeometryDrawing.Geometry>
                                    <RectangleGeometry RadiusX="3.5637" RadiusY="3.5637" Rect="147.436,0.4363,64.1274,52.1274" />
                                </GeometryDrawing.Geometry>
                            </GeometryDrawing>
                            <GeometryDrawing Brush="{StaticResource HSReplayNetBlue}" Geometry="F1 M212,53z M0,0z M147,3.49035C147,1.56268,148.563,0,150.49,0L208.51,0C210.437,0,212,1.56268,212,3.49035L212,17.4903 147,17.4903 147,3.49035z" />
                            <!--<GeometryDrawing Brush="#FFFFFFFF" Geometry="F1 M212,53z M0,0z M160.891,12.7452L159.582,12.7452 157.132,9.3002 156.399,9.90753 156.399,12.7452 155.268,12.7452 155.268,5.26885 156.399,5.26885 156.399,8.88136C156.58,8.66496 156.762,8.45205 156.943,8.24263 157.125,8.03321 157.306,7.82379 157.488,7.61437L159.509,5.26885 160.807,5.26885 157.949,8.53582 160.891,12.7452z M163.978,6.99658C164.474,6.99658 164.9,7.10129 165.256,7.31071 165.612,7.51315 165.884,7.80634 166.073,8.19027 166.268,8.57421 166.366,9.02796 166.366,9.55151L166.366,10.1588 162.575,10.1588C162.589,10.7382 162.739,11.1815 163.025,11.4887 163.319,11.7958 163.727,11.9494 164.251,11.9494 164.614,11.9494 164.935,11.918 165.214,11.8551 165.5,11.7853 165.793,11.6841 166.093,11.5515L166.093,12.4729C165.814,12.5986 165.528,12.6928 165.235,12.7556 164.949,12.8185 164.607,12.8499 164.209,12.8499 163.664,12.8499 163.183,12.7417 162.764,12.5253 162.345,12.3089 162.017,11.9878 161.779,11.562 161.549,11.1291 161.434,10.5986 161.434,9.97035 161.434,9.33511 161.539,8.7976 161.748,8.35781 161.964,7.91803 162.261,7.58295 162.638,7.35259 163.022,7.11525 163.469,6.99658 163.978,6.99658z M163.978,7.8552C163.58,7.8552 163.259,7.98434 163.015,8.24263 162.778,8.50092 162.638,8.8674 162.596,9.34209L165.256,9.34209C165.256,9.0489 165.21,8.79061 165.12,8.56723 165.029,8.34385 164.889,8.16933 164.701,8.04368 164.512,7.91803 164.272,7.8552 163.978,7.8552z M169.971,6.99658C170.466,6.99658 170.892,7.10129 171.248,7.31071 171.604,7.51315 171.876,7.80634 172.065,8.19027 172.26,8.57421 172.358,9.02796 172.358,9.55151L172.358,10.1588 168.567,10.1588C168.581,10.7382 168.731,11.1815 169.018,11.4887 169.311,11.7958 169.719,11.9494 170.243,11.9494 170.606,11.9494 170.927,11.918 171.206,11.8551 171.492,11.7853 171.786,11.6841 172.086,11.5515L172.086,12.4729C171.806,12.5986 171.52,12.6928 171.227,12.7556 170.941,12.8185 170.599,12.8499 170.201,12.8499 169.656,12.8499 169.175,12.7417 168.756,12.5253 168.337,12.3089 168.009,11.9878 167.772,11.562 167.541,11.1291 167.426,10.5986 167.426,9.97035 167.426,9.33511 167.531,8.7976 167.74,8.35781 167.957,7.91803 168.253,7.58295 168.63,7.35259 169.014,7.11525 169.461,6.99658 169.971,6.99658z M169.971,7.8552C169.573,7.8552 169.252,7.98434 169.007,8.24263 168.77,8.50092 168.63,8.8674 168.588,9.34209L171.248,9.34209C171.248,9.0489 171.203,8.79061 171.112,8.56723 171.021,8.34385 170.882,8.16933 170.693,8.04368 170.505,7.91803 170.264,7.8552 169.971,7.8552z M176.56,6.99658C177.237,6.99658 177.781,7.2409 178.193,7.72955 178.612,8.2182 178.821,8.94768 178.821,9.918 178.821,10.5532 178.727,11.0908 178.539,11.5305 178.35,11.9703 178.085,12.3019 177.743,12.5253 177.401,12.7417 177.003,12.8499 176.549,12.8499 176.263,12.8499 176.012,12.815 175.795,12.7452 175.579,12.6684 175.394,12.5707 175.24,12.452 175.094,12.3263 174.965,12.1937 174.853,12.0541L174.78,12.0541C174.8,12.1797 174.818,12.3298 174.832,12.5043 174.846,12.6719 174.853,12.822 174.853,12.9546L174.853,15.2582 173.743,15.2582 173.743,7.10129 174.643,7.10129 174.8,7.8552 174.853,7.8552C174.965,7.70163 175.097,7.55852 175.251,7.42589 175.404,7.29326 175.589,7.18854 175.806,7.11176 176.022,7.03497 176.273,6.99658 176.56,6.99658z M176.298,7.90756C175.949,7.90756 175.67,7.97736 175.46,8.11698 175.251,8.24961 175.097,8.45205 174.999,8.7243 174.909,8.98956 174.86,9.32813 174.853,9.73999L174.853,9.90753C174.853,10.3403 174.898,10.7103 174.989,11.0175 175.08,11.3176 175.23,11.548 175.439,11.7085 175.656,11.8621 175.949,11.9389 176.319,11.9389 176.633,11.9389 176.888,11.8551 177.083,11.6876 177.286,11.5131 177.436,11.2723 177.533,10.9651 177.631,10.658 177.68,10.3019 177.68,9.89705 177.68,9.28275 177.568,8.7976 177.345,8.44158 177.122,8.08556 176.773,7.90756 176.298,7.90756z M185.176,5.26885C185.818,5.26885 186.345,5.34913 186.757,5.50969 187.169,5.67024 187.476,5.91108 187.679,6.23219 187.881,6.54632 187.982,6.94422 187.982,7.42589 187.982,7.80983 187.909,8.13443 187.762,8.3997 187.623,8.65798 187.441,8.87089 187.218,9.03843 186.995,9.19898 186.761,9.32813 186.516,9.42586L188.611,12.7452 187.312,12.7452 185.511,9.71905 184.213,9.71905 184.213,12.7452 183.082,12.7452 183.082,5.26885 185.176,5.26885z M185.103,6.23219L184.213,6.23219 184.213,8.77665 185.155,8.77665C185.539,8.77665 185.853,8.72779 186.097,8.63006 186.349,8.53233 186.53,8.38573 186.642,8.19027 186.761,7.99481 186.82,7.75398 186.82,7.46777 186.82,7.1676 186.757,6.93026 186.632,6.75574 186.513,6.57424 186.324,6.44161 186.066,6.35784 185.815,6.27407 185.494,6.23219 185.103,6.23219z M191.771,6.99658C192.483,6.99658 193.013,7.15364 193.362,7.46777 193.711,7.77492 193.886,8.26008 193.886,8.92325L193.886,12.7452 193.101,12.7452 192.881,11.9598 192.839,11.9598C192.678,12.1623 192.511,12.3298 192.336,12.4625 192.169,12.5951 191.973,12.6928 191.75,12.7556 191.533,12.8185 191.268,12.8499 190.954,12.8499 190.619,12.8499 190.315,12.7906 190.043,12.6719 189.778,12.5462 189.568,12.3577 189.415,12.1064 189.261,11.8482 189.184,11.527 189.184,11.1431 189.184,10.5707 189.401,10.1344 189.834,9.83423 190.273,9.53406 190.94,9.37001 191.834,9.34209L192.797,9.31068 192.797,8.99654C192.797,8.5777 192.703,8.28451 192.514,8.11698 192.326,7.94944 192.061,7.86567 191.718,7.86567 191.425,7.86567 191.146,7.90756 190.881,7.99132 190.616,8.07509 190.364,8.17631 190.127,8.29498L189.771,7.48871C190.036,7.3491 190.34,7.23392 190.682,7.14317 191.031,7.04544 191.394,6.99658 191.771,6.99658z M192.033,10.0541C191.39,10.082 190.944,10.1902 190.692,10.3787 190.448,10.5602 190.326,10.8185 190.326,11.1536 190.326,11.4468 190.413,11.6597 190.588,11.7923 190.769,11.9249 190.996,11.9913 191.268,11.9913 191.708,11.9913 192.071,11.8691 192.357,11.6248 192.643,11.3805 192.787,11.014 192.787,10.5253L192.787,10.0332 192.033,10.0541z M197.626,11.9494C197.773,11.9494 197.923,11.9354 198.077,11.9075 198.23,11.8796 198.363,11.8482 198.475,11.8133L198.475,12.6509C198.356,12.7068 198.195,12.7522 197.993,12.7871 197.79,12.8289 197.588,12.8499 197.386,12.8499 197.078,12.8499 196.796,12.7975 196.537,12.6928 196.286,12.5881 196.084,12.4066 195.93,12.1483 195.777,11.89 195.7,11.5305 195.7,11.0698L195.7,7.94944 194.914,7.94944 194.914,7.44683 195.742,7.02799 196.129,5.83429 196.81,5.83429 196.81,7.10129 198.422,7.10129 198.422,7.94944 196.81,7.94944 196.81,11.0489C196.81,11.356 196.883,11.5829 197.03,11.7295 197.183,11.8761 197.382,11.9494 197.626,11.9494z M201.823,6.99658C202.319,6.99658 202.745,7.10129 203.101,7.31071 203.457,7.51315 203.729,7.80634 203.918,8.19027 204.113,8.57421 204.211,9.02796 204.211,9.55151L204.211,10.1588 200.42,10.1588C200.434,10.7382 200.584,11.1815 200.87,11.4887 201.164,11.7958 201.572,11.9494 202.096,11.9494 202.459,11.9494 202.78,11.918 203.059,11.8551 203.345,11.7853 203.638,11.6841 203.939,11.5515L203.939,12.4729C203.659,12.5986 203.373,12.6928 203.08,12.7556 202.794,12.8185 202.452,12.8499 202.054,12.8499 201.509,12.8499 201.028,12.7417 200.609,12.5253 200.19,12.3089 199.862,11.9878 199.624,11.562 199.394,11.1291 199.279,10.5986 199.279,9.97035 199.279,9.33511 199.384,8.7976 199.593,8.35781 199.809,7.91803 200.106,7.58295 200.483,7.35259 200.867,7.11525 201.314,6.99658 201.823,6.99658z M201.823,7.8552C201.425,7.8552 201.104,7.98434 200.86,8.24263 200.623,8.50092 200.483,8.8674 200.441,9.34209L203.101,9.34209C203.101,9.0489 203.055,8.79061 202.965,8.56723 202.874,8.34385 202.734,8.16933 202.546,8.04368 202.357,7.91803 202.117,7.8552 201.823,7.8552z" />-->
                            <!--<GeometryDrawing Brush="#FFFFFFFF" Geometry="F1 M212,53z M0,0z M157.013,32.484L157.013,29.316 165.669,29.316 165.669,31.444 161.365,40.5 157.909,40.5 162.197,31.444 159.573,31.444 159.573,32.484 157.013,32.484z M166.972,33.188L166.236,31.412C167.148,30.964,167.868,30.708,169.036,29.316L171.884,29.316 171.884,38.372 173.372,38.372 173.372,40.5 166.972,40.5 166.972,38.372 168.572,38.372 168.572,32.436 166.972,33.188z M174.013,38.836C174.013,38.02 174.653,37.012 175.821,37.012 176.989,37.012 177.661,38.02 177.661,38.836 177.661,39.796 177.021,40.564 175.821,40.564 174.765,40.564 174.013,39.684 174.013,38.836z M180.154,32.948L178.426,31.124C179.546,29.828 180.986,29.14 182.586,29.14 184.714,29.14 186.394,30.484 186.394,32.644 186.394,34.484 185.53,35.124 183.834,36.228 183.066,36.724 181.802,37.732 181.434,38.372L184.25,38.372 184.25,37.092 186.938,37.092 186.938,40.5 178.298,40.5 178.298,38.18C179.178,36.74 179.61,36.276 180.634,35.46 181.77,34.452 183.466,33.78 183.466,32.788 183.466,31.972 182.858,31.732 182.122,31.732 181.258,31.732 180.634,32.436 180.154,32.948z M190.872,35.604C188.952,35.604 187.576,34.324 187.576,32.292 187.576,30.34 189.16,29.028 190.808,29.028 192.888,29.028 194.104,30.324 194.104,32.34 194.104,34.212 192.936,35.604 190.872,35.604z M192.168,40.5L190.36,40.5 197.272,29.316 199.096,29.316 192.168,40.5z M190.872,34.132C191.352,34.132 191.704,33.444 191.704,32.34 191.704,31.268 191.304,30.532 190.824,30.532 190.424,30.516 189.96,31.172 189.96,32.292 189.96,33.444 190.456,34.132 190.872,34.132z M198.76,40.5C196.84,40.5 195.464,39.22 195.464,37.188 195.464,35.236 197.048,33.924 198.696,33.924 200.776,33.924 201.992,35.22 201.992,37.236 201.992,39.108 200.824,40.5 198.76,40.5z M198.76,39.028C199.24,39.028 199.592,38.34 199.592,37.236 199.592,36.164 199.192,35.428 198.712,35.428 198.312,35.412 197.848,36.068 197.848,37.188 197.848,38.34 198.344,39.028 198.76,39.028z" />-->
                            <GeometryDrawing Brush="#FF141617">
                                <GeometryDrawing.Geometry>
                                    <RectangleGeometry RadiusX="3.5637" RadiusY="3.5637" Rect="0.4363,0.4363,64.1274,52.1274" />
                                </GeometryDrawing.Geometry>
                            </GeometryDrawing>
                            <GeometryDrawing>
                                <GeometryDrawing.Pen>
                                    <Pen Brush="{StaticResource HSReplayNetBlue}" Thickness="0.872587" StartLineCap="Flat" EndLineCap="Flat" LineJoin="Miter" />
                                </GeometryDrawing.Pen>
                                <GeometryDrawing.Geometry>
                                    <RectangleGeometry RadiusX="3.5637" RadiusY="3.5637" Rect="0.4363,0.4363,64.1274,52.1274" />
                                </GeometryDrawing.Geometry>
                            </GeometryDrawing>
                            <GeometryDrawing Brush="{StaticResource HSReplayNetBlue}" Geometry="F1 M212,53z M0,0z M0,3.49035C0,1.56268,1.56268,0,3.49035,0L61.5097,0C63.4373,0,65,1.56268,65,3.49035L65,19 0,19 0,3.49035z" />
                            <!--<GeometryDrawing Brush="#FFFFFFFF" Geometry="F1 M212,53z M0,0z M11.2869,13.5L10.4331,13.5 10.4331,8.58147C10.4331,8.1929,10.457,7.71741,10.5047,7.155L10.4842,7.155C10.4024,7.48563,10.3291,7.72252,10.2644,7.86568L7.75909,13.5 7.33984,13.5 4.83968,7.90658C4.7681,7.74297,4.69481,7.49244,4.61983,7.155L4.59937,7.155C4.62664,7.44813,4.64028,7.92703,4.64028,8.5917L4.64028,13.5 3.812,13.5 3.812,6.16823 4.94705,6.16823 7.19668,11.281C7.37052,11.673,7.483,11.9662,7.53413,12.1604L7.56481,12.1604C7.71137,11.7582,7.82897,11.4583,7.91759,11.2606L10.2132,6.16823 11.2869,6.16823 11.2869,13.5z M17.3405,13.5L16.502,13.5 16.502,12.6717 16.4816,12.6717C16.1339,13.3057 15.5953,13.6227 14.8659,13.6227 13.6184,13.6227 12.9946,12.8796 12.9946,11.3935L12.9946,8.26448 13.828,8.26448 13.828,11.2606C13.828,12.365 14.2507,12.9171 15.096,12.9171 15.505,12.9171 15.8407,12.7672 16.1032,12.4672 16.3691,12.1639 16.502,11.7685 16.502,11.281L16.502,8.26448 17.3405,8.26448 17.3405,13.5z M19.8765,13.5L19.038,13.5 19.038,5.74897 19.8765,5.74897 19.8765,13.5z M22.4124,13.5L21.5739,13.5 21.5739,5.74897 22.4124,5.74897 22.4124,13.5z M24.5393,6.93515C24.3894,6.93515 24.2616,6.88402 24.1559,6.78176 24.0502,6.67951 23.9974,6.54998 23.9974,6.39319 23.9974,6.2364 24.0502,6.10687 24.1559,6.00461 24.2616,5.89895 24.3894,5.84612 24.5393,5.84612 24.6927,5.84612 24.8223,5.89895 24.9279,6.00461 25.037,6.10687 25.0915,6.2364 25.0915,6.39319 25.0915,6.54316 25.037,6.67099 24.9279,6.77665 24.8223,6.88231 24.6927,6.93515 24.5393,6.93515z M24.9484,13.5L24.1099,13.5 24.1099,8.26448 24.9484,8.26448 24.9484,13.5z M31.1144,13.0807C31.1144,15.0032 30.1941,15.9644 28.3535,15.9644 27.7059,15.9644 27.1401,15.8417 26.6561,15.5963L26.6561,14.7578C27.2457,15.085 27.8081,15.2486 28.3433,15.2486 29.6317,15.2486 30.2759,14.5635 30.2759,13.1932L30.2759,12.6206 30.2555,12.6206C29.8567,13.2887 29.2568,13.6227 28.4558,13.6227 27.8047,13.6227 27.2798,13.3909 26.881,12.9274 26.4856,12.4604 26.2879,11.8349 26.2879,11.051 26.2879,10.1613 26.501,9.45406 26.927,8.92914 27.3565,8.40423 27.9428,8.14177 28.6858,8.14177 29.3914,8.14177 29.9146,8.42468 30.2555,8.9905L30.2759,8.9905 30.2759,8.26448 31.1144,8.26448 31.1144,13.0807z M30.2759,11.1328L30.2759,10.3607C30.2759,9.94489 30.1345,9.5887 29.8516,9.29215 29.5721,8.99561 29.2227,8.84734 28.8034,8.84734 28.2853,8.84734 27.8797,9.03651 27.5866,9.41486 27.2935,9.7898 27.1469,10.3164 27.1469,10.9947 27.1469,11.5776 27.2866,12.0446 27.5661,12.3956 27.849,12.7433 28.2223,12.9171 28.6858,12.9171 29.1562,12.9171 29.538,12.7501 29.8311,12.4161 30.1277,12.082 30.2759,11.6543 30.2759,11.1328z M36.5596,13.5L35.7211,13.5 35.7211,12.6819 35.7006,12.6819C35.3359,13.3091 34.7991,13.6227 34.0901,13.6227 33.5686,13.6227 33.1596,13.4847 32.863,13.2086 32.5699,12.9325 32.4233,12.5661 32.4233,12.1093 32.4233,11.1311 32.9994,10.5618 34.1514,10.4016L35.7211,10.1818C35.7211,9.29215 35.3615,8.84734 34.6423,8.84734 34.0117,8.84734 33.4425,9.06208 32.9346,9.49155L32.9346,8.6326C33.4493,8.30538 34.0424,8.14177 34.7138,8.14177 35.9443,8.14177 36.5596,8.7928 36.5596,10.0949L36.5596,13.5z M35.7211,10.8516L34.4582,11.0254C34.0696,11.0799 33.7765,11.1771 33.5788,11.3168 33.3811,11.4532 33.2823,11.6969 33.2823,12.048 33.2823,12.3036 33.3726,12.5132 33.5532,12.6768 33.7373,12.837 33.981,12.9171 34.2844,12.9171 34.7002,12.9171 35.0428,12.7723 35.312,12.4826 35.5847,12.1894 35.7211,11.8196 35.7211,11.3731L35.7211,10.8516z M42.4853,13.5L41.6468,13.5 41.6468,10.5141C41.6468,9.40293 41.2412,8.84734 40.43,8.84734 40.0107,8.84734 39.6631,9.00584 39.387,9.32283 39.1143,9.63642 38.9779,10.0335 38.9779,10.5141L38.9779,13.5 38.1394,13.5 38.1394,8.26448 38.9779,8.26448 38.9779,9.13366 38.9984,9.13366C39.3938,8.4724 39.9664,8.14177 40.7163,8.14177 41.2889,8.14177 41.7269,8.32754 42.0303,8.69907 42.3336,9.06719 42.4853,9.60063 42.4853,10.2994L42.4853,13.5z M55.748,6.16823L53.6773,13.5 52.67,13.5 51.1618,8.14177C51.097,7.9134,51.0578,7.66458,51.0442,7.3953L51.0237,7.3953C51.0033,7.64753,50.959,7.89295,50.8908,8.13155L49.3723,13.5 48.3753,13.5 46.2279,6.16823 47.1738,6.16823 48.7332,11.7923C48.7979,12.0275,48.8388,12.2729,48.8559,12.5286L48.8815,12.5286C48.8985,12.3479,48.9513,12.1025,49.04,11.7923L50.6607,6.16823 51.4839,6.16823 53.0382,11.8332C53.0927,12.0275,53.1336,12.2559,53.1609,12.5183L53.1813,12.5183C53.195,12.3411,53.241,12.1059,53.3194,11.8128L54.8174,6.16823 55.748,6.16823z M62.0981,13.5L61.0755,13.5 59.8484,11.4446C59.736,11.2538 59.6269,11.0919 59.5212,10.9589 59.4156,10.8226 59.3065,10.7118 59.194,10.6266 59.0849,10.5414 58.9656,10.48 58.8361,10.4425 58.71,10.4016 58.5668,10.3812 58.4066,10.3812L57.7011,10.3812 57.7011,13.5 56.8421,13.5 56.8421,6.16823 59.0304,6.16823C59.3508,6.16823 59.6456,6.20913 59.9149,6.29093 60.1876,6.36933 60.4228,6.49033 60.6205,6.65394 60.8216,6.81755 60.9784,7.02206 61.0909,7.26748 61.2033,7.50949 61.2596,7.7941 61.2596,8.12132 61.2596,8.37696 61.2204,8.61215 61.142,8.82689 61.067,9.03822 60.9579,9.22739 60.8148,9.39441 60.675,9.56143 60.5046,9.70459 60.3035,9.82389 60.1058,9.93978 59.8825,10.0301 59.6337,10.0949L59.6337,10.1153C59.7564,10.1699 59.8621,10.2329 59.9507,10.3045 60.0427,10.3727 60.1296,10.4545 60.2115,10.5499 60.2933,10.6453 60.3734,10.7544 60.4518,10.8771 60.5336,10.9964 60.6239,11.1362 60.7227,11.2964L62.0981,13.5z M57.7011,6.94537L57.7011,9.60404 58.8668,9.60404C59.0815,9.60404 59.2792,9.57165 59.4599,9.50689 59.6439,9.44213 59.8024,9.3501 59.9354,9.2308 60.0683,9.10809 60.1723,8.95982 60.2472,8.78599 60.3222,8.60874 60.3597,8.41105 60.3597,8.1929 60.3597,7.80092 60.2319,7.49585 59.9763,7.27771 59.724,7.05615 59.3576,6.94537 58.877,6.94537L57.7011,6.94537z" />-->
                            <!--<GeometryDrawing Brush="#FF269110" Geometry="F1 M212,53z M0,0z M13.5205,41.708C11.9045,41.708,10.2085,40.764,9.5525,39.772L11.2965,37.964C11.9205,38.604 12.6085,39.148 13.5365,39.148 14.6085,39.148 15.2965,38.476 15.2965,37.548 15.2965,36.7 14.5765,35.996 13.6645,35.996 12.9285,35.996 12.4965,36.38 12.1605,36.732L10.1125,35.82 11.0885,30.316 17.4245,30.316 17.4245,32.7 13.1205,32.7 12.8485,34.252C13.1685,34.044 13.7925,33.868 14.5765,33.868 16.4485,33.868 18.3045,35.18 18.3045,37.66 18.3045,40.348 15.8405,41.708 13.5205,41.708z M18.7869,33.484L18.7869,30.316 27.4429,30.316 27.4429,32.444 23.1389,41.5 19.6829,41.5 23.9709,32.444 21.3469,32.444 21.3469,33.484 18.7869,33.484z M27.8494,39.836C27.8494,39.02 28.4894,38.012 29.6574,38.012 30.8254,38.012 31.4974,39.02 31.4974,39.836 31.4974,40.796 30.8574,41.564 29.6574,41.564 28.6014,41.564 27.8494,40.684 27.8494,39.836z M31.9744,33.484L31.9744,30.316 40.6304,30.316 40.6304,32.444 36.3264,41.5 32.8704,41.5 37.1584,32.444 34.5344,32.444 34.5344,33.484 31.9744,33.484z M44.3329,36.604C42.4129,36.604 41.0369,35.324 41.0369,33.292 41.0369,31.34 42.6209,30.028 44.2689,30.028 46.3489,30.028 47.5649,31.324 47.5649,33.34 47.5649,35.212 46.3969,36.604 44.3329,36.604z M45.6289,41.5L43.8209,41.5 50.7329,30.316 52.5569,30.316 45.6289,41.5z M44.3329,35.132C44.8129,35.132 45.1649,34.444 45.1649,33.34 45.1649,32.268 44.7649,31.532 44.2849,31.532 43.8849,31.516 43.4209,32.172 43.4209,33.292 43.4209,34.444 43.9169,35.132 44.3329,35.132z M52.2209,41.5C50.3009,41.5 48.9249,40.22 48.9249,38.188 48.9249,36.236 50.5089,34.924 52.1569,34.924 54.2369,34.924 55.4529,36.22 55.4529,38.236 55.4529,40.108 54.2849,41.5 52.2209,41.5z M52.2209,40.028C52.7009,40.028 53.0529,39.34 53.0529,38.236 53.0529,37.164 52.6529,36.428 52.1729,36.428 51.7729,36.412 51.3089,37.068 51.3089,38.188 51.3089,39.34 51.8049,40.028 52.2209,40.028z" />-->
                            <GeometryDrawing Brush="{Binding RankGradient}" Geometry="F1 M212,53z M0,0z M73,3.49035C73,1.56268,74.5627,0,76.4903,0L135.51,0C137.437,0,139,1.56268,139,3.49035L139,49.5097C139,51.4373,137.437,53,135.51,53L76.4904,53C74.5627,53,73,51.4373,73,49.5097L73,3.49035z">
                                <!--<GeometryDrawing.Brush>
                                    <LinearGradientBrush StartPoint="106,0" EndPoint="106,53" MappingMode="Absolute" SpreadMethod="Pad" Opacity="1">
                                        <GradientStop Color="#FF6A9D36" Offset="0.0001" />
                                        <GradientStop Color="#FF587937" Offset="1" />
                                    </LinearGradientBrush>
                                </GeometryDrawing.Brush>-->
                            </GeometryDrawing>
                            <GeometryDrawing Geometry="F1 M212,53z M0,0z M76.4903,0.436293L135.51,0.436293C137.196,0.436293,138.564,1.80364,138.564,3.49035L138.564,49.5097C138.564,51.1964,137.196,52.5637,135.51,52.5637L76.4904,52.5637C74.8036,52.5637,73.4363,51.1964,73.4363,49.5097L73.4363,3.49035C73.4363,1.80364,74.8036,0.436293,76.4903,0.436293z">
                                <GeometryDrawing.Pen>
                                    <Pen Thickness="0.872587" StartLineCap="Flat" EndLineCap="Flat" LineJoin="Miter">
                                        <Pen.Brush>
                                            <SolidColorBrush Color="#FF000000" Opacity="0.18" />
                                        </Pen.Brush>
                                    </Pen>
                                </GeometryDrawing.Pen>
                            </GeometryDrawing>
                            <!--<GeometryDrawing Brush="#FFFFFFFF" Geometry="F1 M212,53z M0,0z M89.9281,16.5L89.0346,16.5 89.0346,14.2871 86.8426,14.2871 86.8426,16.5 85.9491,16.5 85.9491,11.5158 86.8426,11.5158 86.8426,13.5262 89.0346,13.5262 89.0346,11.5158 89.9281,11.5158 89.9281,16.5z M94.304,16.5L93.8503,15.1806 91.9725,15.1806 91.5257,16.5 90.5763,16.5 92.4053,11.4948 93.4244,11.4948 95.2534,16.5 94.304,16.5z M93.1871,13.1283C93.1685,13.0632 93.1406,12.9747 93.1033,12.8631 93.0661,12.7514 93.0289,12.6373 92.9916,12.521 92.9591,12.4 92.9335,12.2999 92.9148,12.2208 92.8916,12.3139 92.8613,12.4209 92.8241,12.5419 92.7915,12.6629 92.7589,12.777 92.7264,12.884 92.6984,12.991 92.6752,13.0725 92.6566,13.1283L92.2168,14.4128 93.6199,14.4128 93.1871,13.1283z M100.146,16.5L99.0362,16.5 96.6767,12.5908 96.6488,12.5908C96.6535,12.6932 96.6581,12.8026 96.6628,12.9189 96.6721,13.0306 96.6791,13.1469 96.6837,13.2679 96.6884,13.3843 96.693,13.503 96.6977,13.624 96.7023,13.745 96.707,13.866 96.7116,13.9869L96.7116,16.5 95.9089,16.5 95.9089,11.5158 97.0118,11.5158 99.3643,15.3971 99.3852,15.3971C99.3806,15.304 99.3759,15.2039 99.3713,15.0969 99.3666,14.9852 99.362,14.8712 99.3573,14.7548 99.3573,14.6385 99.355,14.5198 99.3503,14.3988 99.3457,14.2778 99.341,14.1615 99.3364,14.0498L99.3364,11.5158 100.146,11.5158 100.146,16.5z M105.521,13.959C105.521,14.5175 105.414,14.9852 105.2,15.3621 104.991,15.7391 104.686,16.023 104.285,16.2138 103.89,16.4046 103.411,16.5 102.847,16.5L101.444,16.5 101.444,11.5158 102.994,11.5158C103.511,11.5158 103.957,11.6089 104.334,11.795 104.711,11.9812 105.002,12.2557 105.207,12.6187 105.416,12.9771 105.521,13.4238 105.521,13.959z M104.586,13.9869C104.586,13.5914 104.525,13.2656 104.404,13.0097 104.283,12.7537 104.104,12.5652 103.867,12.4442 103.629,12.3186 103.338,12.2557 102.994,12.2557L102.338,12.2557 102.338,15.7531 102.875,15.7531C103.452,15.7531 103.881,15.6065 104.16,15.3133 104.444,15.0154 104.586,14.5733 104.586,13.9869z M109.815,11.5158C110.252,11.5158 110.613,11.5693 110.897,11.6763 111.181,11.7834 111.392,11.9463 111.532,12.165 111.672,12.3791 111.742,12.6513 111.742,12.9817 111.742,13.2237 111.695,13.4308 111.602,13.603 111.513,13.7752 111.397,13.9195 111.253,14.0358 111.109,14.1475 110.953,14.2383 110.785,14.3081L112.216,16.5 111.211,16.5 110.017,14.5314 109.291,14.5314 109.291,16.5 108.398,16.5 108.398,11.5158 109.815,11.5158z M109.759,12.2627L109.291,12.2627 109.291,13.7985 109.787,13.7985C110.034,13.7985 110.234,13.7682 110.387,13.7077 110.541,13.6472 110.653,13.5588 110.722,13.4425 110.797,13.3261 110.834,13.1818 110.834,13.0097 110.834,12.8282 110.794,12.6839 110.715,12.5768 110.636,12.4652 110.518,12.386 110.359,12.3395 110.206,12.2883 110.006,12.2627 109.759,12.2627z M115.989,16.5L115.535,15.1806 113.658,15.1806 113.211,16.5 112.261,16.5 114.09,11.4948 115.11,11.4948 116.939,16.5 115.989,16.5z M114.872,13.1283C114.854,13.0632 114.826,12.9747 114.788,12.8631 114.751,12.7514 114.714,12.6373 114.677,12.521 114.644,12.4 114.619,12.2999 114.6,12.2208 114.577,12.3139 114.546,12.4209 114.509,12.5419 114.477,12.6629 114.444,12.777 114.412,12.884 114.384,12.991 114.36,13.0725 114.342,13.1283L113.902,14.4128 115.305,14.4128 114.872,13.1283z M121.831,16.5L120.721,16.5 118.362,12.5908 118.334,12.5908C118.339,12.6932 118.343,12.8026 118.348,12.9189 118.357,13.0306 118.364,13.1469 118.369,13.2679 118.374,13.3843 118.378,13.503 118.383,13.624 118.387,13.745 118.392,13.866 118.397,13.9869L118.397,16.5 117.594,16.5 117.594,11.5158 118.697,11.5158 121.049,15.3971 121.07,15.3971C121.066,15.304 121.061,15.2039 121.056,15.0969 121.052,14.9852 121.047,14.8712 121.042,14.7548 121.042,14.6385 121.04,14.5198 121.035,14.3988 121.031,14.2778 121.026,14.1615 121.022,14.0498L121.022,11.5158 121.831,11.5158 121.831,16.5z M126.997,16.5L125.971,16.5 124.491,14.2941 124.023,14.6641 124.023,16.5 123.129,16.5 123.129,11.5158 124.023,11.5158 124.023,13.8613C124.116,13.745 124.211,13.6263 124.309,13.5053 124.407,13.3843 124.505,13.2633 124.602,13.1423L125.95,11.5158 126.962,11.5158 125.133,13.7077 126.997,16.5z" />-->
                            <!--<GeometryDrawing Brush="#FFFFFFFF" Geometry="F1 M212,53z M0,0z M100.008,39.5L101.83,24.8405 105.181,24.8405 103.359,39.5 100.008,39.5z M92.4064,36.6309L92.4064,33.4896 106.647,33.4896 106.647,36.6309 92.4064,36.6309z M94.5843,39.5L96.4063,24.8405 99.757,24.8405 97.9351,39.5 94.5843,39.5z M93.1184,30.8509L93.1184,27.7096 107.359,27.7096 107.359,30.8509 93.1184,30.8509z M113.291,39.8351C112.285,39.8351 111.266,39.7234 110.233,39.5 109.2,39.2766 108.292,38.9485 107.511,38.5157L109.186,34.8718C109.814,35.2348 110.47,35.507 111.154,35.6885 111.853,35.8561 112.509,35.9398 113.123,35.9398 113.681,35.9398 114.135,35.8491 114.484,35.6676 114.847,35.4721 115.029,35.1859 115.029,34.809 115.029,34.4879 114.896,34.2435 114.631,34.076 114.366,33.8945 113.919,33.8038 113.291,33.8038L111.343,33.8038 111.343,30.7671 115.133,26.9138 115.573,28.5682 108.264,28.5682 108.264,24.8405 119.196,24.8405 119.196,27.8771 115.406,31.7305 113.039,30.3902 114.338,30.3902C116.181,30.3902 117.584,30.8021 118.547,31.6258 119.51,32.4495 119.992,33.5106 119.992,34.809 119.992,35.6467 119.755,36.4494 119.28,37.2173 118.819,37.9852 118.093,38.6135 117.102,39.1021 116.125,39.5907 114.854,39.8351 113.291,39.8351z" />-->
                        </DrawingGroup>
                    </DrawingImage.Drawing>
                </DrawingImage>
            </Image.Source>
        </Image>


        <!-- Mulligan Winrate -->
        <TextBlock Grid.Column="0" Grid.Row="0" Text="{lex:Loc ConstructedMulliganGuide_Header_MulliganWR}" FontSize="10.5" TextTrimming="CharacterEllipsis" Padding="1 0" />
        <TextBlock Text="{Binding MulliganWr, StringFormat={}{0:0.0}%, TargetNullValue=&#8211;, FallbackValue=&#8211;}" Grid.Row="1"
            Style="{StaticResource StatsTextStyle}" Foreground="{Binding MulliganWrColor, FallbackValue=White}" />

        <!-- Mulligan Winrate Tooltip -->
        <Grid Grid.RowSpan="2" Grid.Column="0" ToolTipService.InitialShowDelay="0" extensions:OverlayExtensions.IsOverlayHitTestVisible="True">
            <Border Name="MulliganWRBorderMask" Background="#01000000" CornerRadius="0 0 35 0"/>
            <Grid.OpacityMask>
                <VisualBrush Visual="{Binding ElementName=MulliganWRBorderMask}" />
            </Grid.OpacityMask>
            <Grid.ToolTip>
                <TextBlock TextAlignment="Left" FontSize="12" TextWrapping="Wrap">
                    <Bold><Run Text="{lex:Loc ConstructedMulliganGuide_Header_MulliganWRTooltip_Title}"/></Bold>
                    <LineBreak/>
                    <Run Text="{lex:Loc ConstructedMulliganGuide_Header_MulliganWRTooltip_Desc}"/>
                </TextBlock>
            </Grid.ToolTip>
        </Grid>

        <!-- Hand Rank & Tooltip -->
        <Grid Grid.RowSpan="2" Grid.Column="1" Height="52" Width="52" VerticalAlignment="Top" Background="#01000000"
              ToolTipService.InitialShowDelay="0" extensions:OverlayExtensions.IsOverlayHitTestVisible="True">
            <Grid.ToolTip>
                <TextBlock TextAlignment="Left" FontSize="12" TextWrapping="Wrap">
                    <Bold><Run Text="{lex:Loc ConstructedMulliganGuide_Header_HandRankTooltip_Title}"/></Bold>
                    <LineBreak/>
                    <Run Text="{Binding HandRankTooltipText, Mode=OneWay}"/>
                </TextBlock>
            </Grid.ToolTip>
            <StackPanel VerticalAlignment="Center">
                <TextBlock Text="{lex:Loc ConstructedMulliganGuide_Header_HandRank}" FontSize="8.5" Margin="0 5 0 0" TextTrimming="CharacterEllipsis" />
                <TextBlock Style="{StaticResource BoldTextStyle}" Text="{Binding Rank, StringFormat={}#{0}, TargetNullValue=&#0063;, FallbackValue=&#0063;}" FontSize="23" />
            </StackPanel>
        </Grid>

        <!-- Keep Rate -->
        <TextBlock Grid.Column="2" Grid.Row="0" Text="{lex:Loc ConstructedMulliganGuide_Header_KeepRate}" FontSize="10.5" TextTrimming="CharacterEllipsis" Padding="1 0" />
        <TextBlock Text="{Binding KeepRate, StringFormat={}{0:0.0}%, TargetNullValue=&#8211;, FallbackValue=&#8211;}" Grid.Row="1" Grid.Column="2"
            Style="{StaticResource StatsTextStyle}"
        />

        <!-- Keep Rate Tooltip -->
        <Grid Grid.RowSpan="2" Grid.Column="2" Height="62" Width="65" ToolTipService.InitialShowDelay="0" extensions:OverlayExtensions.IsOverlayHitTestVisible="True">
            <Border Name="KeepRateBorderMask" Background="#01000000" CornerRadius="0 0 0 35"/>
            <Grid.OpacityMask>
                <VisualBrush Visual="{Binding ElementName=KeepRateBorderMask}" />
            </Grid.OpacityMask>
            <Grid.ToolTip>
                <TextBlock TextAlignment="Left" FontSize="12" TextWrapping="Wrap">
                    <Bold><Run Text="{lex:Loc ConstructedMulliganGuide_Header_KeepRateTooltip_Title}"/></Bold>
                    <LineBreak/>
                    <Run Text="{lex:Loc ConstructedMulliganGuide_Header_KeepRateTooltip_Desc}"/>
                </TextBlock>
            </Grid.ToolTip>
        </Grid>
    </Grid>
</UserControl>
