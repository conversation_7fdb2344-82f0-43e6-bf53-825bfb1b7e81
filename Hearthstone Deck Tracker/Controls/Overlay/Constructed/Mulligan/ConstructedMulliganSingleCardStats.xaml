<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Constructed.Mulligan.ConstructedMulliganSingleCardStats"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             xmlns:mulligan="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Constructed.Mulligan"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d"
             d:DesignHeight="400" d:DesignWidth="212">
    <DockPanel Width="212">
        <Grid DockPanel.Dock="Top" >
            <mulligan:ConstructedMulliganSingleCardHeader DataContext="{Binding CardHeaderVM}" VerticalAlignment="Top"/>
        </Grid>

        <!-- Card Container -->
        <Grid Background="#00ff00ff" Margin="0 0 0 60" />
    </DockPanel>
</UserControl>
