﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Constructed.PlayerResourcesWidget.PlayerResourcesWidget"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             xmlns:vm="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Constructed.PlayerResourcesWidget"
             mc:Ignorable="d"
             d:DesignHeight="300" d:DesignWidth="300">
    <UserControl.DataContext>
        <vm:PlayerResourcesViewModel />
    </UserControl.DataContext>
    <Border Background="#AA000000"
            Padding="8"
            CornerRadius="8"
            Visibility="{Binding HasVisibleResources, Converter={StaticResource BooleanToVisibilityConverter}}">
        <StackPanel Orientation="Horizontal">
            <ItemsControl ItemsSource="{Binding ChangedResources}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <StackPanel Orientation="Horizontal"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>

                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal" Margin="0,0,12,0">
                            <Image Source="{Binding Icon}" Width="20" Height="20" Margin="0,0,4,0" />
                            <hearthstoneDeckTracker:HearthstoneTextBlock
                                Text="{Binding Value}"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontWeight="Regular"
                                MaxWidth="36">
                            </hearthstoneDeckTracker:HearthstoneTextBlock>
                        </StackPanel>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </StackPanel>
    </Border>
</UserControl>

