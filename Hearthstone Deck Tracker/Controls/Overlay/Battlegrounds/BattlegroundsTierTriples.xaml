<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.BattlegroundsTierTriples"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             mc:Ignorable="d"
             d:DesignHeight="58" d:DesignWidth="70">
    <Border
        Background="{Binding BgColor, FallbackValue=#37393C, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTierTriples}}"
        CornerRadius="3"
    >
        <StackPanel Orientation="Vertical" Margin="3 4 0 0" Height="60">
            <Canvas Height="42" Width="70">
                <local:BattlegroundsTier
                    Canvas.Left="{Binding TierLeft, FallbackValue=0, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTierTriples}}"
                    Canvas.Top="{Binding TierTop, FallbackValue=2, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTierTriples}}"
                    Tier="{Binding Tier, FallbackValue=5, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTierTriples}}"
                    Opacity="{Binding TierOpacity, FallbackValue=1, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTierTriples}}"
                    Width="24"
                    Height="24"
                >
                </local:BattlegroundsTier>
                <Image
                    Canvas.Left="28"
                    Canvas.Top="2"
                    Source="/HearthstoneDeckTracker;component/Resources/triple.png"
                    Width="34" Height="34"
                    Visibility="{Binding TripleVisibility, FallbackValue=Visible, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTierTriples}}"
                />
                <Image
                    Canvas.Left="28"
                    Canvas.Top="2"
                    Source="/HearthstoneDeckTracker;component/Resources/triple-black.png"
                    Width="34" Height="34"
                    Opacity="{Binding TripleOpacity, FallbackValue=0, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTierTriples}}"
                    Visibility="{Binding TripleVisibility, FallbackValue=Visible, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTierTriples}}"
                />
                <hearthstoneDeckTracker:HearthstoneTextBlock
                    Canvas.Left="40"
                    Canvas.Top="5"
                    Text="{Binding QtyText, FallbackValue=0, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTierTriples}}"
                    FontSize="15" FontWeight="Bold"
                    Visibility="{Binding TripleVisibility, FallbackValue=Visible, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTierTriples}}"
                />
            </Canvas>
            <Label
                Content="{Binding TurnText, FallbackValue=Turn 1, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTierTriples}}"
                Foreground="White"
                FontSize="10" FontWeight="Bold" Margin="2 -7 0 0" HorizontalAlignment="Center"
                Visibility="{Binding TripleVisibility, FallbackValue=Visible, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTierTriples}}"
            />
        </StackPanel>
    </Border>
</UserControl>
