﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Tier7.Tier7PreLobby"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Tier7"
             xmlns:anim="clr-namespace:Hearthstone_Deck_Tracker.Utility.Animations"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             xmlns:commands="clr-namespace:Hearthstone_Deck_Tracker.Commands"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             Width="800" Height="630"
             mc:Ignorable="d">
    <d:UserControl.DataContext>
        <local:Tier7PreLobbyViewModel UserState="Subscribed" Username="Epix">
        </local:Tier7PreLobbyViewModel>
    </d:UserControl.DataContext>

    <UserControl.Resources>
        <Style TargetType="TextBlock">
            <Style.Setters>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="TextAlignment" Value="Center"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="TextWrapping" Value="Wrap"/>
            </Style.Setters>
        </Style>

        <Style TargetType="ToolTip" BasedOn="{StaticResource BgsTooltipStyle}"/>
    </UserControl.Resources>

    <Grid Visibility="{Binding Visibility}">

        <!-- Anonymous Tooltip -->
        <Border Background="{StaticResource Tier7Black}" BorderBrush="{StaticResource Tier7Purple}" BorderThickness="1" CornerRadius="4"
                anim:FadeAnimation.Visibility="{Binding IsMouseOver, ElementName=HoverTrigger, Converter={StaticResource BoolToVisibility}}"
                anim:FadeAnimation.Direction="Right" anim:FadeAnimation.Distance="20" anim:FadeAnimation.Duration="0:0:0.2">
            <Grid>
                <Image Source="{StaticResource Tier7PreLobbyLarge}" RenderOptions.BitmapScalingMode="Fant" Margin="280 30 20 0" HorizontalAlignment="Left" VerticalAlignment="Top" />
                <Border Background="#19FFFFFF" VerticalAlignment="Bottom" Padding="16" CornerRadius="0 0 4 4">
                    <TextBlock FontSize="16">
                        <Run Text="{lex:Loc BattlegroundsPreLobby_AnonymousTooltip_Top}"/>
                        <LineBreak/>
                        <Run Text="{lex:Loc BattlegroundsPreLobby_AnonymousTooltip_Bottom}"/>
                    </TextBlock>
                </Border>
            </Grid>
        </Border>

        <!-- Interactive Panel -->
        <Border Name="Tier7Panel" MinWidth="{Binding PanelMinWidth}" Background="{StaticResource Tier7Black}"
                BorderBrush="{StaticResource Tier7Purple}" BorderThickness="1" CornerRadius="4" HorizontalAlignment="Left" VerticalAlignment="Top">
            <StackPanel>
                <!-- Header -->
                <Border Background="{StaticResource Tier7Purple}" Padding="8 5" Margin="-1 -1 -1 0" CornerRadius="4">
                    <DockPanel>
                        <local:Tier7Logo Width="16" Height="16" DockPanel.Dock="Left"/>
                        <Border DockPanel.Dock="Right" Padding="4 3" Margin="0 -3 -6 -3" CornerRadius="3"
                                Cursor="Hand" MouseUp="Chevron_MouseUp">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Self}}" Value="True">
                                            <Setter Property="Background" Value="#22FFFFFF"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>
                            <Rectangle Height="8" Width="16">
                                <Rectangle.Fill>
                                    <VisualBrush Visual="{Binding ChevronIcon, FallbackValue={StaticResource chevron_up}}" />
                                </Rectangle.Fill>
                            </Rectangle>
                        </Border>
                        <Border DockPanel.Dock="Right" Padding="4 3" Margin="0 -3 2 -3" CornerRadius="3" Cursor="Hand"
                                Visibility="{Binding IsMouseOver, ElementName=Tier7Panel, Converter={StaticResource BoolToVisibility}, ConverterParameter=Hidden}">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Self}}" Value="True">
                                            <Setter Property="Background" Value="#22FFFFFF"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>
                            <Border.InputBindings>
                                <MouseBinding MouseAction="LeftClick" Command="commands:GlobalCommands.ShowSettings" CommandParameter="Battlegrounds" />
                            </Border.InputBindings>
                            <Rectangle Height="14" Width="14">
                                <Rectangle.Fill>
                                    <VisualBrush Visual="{StaticResource appbar_settings}" />
                                </Rectangle.Fill>
                            </Rectangle>
                        </Border>
                        <TextBlock Text="{lex:Loc BattlegroundsPreLobby_InteractivePanel_Header}" Foreground="White" Margin="12 0" VerticalAlignment="Center" TextAlignment="Left" />
                    </DockPanel>
                </Border>
                <StackPanel Name="Tier7Content"  Visibility="{Binding IsCollapsed, Converter={StaticResource InverseBoolToVisibility}}">
                    <!-- Loading -->
                    <StackPanel Margin="16" Visibility="{Binding UserState, Converter={StaticResource EnumToVisibility}, ConverterParameter={x:Static local:UserState.Loading}}">
                        <TextBlock Text="{lex:Loc BattlegroundsPreLobby_InteractivePanel_Loading}" />
                    </StackPanel>

                    <!-- UnknownPlayer -->
                    <StackPanel Width="182" Margin="16" Visibility="{Binding UserState, Converter={StaticResource EnumToVisibility}, ConverterParameter={x:Static local:UserState.UnknownPlayer}}">
                        <StackPanel Name="HoverTrigger" >
                            <Image Source="{StaticResource Tier7PreLobbySmall}" Stretch="UniformToFill" RenderOptions.BitmapScalingMode="Fant" HorizontalAlignment="Center" Margin="0 -8 0 0" />
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Background="{StaticResource Tier7Black}">
                                <TextBlock Text="{lex:Loc BattlegroundsPreLobby_InteractivePanel_Anonymous_Hover}" Foreground="#07618B"/>
                                <Rectangle Fill="#07618B" Width="17" Height="17" Margin="4 0 0 0">
                                    <Rectangle.OpacityMask>
                                        <VisualBrush Visual="{DynamicResource appbar_magnify}" />
                                    </Rectangle.OpacityMask>
                                </Rectangle>
                            </StackPanel>
                        </StackPanel>
                        <TextBlock Text="{lex:Loc BattlegroundsPreLobby_InteractivePanel_Anonymous_Hover_Details}" Margin="0 8 0 0" />
                    </StackPanel>

                    <!-- ValidPlayer -->
                    <StackPanel Width="230" Margin="16" Visibility="{Binding UserState, Converter={StaticResource EnumToVisibility}, ConverterParameter={x:Static local:UserState.ValidPlayer}}">
                        <TextBlock  Foreground="{StaticResource Tier7Orange}" FontWeight="SemiBold">
                        <Run Text="{lex:Loc BattlegroundsPreLobby_Welcome}"/>
                        <Run Text="{Binding Username}"/>!
                        </TextBlock>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0 8 0 0">
                            <TextBlock FontWeight="SemiBold" Margin="0 0 8 0">
                            <Run Text="{lex:Loc BattlegroundsPreLobby_TrialsRemaining}"/>
                            <Run Text="{Binding TrialUsesRemaining}"/>
                            </TextBlock>
                            <Border Width="18" Height="18" BorderBrush="White" Background="{StaticResource Tier7Black}" BorderThickness="1" CornerRadius="18"
                                ToolTipService.InitialShowDelay="0" ToolTip="{lex:Loc BattlegroundsPreLobby_TrialsRemaining_Tooltip}">
                                <TextBlock Text="i" VerticalAlignment="Center" FontSize="12" FontWeight="SemiBold"/>
                            </Border>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center" Visibility="{Binding ResetTimeVisibility}">
                            <TextBlock>
                            <Run Text="{lex:Loc BattlegroundsPreLobby_TrialsResetsIn}" />
                            <Bold><Run Text="{Binding TrialTimeRemaining}" /></Bold>
                            </TextBlock>
                        </StackPanel>
                        <TextBlock Text="{lex:Loc BattlegroundsPreLobby_Join}" TextWrapping="Wrap" Margin="0 8 0 0" />
                        <Button Style="{StaticResource Tier7ButtonStyle}" Content="SUBSCRIBE NOW" Margin="0 8 0 0" Command="{Binding SubscribeNowCommand}"/>

                        <!-- Subscribed? Sign In -->
                        <TextBlock Margin="0 8 0 0" Visibility="{Binding RefreshSubscriptionState, Converter={StaticResource EnumToVisibility}, ConverterParameter={x:Static local:RefreshSubscriptionState.SignIn}}">
                        <Run Text="{lex:Loc BattlegroundsPreLobby_AlreadySubscribed}"/>
                        <Hyperlink Command="commands:GlobalCommands.SignInCommand">
                            <Run Text="{lex:Loc BattlegroundsPreLobby_SignInToSubscription}"/>
                        </Hyperlink>
                        </TextBlock>

                        <!-- Subscribed? Refresh -->
                        <TextBlock Margin="0 8 0 0" Visibility="{Binding RefreshSubscriptionState, Converter={StaticResource EnumToVisibility}, ConverterParameter={x:Static local:RefreshSubscriptionState.Refresh}}">
                        <Run Text="{lex:Loc BattlegroundsPreLobby_AlreadySubscribed}"/>
                        <Hyperlink Command="{Binding RefreshAccountCommand}" IsEnabled="{Binding RefreshAccountEnabled}">
                            <Run Text="{lex:Loc BattlegroundsPreLobby_RefreshSubscription}"/>
                        </Hyperlink>
                        </TextBlock>
                    </StackPanel>

                    <!-- Subscribed -->
                    <StackPanel Width="230" Margin="16" Visibility="{Binding UserState, Converter={StaticResource EnumToVisibility}, ConverterParameter={x:Static local:UserState.Subscribed}}">
                        <TextBlock Text="{lex:Loc BattlegroundsPreLobby_Subscribed_Tier7}" Foreground="{StaticResource Tier7Orange}" FontWeight="SemiBold"/>
                        <StackPanel Margin="0 8 0 0" Visibility="{Binding AllTimeHighMMRVisibility}">
                            <TextBlock Text="{lex:Loc BattlegroundsPreLobby_Subscribed_AllTimeMMR}"/>
                            <TextBlock FontSize="24" Text="{Binding AllTimeHighMMR, FallbackValue='N/A'}" FontFamily="/HearthstoneDeckTracker;component/Resources/#Chunkfive"/>
                        </StackPanel>
                        <Button Style="{StaticResource Tier7ButtonStyle}" Content="{lex:Loc BattlegroundsPreLobby_Subscribed_MyStats}" Margin="0 8 0 0" Command="{Binding MyStatsCommand}"/>
                    </StackPanel>

                    <!-- Disabled -->
                    <DockPanel Width="182" Margin="16" Visibility="{Binding UserState, Converter={StaticResource EnumToVisibility}, ConverterParameter={x:Static local:UserState.Disabled}}">
                        <Rectangle Fill="{StaticResource Tier7Orange}" Width="17" Height="15" Margin="4 0 0 0" DockPanel.Dock="Right">
                            <Rectangle.OpacityMask>
                                <VisualBrush Visual="{DynamicResource appbar_warning}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>
                        <TextBlock Text="{lex:Loc BattlegroundsPreLobby_Disabled}" TextWrapping="Wrap" />
                    </DockPanel>
                </StackPanel>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
