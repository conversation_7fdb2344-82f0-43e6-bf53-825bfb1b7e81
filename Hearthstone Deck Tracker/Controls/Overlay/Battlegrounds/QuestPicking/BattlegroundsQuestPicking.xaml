﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.BattlegroundsQuestPicking"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds"
             xmlns:tier7="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Tier7"
             xmlns:anim="clr-namespace:Hearthstone_Deck_Tracker.Utility.Animations"
             mc:Ignorable="d">
    <UserControl.LayoutTransform>
        <ScaleTransform ScaleX="{Binding Scaling}" ScaleY="{Binding Scaling}" CenterX="0.5" CenterY="0.5"/>
    </UserControl.LayoutTransform>
    <UserControl.Resources>
        <Style TargetType="ToolTip" BasedOn="{StaticResource BgsTooltipStyle}">
            <Setter Property="LayoutTransform" Value="{Binding LayoutTransform, RelativeSource={RelativeSource AncestorType=local:BattlegroundsQuestPicking}}"/>
        </Style>
    </UserControl.Resources>
    <Grid>
        <Grid Margin="0,154,5,0" HorizontalAlignment="Center" VerticalAlignment="Center"
              anim:FadeAnimation.Visibility="{Binding Visibility}" Visibility="Collapsed"
              anim:FadeAnimation.Direction="Down" anim:FadeAnimation.Distance="20" anim:FadeAnimation.Duration="0:0:0.2"
        >
            <ItemsControl ItemsSource="{Binding Quests}" Margin="-16 0">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Grid Height="880" Width="273" Margin="56 0">
                            <local:BattlegroundsSingleQuestView />
                        </Grid>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <UniformGrid Rows="1"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
            </ItemsControl>
        </Grid>
        <tier7:OverlayMessage DataContext="{Binding Message}" HorizontalAlignment="Center" VerticalAlignment="Bottom" Margin="738 0 0 13" />
    </Grid>
</UserControl>
