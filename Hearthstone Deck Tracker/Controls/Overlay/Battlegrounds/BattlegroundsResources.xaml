﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:converters="clr-namespace:Hearthstone_Deck_Tracker.Utility.Converters"
                    xmlns:System="clr-namespace:System;assembly=mscorlib"
                    >

    <converters:CenteredTooltipConverter x:Key="TooltipPosition"/>

    <SolidColorBrush x:Key="Tier7Purple" Color="#361637"/>
    <SolidColorBrush x:Key="Tier7Orange" Color="#FFB00D"/>
    <SolidColorBrush x:Key="Tier7Black" Color="#141617"/>
    <SolidColorBrush x:Key="Tier7Highlight" Color="#6A9D36"/>
    <SolidColorBrush x:Key="Tier7YellowButtonBackground" Color="#F1C040"/>
    <SolidColorBrush x:Key="Tier7YellowButtonBackgroundHover" Color="#CCCCCC"/>
    <SolidColorBrush x:Key="Tier7YellowButtonForeground" Color="#26200F"/>

    <Style x:Key="Tier7ButtonStyle" TargetType="{x:Type ButtonBase}">
        <Setter Property="Background" Value="{StaticResource Tier7YellowButtonBackground}" />
        <Setter Property="Foreground" Value="{StaticResource Tier7YellowButtonForeground}" />
        <Setter Property="FontWeight" Value="Bold" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Padding" Value="4" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ButtonBase}">
                    <ControlTemplate.Triggers>
                        <Trigger Property="UIElement.IsMouseOver" Value="true">
                            <Setter Property="Background" Value="{StaticResource Tier7YellowButtonBackgroundHover}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                    <Border Background="{TemplateBinding Background}" Padding="4">
                        <TextBlock Text="{TemplateBinding Content}" TextAlignment="Center" Margin="0 0 0 1"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="BgsTooltipStyle" TargetType="ToolTip">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToolTip">
                    <Grid>
                        <Border Background="{StaticResource Tier7Black}" BorderBrush="{StaticResource Tier7Purple}" BorderThickness="1"
                                Width="12" Height="12" HorizontalAlignment="Center" VerticalAlignment="Bottom" Margin="0 0 0 4">
                            <Border.RenderTransform>
                                <RotateTransform Angle="45" CenterX="6" CenterY="6"/>
                            </Border.RenderTransform>
                        </Border>
                        <Border Background="{StaticResource Tier7Black}" BorderBrush="{StaticResource Tier7Purple}" BorderThickness="1" Padding="8" MaxWidth="230" Margin="0 0 0 10">
                            <ContentPresenter Content="{TemplateBinding Content}">
                                <ContentPresenter.Resources>
                                    <Style TargetType="TextBlock">
                                        <Style.Setters>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="TextAlignment" Value="Center"/>
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="TextWrapping" Value="Wrap"/>
                                        </Style.Setters>
                                    </Style>
                                    <DataTemplate DataType="{x:Type System:String}">
                                        <TextBlock Text="{Binding}" />
                                    </DataTemplate>
                                </ContentPresenter.Resources>
                            </ContentPresenter>

                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="HorizontalOffset">
            <Setter.Value>
                <MultiBinding Converter="{StaticResource TooltipPosition}">
                    <Binding Path="ActualWidth" RelativeSource="{RelativeSource Self}"/>
                    <Binding Path="PlacementTarget.ActualWidth" RelativeSource="{RelativeSource Self}"/>
                </MultiBinding>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Placement" Value="Top"/>
        <Setter Property="VerticalOffset" Value="-4"/>
    </Style>

    <Style x:Key="BgsLeftTooltipStyle" TargetType="ToolTip">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToolTip">
                    <Border Background="{StaticResource Tier7Black}"
                            BorderBrush="{StaticResource Tier7Orange}"
                            BorderThickness="1"
                            Padding="8"
                            MaxWidth="230"
                            Margin="10,0,0,0">
                        <ContentPresenter Content="{TemplateBinding Content}">
                            <ContentPresenter.Resources>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="White" />
                                    <Setter Property="TextAlignment" Value="Center" />
                                    <Setter Property="FontSize" Value="14" />
                                    <Setter Property="TextWrapping" Value="Wrap" />
                                </Style>
                                <DataTemplate DataType="{x:Type System:String}">
                                    <TextBlock Text="{Binding}" />
                                </DataTemplate>
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Placement" Value="Left" />
    </Style>
</ResourceDictionary>
