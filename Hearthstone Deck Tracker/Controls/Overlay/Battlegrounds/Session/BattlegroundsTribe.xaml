﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Session.BattlegroundsTribe"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Session"
             mc:Ignorable="d">
    <StackPanel Orientation="Vertical">
        <Canvas Width="38" Height="38">
            <Border BorderBrush="{Binding BorderColor, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTribe}}" BorderThickness="2" CornerRadius="36">
                <Ellipse Height="34" Width="34">
                    <Ellipse.Fill>
                        <ImageBrush
                            x:Name="ImgBrush"
                            ImageSource="{Binding ImageSrc, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTribe}}"
                        >
                            <ImageBrush.Transform>
                                <ScaleTransform ScaleX="1.1" ScaleY="1.1" CenterX="17" CenterY="35" />
                            </ImageBrush.Transform>
                        </ImageBrush>
                    </Ellipse.Fill>
                </Ellipse>
            </Border>
            <Image
                Width="26"
                Canvas.Left="16"
                Canvas.Top="15"
                Source="/HearthstoneDeckTracker;component/Resources/tribes-x.png"
                Visibility="{Binding XVisibility, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTribe}}"
            />
        </Canvas>
        <StackPanel Height="16" Margin="0,2,0,0" Orientation="Vertical">
            <hearthstoneDeckTracker:HearthstoneTextBlock
                Text="{Binding TribeName, RelativeSource={RelativeSource AncestorType=local:BattlegroundsTribe}}"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="10"
                MaxWidth="38"
                Height="16"
            />
        </StackPanel>
    </StackPanel>
</UserControl>
