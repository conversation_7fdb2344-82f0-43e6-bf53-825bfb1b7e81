﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Session.BattlegroundsSession"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Session"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             xmlns:composition="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Composition"
             xmlns:tier7="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Tier7"
             xmlns:animations="clr-namespace:Hearthstone_Deck_Tracker.Utility.Animations"
             xmlns:commands="clr-namespace:Hearthstone_Deck_Tracker.Commands"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d"
             Width="240"
    >
    <Border
        BorderBrush="#4A5256"
        BorderThickness="1"
        CornerRadius="{Binding CornerRadius, RelativeSource={RelativeSource AncestorType=local:BattlegroundsSession}}"
        Width="240"
    >
        <Grid
            Name="BattlegroundsSessionPanelTopGroup"
            MouseEnter="Panel_MouseEnter"
            MouseLeave="Panel_MouseLeave"
        >
            <Border
                Name="BorderMask"
                Background="#2E3235"
                CornerRadius="{Binding CornerRadius, RelativeSource={RelativeSource AncestorType=local:BattlegroundsSession}}"
            />
            <StackPanel Name="BattlegroundsSessionPanel" VerticalAlignment="Top">
                <StackPanel.OpacityMask>
                    <VisualBrush Visual="{Binding ElementName=BorderMask}" />
                </StackPanel.OpacityMask>
                <StackPanel
                    Orientation="Vertical"
                >
                    <StackPanel Background="#2E3235" Visibility="{Binding MinionTypesSectionVisibility}">
                        <Border BorderBrush="#4A5256" BorderThickness="0,0,0,1" Background="#1C2022">
                            <hearthstoneDeckTracker:HearthstoneTextBlock Text="{Binding AvailableMinionTypesHeaderLabel}" d:Text="{lex:Loc Battlegrounds_Session_Header_Label_Minions_MinionTypes}" FontSize="13" HorizontalAlignment="Center" Margin="0,6"/>
                        </Border>
                        <Grid>
                            <StackPanel
                                Visibility="{Binding MinionTypesBodyVisibility, FallbackValue=Visible}"
                            >
                                <ItemsControl
                                    ItemsSource="{Binding AvailableMinionTypes}"
                                    d:ItemsSource="{d:SampleData ItemCount=5}"
                                >
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Margin="8,0,0,0">
                                                <local:BattlegroundsTribe
                                                    Tribe="{Binding}"
                                                    Availability="Available"
                                                />
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <StackPanel
                                                Visibility="{Binding AvailableMinionTypesSectionVisibility}"
                                                Orientation="Horizontal"
                                                Margin="0,8,8,4"
                                                HorizontalAlignment="Center"
                                                Height="58"
                                            />
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                </ItemsControl>
                                <Separator Background="#4A5256" Visibility="{Binding MinionTypesBorderVisibility}"/>
                                <ItemsControl
                                    ItemsSource="{Binding BannedMinionTypes}"
                                    d:ItemsSource="{d:SampleData ItemCount=5}"
                                >
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Margin="8,0,0,0">
                                                <local:BattlegroundsTribe
                                                    Tribe="{Binding}"
                                                    Availability="Banned"
                                                />
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <StackPanel
                                                Visibility="{Binding BannedMinionTypesSectionVisibility}"
                                                Orientation="Horizontal"
                                                Margin="0,8,8,4"
                                                HorizontalAlignment="Center"
                                                Height="58"
                                            />
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                </ItemsControl>
                            </StackPanel>
                            <TextBlock
                                Text="{lex:Loc Battlegrounds_Session_Text_WaitingNextGame}"
                                Foreground="#FFFFFF"
                                Opacity=".5"
                                TextWrapping="WrapWithOverflow"
                                TextAlignment="Center"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Visibility="{Binding MinionTypesWaitingMsgVisibility, FallbackValue=Collapsed}"
                                Margin="0 0 0 5"
                            />
                        </Grid>
                    </StackPanel>

                    <StackPanel VerticalAlignment="Top" animations:FadeAnimation.Visibility="{Binding AvailableCompStatsSectionVisibility}"
                                animations:FadeAnimation.Direction="Up" animations:FadeAnimation.Distance="10" animations:FadeAnimation.Duration="0:0:0.2">
                        <StackPanel.OpacityMask>
                            <VisualBrush Visual="{Binding ElementName=BorderMask}" />
                        </StackPanel.OpacityMask>
                        <StackPanel Orientation="Vertical">
                            <StackPanel Background="#2E3235">

                                <Border BorderBrush="#4A5256" BorderThickness="0,1,0,1" Background="#1C2022" VerticalAlignment="Center">
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <tier7:Tier7Logo Width="16" Height="16"  VerticalAlignment="Center" />
                                        <hearthstoneDeckTracker:HearthstoneTextBlock Text="{lex:Loc Battlegrounds_Session_Header_Label_CompStats}" d:Text="Composition Stats" FontSize="13" HorizontalAlignment="Center" Margin="6,6, 0, 6"/>
                                    </StackPanel>
                                </Border>

                                <Grid>
                                    <StackPanel Visibility="{Binding CompStatsBodyVisibility, FallbackValue=Visible}">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="0.9*"/>
                                        <ColumnDefinition Width="70px"/>
                                        <ColumnDefinition Width="0.8*"/>
                                    </Grid.ColumnDefinitions>
                                    <Label
                                        Grid.Column="0"
                                        Content="{lex:Loc Battlegrounds_Session_CompStats_Label_Composition}"
                                        Foreground="#FFFFFF"
                                        Opacity=".55"
                                        FontSize="11"
                                        HorizontalAlignment="Left"
                                        Margin="2,0,0,0"
                                    />
                                    <Label
                                        Grid.Column="1"
                                        Content="{lex:Loc Battlegrounds_Session_CompStats_Label_FirstPlace}"
                                        Foreground="#FFFFFF"
                                        Opacity=".55"
                                        FontSize="11"
                                        HorizontalAlignment="Center"
                                    />
                                    <Label
                                        Grid.Column="2"
                                        Content="{lex:Loc Battlegrounds_Session_CompStats_Label_AveragePlace}"
                                        Foreground="#FFFFFF"
                                        Opacity=".55"
                                        FontSize="11"
                                        HorizontalAlignment="Center"
                                    />
                                    </Grid>
                                    <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="150">
                                        <ItemsControl ItemsSource="{Binding CompositionStats}" d:ItemsSource="{d:SampleData ItemCount=6}" HorizontalContentAlignment="Center" >
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <composition:BattlegroundsCompositionStatsRow DataContext="{Binding}" />
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                            <ItemsControl.ItemsPanel>
                                                <ItemsPanelTemplate>
                                                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center"/>
                                                </ItemsPanelTemplate>
                                            </ItemsControl.ItemsPanel>
                                        </ItemsControl>
                                    </ScrollViewer>
                                    </StackPanel>
                                    <TextBlock
                                        Text="{lex:Loc Battlegrounds_Session_Text_WaitingNextGame}"
                                        Foreground="#FFFFFF"
                                        Opacity=".5"
                                        TextWrapping="WrapWithOverflow"
                                        TextAlignment="Center"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Visibility="{Binding CompStatsWaitingMsgVisibility, FallbackValue=Collapsed}"
                                        Margin="0 26 0 26"
                                    />
                                    <TextBlock
                                        Text="{lex:Loc Battlegrounds_Session_CompStats_ErrorMessage}"
                                        Foreground="#FFFFFF"
                                        Opacity=".5"
                                        TextWrapping="WrapWithOverflow"
                                        TextAlignment="Center"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Visibility="{Binding CompStatsErrorVisibility, FallbackValue=Collapsed}"
                                        Margin="0 26 0 26"
                                    />
                                </Grid>
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>

                    <StackPanel Background="#2E3235" Visibility="{Binding BgStartCurrentMMRSectionVisibility}">
                        <Border BorderBrush="#4A5256" BorderThickness="0,1,0,0" Visibility="{Binding ElementName=BgBannedTribesSection, Path=Visibility}"/>
                        <Border BorderBrush="#4A5256" BorderThickness="0,0,0,1" Background="#1C2022">
                            <hearthstoneDeckTracker:HearthstoneTextBlock Text="{lex:Loc Battlegrounds_Session_Header_Label_MMR}" FontSize="13" HorizontalAlignment="Center" Margin="0,6"/>
                        </Border>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="0.5*"/>
                                <ColumnDefinition Width="0.5*"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0" Orientation="Vertical">
                                <Label
                                    Content="{lex:Loc Battlegrounds_Session_MMR_Label_Start}"
                                    Foreground="#FFFFFF"
                                    Opacity=".55"
                                    FontSize="11"
                                    HorizontalAlignment="Center"
                                />
                                <hearthstoneDeckTracker:HearthstoneTextBlock
                                    Text="{Binding BgRatingStart, FallbackValue=0}"
                                    FontSize="18"
                                    HorizontalAlignment="Center"
                                    Margin="0,-5,0,8"
                                />
                            </StackPanel>
                            <Border Grid.Column="1" BorderBrush="#4A5256" BorderThickness="1,0,0,0">
                                <StackPanel Orientation="Vertical">
                                    <Label
                                        Content="{lex:Loc Battlegrounds_Session_MMR_Label_Current}"
                                        Foreground="#FFFFFF"
                                        Opacity=".55"
                                        FontSize="11"
                                        HorizontalAlignment="Center"
                                    />
                                    <hearthstoneDeckTracker:HearthstoneTextBlock
                                        Text="{Binding BgRatingCurrent, FallbackValue=0}"
                                        FontSize="18"
                                        HorizontalAlignment="Center"
                                        Margin="0,-5,0,8"
                                    />
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </StackPanel>
                <StackPanel Visibility="{Binding BgLatestGamesSectionVisibility}">
                    <StackPanel
                        Name="BgLatestGamesSectionHeader"
                        Background="#2E3235"
                        Orientation="Vertical"
                    >
                        <Border BorderBrush="#4A5256" BorderThickness="0,1,0,0" Visibility="{Binding ElementName=BgStartCurrentMMRSection, Path=Visibility}"/>
                        <Border BorderBrush="#4A5256" BorderThickness="0,0,0,1" Background="#1C2022">
                            <hearthstoneDeckTracker:HearthstoneTextBlock Text="{lex:Loc Battlegrounds_Session_Header_Label_Latest_Games}" FontSize="13" HorizontalAlignment="Center" Margin="0,6"/>
                        </Border>
                        <Grid Visibility="{Binding GridHeaderVisibility, FallbackValue=Collapsed}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1.7*"/>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>
                            <Label
                                Grid.Column="0"
                                Content="{lex:Loc Battlegrounds_Session_Games_Label_Hero}"
                                Foreground="#FFFFFF"
                                Opacity=".55"
                                FontSize="11"
                                HorizontalAlignment="Center"
                            />
                            <Label
                                Grid.Column="1"
                                Content="{lex:Loc Battlegrounds_Session_Games_Label_Final}"
                                Foreground="#FFFFFF"
                                Opacity=".55"
                                FontSize="11"
                                HorizontalAlignment="Center"
                            />
                            <Label
                                Grid.Column="2"
                                Content="{lex:Loc Battlegrounds_Session_Games_Label_MMR}"
                                Foreground="#FFFFFF"
                                Opacity=".55"
                                FontSize="11"
                                HorizontalAlignment="Center"
                            />
                        </Grid>
                        <StackPanel Visibility="{Binding GamesEmptyStateVisibility, FallbackValue=Collapsed}">
                            <TextBlock
                                Text="{lex:Loc Battlegrounds_Session_Games_Text_EmptyState1}"
                                Foreground="#FFFFFF"
                                Opacity=".5"
                                TextWrapping="WrapWithOverflow"
                                TextAlignment="Center"
                                HorizontalAlignment="Center"
                                Margin="4,8,4,0"
                            />
                            <TextBlock
                                Text="{lex:Loc Battlegrounds_Session_Games_Text_EmptyState2}"
                                Foreground="#FFFFFF"
                                Opacity=".5"
                                TextWrapping="WrapWithOverflow"
                                TextAlignment="Center"
                                HorizontalAlignment="Center"
                                Margin="4,0,0,8"
                            />
                        </StackPanel>
                    </StackPanel>
                    <ItemsControl
                        Name="ItemsControlGames"
                        ItemsSource="{Binding SessionGames}"
                    >
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <local:BattlegroundsGameView
                                    DataContext="{Binding}"
                                    FinalBoardTooltip="{Binding FinalBoardTooltip, RelativeSource={RelativeSource AncestorType=local:BattlegroundsSession}}"
                                />
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <StackPanel />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                    </ItemsControl>
                </StackPanel>
            </StackPanel>
            <Border
                Name="BtnOptions"
                Height="23"
                Width="24"
                CornerRadius="3"
                Background="Transparent"
                Cursor="Hand"
                MouseEnter="BtnOptions_MouseEnter"
                MouseLeave="BtnOptions_MouseLeave"
                VerticalAlignment="Top"
                HorizontalAlignment="Right"
                Margin="4"
                Visibility="{Binding CogBtnVisibility, RelativeSource={RelativeSource AncestorType=local:BattlegroundsSession}}"
            >
                <Border.InputBindings>
                    <MouseBinding MouseAction="LeftClick" Command="commands:GlobalCommands.ShowSettings" CommandParameter="Battlegrounds" />
                </Border.InputBindings>
                <Rectangle
                    Height="14"
                    Width="14"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                >
                    <Rectangle.Fill>
                        <VisualBrush Visual="{StaticResource appbar_settings}" />
                    </Rectangle.Fill>
                </Rectangle>
            </Border>
        </Grid>
    </Border>
</UserControl>
