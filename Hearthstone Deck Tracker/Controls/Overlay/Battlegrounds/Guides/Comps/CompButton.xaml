﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Guides.Comps.CompButton"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             mc:Ignorable="d"
             d:DesignHeight="300" d:DesignWidth="300">
    <Button Cursor="Hand" DataContext="{Binding}" Click="Button_OnClick" Height="48" Margin="0" Padding="0">
        <Button.Style>
            <Style TargetType="Button">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border>
                                <Grid>
                                    <Rectangle x:Name="HoverRectangle" Opacity="0.2">
                                        <Rectangle.Fill>
                                            <ImageBrush
                                                x:Name="ImgBrush"
                                                ImageSource="{Binding CardAsset.Asset}" Stretch="None">
                                                <ImageBrush.Transform>
                                                    <TranslateTransform X="-30" Y="0" />
                                                </ImageBrush.Transform>
                                            </ImageBrush>
                                        </Rectangle.Fill>
                                        <Rectangle.OpacityMask>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0.70,0">
                                                <GradientStop Color="#292d30" Offset="0.5"/>
                                                <GradientStop Color="Transparent" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Rectangle.OpacityMask>
                                    </Rectangle>
                                    <ContentPresenter />
                                </Grid>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="HoverRectangle" Property="Opacity" Value="0.4" />
                                </Trigger>
                                <Trigger Property="IsMouseOver" Value="False">
                                    <Setter TargetName="HoverRectangle" Property="Opacity" Value="0.2" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </Button.Style>
        <Grid HorizontalAlignment="Stretch" VerticalAlignment="Center">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <hearthstoneDeckTracker:OutlinedTextBlock
                Margin="10,0,10,0"
                Text="{Binding CompGuide.Name}"
                FontSize="12"
                TextWrapping="Wrap"
                TextTrimming="None"
                FontFamily="/HearthstoneDeckTracker;component/Resources/#Chunkfive"
            />
            <Rectangle Grid.Column="1" Width="8" Height="8" Opacity="0.5" Margin="0,0,14,0">
                <Rectangle.Fill>
                    <VisualBrush Visual="{StaticResource appbar_chevron_right_white}" />
                </Rectangle.Fill>
            </Rectangle>
        </Grid>
    </Button>
</UserControl>

