﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Guides.Comps.CompGuide"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Guides.Comps"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             xmlns:controls="clr-namespace:Hearthstone_Deck_Tracker.Controls"
             xmlns:extensions="clr-namespace:Hearthstone_Deck_Tracker.Utility.Extensions"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             xmlns:tier7="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Tier7"
             xmlns:tooltips="clr-namespace:Hearthstone_Deck_Tracker.Controls.Tooltips"
             xmlns:guides="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Guides"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d"
             d:DesignHeight="300" d:DesignWidth="300"
             d:DataContext="{d:DesignInstance local:BattlegroundsCompGuideViewModel}">
    <UserControl.Resources>
        <Style TargetType="TextBlock">
            <Style.Setters>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="TextAlignment" Value="Center"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="TextWrapping" Value="Wrap"/>
            </Style.Setters>
        </Style>

        <Style x:Key="BgsTooltipStyle" TargetType="ToolTip" BasedOn="{StaticResource BgsLeftTooltipStyle}"/>
        <Style x:Key="StyledBackButton" TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border BorderThickness="0" Margin="0,-9,0,0" Background="Transparent" Height="39.5">
                            <StackPanel Orientation="Horizontal">
                                <Rectangle Width="9" Height="9">
                                    <Rectangle.Fill>
                                        <VisualBrush Visual="{StaticResource guides_back_arrow}" />
                                    </Rectangle.Fill>
                                </Rectangle>
                                <TextBlock x:Name="BackButtonText"
                                           Foreground="White"
                                           Text="{lex:Loc Battlegrounds_CompGuide_Back_Button}"
                                           VerticalAlignment="Center"
                                           FontSize="10"
                                           Margin="5,0,0,0" />
                            </StackPanel>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="BackButtonText" Property="Opacity" Value="1" />
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="False">
                                <Setter TargetName="BackButtonText" Property="Opacity" Value="0.6" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="InspirationButtonStyle" TargetType="{x:Type ButtonBase}">
            <Setter Property="Background" Value="{StaticResource Tier7YellowButtonBackground}" />
            <Setter Property="Foreground" Value="{StaticResource Tier7YellowButtonForeground}" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="FontSize" Value="11" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Padding" Value="4" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ButtonBase}">
                        <Border CornerRadius="3" Background="{TemplateBinding Background}" Padding="4">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center">
                                <tier7:Tier7Logo x:Name="logo" Width="14" Height="14" Margin="0,0,4,-2" LogoBrush="Black"/>
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                            </StackPanel>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="UIElement.IsMouseOver" Value="true">
                                <Setter Property="Background" Value="{StaticResource Tier7YellowButtonBackgroundHover}" />
                            </Trigger>
                            <Trigger Property="UIElement.IsEnabled" Value="false">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <SolidColorBrush Color="#FFFFFF" Opacity="0.08" />
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Foreground">
                                    <Setter.Value>
                                        <SolidColorBrush Color="#FFFFFF" Opacity="0.2" />
                                    </Setter.Value>
                                </Setter>
                                <Setter TargetName="logo" Property="LogoBrush">
                                    <Setter.Value>
                                        <SolidColorBrush Color="#FFFFFF" Opacity="0.3" />
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Cursor" Value="Arrow" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style TargetType="controls:BattlegroundsMinion">
            <Setter Property="Width" Value="70"/>
            <Setter Property="Height" Value="70"/>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5" />
            <Setter Property="RenderTransform">
                <Setter.Value>
                    <ScaleTransform ScaleX="1" ScaleY="1" />
                </Setter.Value>
            </Setter>
            <Setter Property="ToolTipService.Placement" Value="Left"/>
            <EventSetter Event="extensions:OverlayExtensions.TooltipLoaded" Handler="CardTooltip_Loaded"/>
            <Setter Property="extensions:OverlayExtensions.ToolTip" Value="{x:Type tooltips:CardTooltip}" />
            <Style.Triggers>
                <Trigger Property="controls:BattlegroundsMinion.IsMouseOver" Value="True">
                    <Setter Property="controls:BattlegroundsMinion.RenderTransform">
                        <Setter.Value>
                            <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>
        <Style TargetType="guides:ReferencedCardRun">
            <Setter Property="ToolTipService.Placement" Value="Left"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <EventSetter Event="extensions:OverlayExtensions.TooltipLoaded" Handler="CardTooltip_Loaded"/>
            <Setter Property="extensions:OverlayExtensions.ToolTip" Value="{x:Type tooltips:CardTooltip}" />
        </Style>
        <Style TargetType="ToolTip">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
        </Style>
    </UserControl.Resources>
    <Grid d:Background="#292d30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Button Click="Button_OnClick" Style="{StaticResource StyledBackButton}" Cursor="Hand">
        </Button>
        <Border Grid.Row="1" BorderBrush="#4F565B" BorderThickness="1" CornerRadius="3" Background="#292d30">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <Grid Height="48">
                    <Border BorderThickness="0, 0, 0, 1" BorderBrush="#4A5256" Background="#141617" CornerRadius="3,3,0,0">
                        <Grid>
                            <Rectangle Opacity="0.4">
                                <Rectangle.Fill>
                                    <ImageBrush
                                        ImageSource="{Binding CardAsset.Asset}" Stretch="None">
                                        <ImageBrush.Transform>
                                            <TranslateTransform X="-30" Y="0" />
                                        </ImageBrush.Transform>
                                    </ImageBrush>
                                </Rectangle.Fill>
                                <Rectangle.OpacityMask>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0.80,0">
                                        <GradientStop Color="#141617" Offset="0.5"/>
                                        <GradientStop Color="Transparent" Offset="1"/>
                                    </LinearGradientBrush>
                                </Rectangle.OpacityMask>
                            </Rectangle>
                            <Grid Margin="9,0,9,0" HorizontalAlignment="Stretch">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <hearthstoneDeckTracker:OutlinedTextBlock
                                    Text="{Binding CompGuide.Name}"
                                    FontSize="13"
                                    TextWrapping="Wrap"
                                    TextTrimming="None"
                                    FontFamily="/HearthstoneDeckTracker;component/Resources/#Chunkfive"
                                />
                                <Border Grid.Column="1" VerticalAlignment="Center" Background="{Binding TierColor}" CornerRadius="3" Padding="8,2,8,5" HorizontalAlignment="Right">
                                    <TextBlock
                                        Text="{Binding TierText}"
                                        FontSize="12"
                                        Foreground="White"
                                        FontWeight="Black"
                                    />
                                </Border>
                            </Grid>
                        </Grid>
                    </Border>
                </Grid>
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" Background="#2e3235" extensions:OverlayExtensions.IsOverlayHitTestVisible="True">
                    <StackPanel>
                        <StackPanel Margin="9">
                            <Grid Margin="0,0,0,7" HorizontalAlignment="Stretch">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <hearthstoneDeckTracker:OutlinedTextBlock Grid.Column="0" FontSize="12" Text="{lex:Loc Battlegrounds_CompGuide_HowToPlay}" FontFamily="/HearthstoneDeckTracker;component/Resources/#Chunkfive"/>
                                <Border Grid.Column="1" Background="{Binding DifficultyColor}" CornerRadius="3" Padding="8 4" HorizontalAlignment="Right">
                                    <TextBlock Text="{Binding DifficultyText}" Foreground="White" FontWeight="Bold" FontSize="10"/>
                                </Border>
                            </Grid>
                            <TextBlock TextAlignment="Left" TextWrapping="Wrap" FontSize="11" LineHeight="17" Foreground="White" Opacity="0.7" guides:TextBlockExtensions.BindableInlines="{Binding HowToPlay}" Margin="0,0,0,3" />
                        </StackPanel>
                        <Border BorderThickness="0, 1, 0, 0" BorderBrush="#4A5256" Background="#1c1f22" Padding="0,9,0,9">
                            <StackPanel>
                                <hearthstoneDeckTracker:OutlinedTextBlock FontSize="12" Text="{lex:Loc Battlegrounds_CompGuide_CoreCards}" Margin="9,0,0,9" FontFamily="/HearthstoneDeckTracker;component/Resources/#Chunkfive"/>
                                <ItemsControl ItemsSource="{Binding CoreCards}" Margin="-7,0,-7,0">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <WrapPanel HorizontalAlignment="Center" VerticalAlignment="Top" ItemWidth="Auto" ItemHeight="Auto"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <controls:BattlegroundsMinion
                                                Margin="0,10"
                                            />
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                                <Border ToolTipService.InitialShowDelay="500">
                                    <Border.ToolTip>
                                        <ToolTip
                                            Visibility="{Binding ExampleBoardsButtonEnabled, Converter={StaticResource InverseBoolToVisibility}}"
                                            Style="{StaticResource BgsTooltipStyle}"
                                            Content="{lex:Loc Battlegrounds_CompGuide_CoreCards_Tier7_Tooltip}"
                                            VerticalOffset="-8" />
                                    </Border.ToolTip>
                                    <Button Style="{StaticResource InspirationButtonStyle}"
                                            Content="{lex:Loc Battlegrounds_CompGuide_Inspiration_Button}"
                                            Command="{Binding ShowExampleBoardsCommand}"
                                            Margin="9,0"
                                            IsEnabled="{Binding ExampleBoardsButtonEnabled}"
                                    >
                                    </Button>
                                </Border>
                            </StackPanel>
                        </Border>
                        <Border BorderThickness="0, 1, 0, 0" BorderBrush="#4A5256" Background="#1c1f22" Padding="0,9,0,9">
                            <StackPanel>
                                <hearthstoneDeckTracker:OutlinedTextBlock FontSize="12" Text="{lex:Loc Battlegrounds_CompGuide_AddonCards}" Margin="9,0,0,9" FontFamily="/HearthstoneDeckTracker;component/Resources/#Chunkfive"/>
                                <ItemsControl ItemsSource="{Binding AddonCards}" Margin="-7,0,-7,0">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <WrapPanel HorizontalAlignment="Center" VerticalAlignment="Top" ItemWidth="Auto" ItemHeight="Auto"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <controls:BattlegroundsMinion
                                                Margin="0,10"
                                            />
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>
                        <Border BorderThickness="0, 1, 0, 0" BorderBrush="#4A5256" Padding="9" >
                            <StackPanel>
                                <hearthstoneDeckTracker:OutlinedTextBlock FontSize="12" Text="{lex:Loc Battlegrounds_CompGuide_WhenToCommit}" Margin="0,0,0,9" FontFamily="/HearthstoneDeckTracker;component/Resources/#Chunkfive"/>
                                <ItemsControl ItemsSource="{Binding WhenToCommitTags}" Margin="0">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <StackPanel HorizontalAlignment="Stretch" VerticalAlignment="Center" Margin="-3" />
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="#5C000000" CornerRadius="3" Padding="8,6" Margin="3" HorizontalAlignment="Stretch">
                                                <TextBlock HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" FontSize="11" Foreground="White" Opacity="0.8" guides:TextBlockExtensions.BindableInlines="{Binding }" />
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>
                        <Border BorderThickness="0, 1, 0, 0" BorderBrush="#4A5256" Padding="9" >
                            <StackPanel>
                                <hearthstoneDeckTracker:OutlinedTextBlock FontSize="12" Text="{lex:Loc Battlegrounds_CompGuide_CommonEnablers}" Margin="0,0,0,9" FontFamily="/HearthstoneDeckTracker;component/Resources/#Chunkfive"/>
                                <ItemsControl ItemsSource="{Binding CommonEnablerTags}" Margin="0">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <StackPanel HorizontalAlignment="Stretch" VerticalAlignment="Center" Margin="-3" />
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="#5C000000" CornerRadius="3" Padding="8,6" Margin="3" HorizontalAlignment="Stretch">
                                                <TextBlock HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" FontSize="11" Foreground="White" Opacity="0.8" guides:TextBlockExtensions.BindableInlines="{Binding }"/>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </Border>
        <Border Grid.Row="2" BorderThickness="0, 1, 0, 0" BorderBrush="#4A5256" Background="#141617" Margin="-9,9,-9,-9">
            <Grid Margin="7" HorizontalAlignment="Stretch">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <StackPanel HorizontalAlignment="Left">
                    <TextBlock Text="{lex:Loc Battlegrounds_CompGuide_CreatedBy}" Foreground="White" FontSize="9" Opacity="0.8"/>
                    <TextBlock Text="JeefHS" Foreground="White" FontSize="10" FontWeight="Bold"/>
                </StackPanel>

                <StackPanel Grid.Column="1">
                    <TextBlock Text="{lex:Loc Battlegrounds_CompGuide_LastUpdated}" Foreground="White" HorizontalAlignment="Right" FontSize="9" Opacity="0.8"/>
                    <TextBlock Text="{Binding LastUpdatedFormatted}" Foreground="White" FontSize="10" FontWeight="Bold" HorizontalAlignment="Right"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
