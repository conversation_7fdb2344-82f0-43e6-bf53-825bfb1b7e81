﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Guides.GuidesTabs"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Guides"
             xmlns:minions="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Minions"
             xmlns:comps="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Guides.Comps"
             xmlns:extensions="clr-namespace:Hearthstone_Deck_Tracker.Utility.Extensions"
             xmlns:heroes="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Guides.Heroes"
             mc:Ignorable="d"
             Width="249"
             d:DesignHeight="249" d:DesignWidth="249">

    <UserControl.Resources>
        <Style TargetType="Button">
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Background" Value="#141617" />
            <Setter Property="BorderThickness" Value="1,0,0,1" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Width="83"
                                Height="49"
                                BorderBrush="#3f4346"
                        >
                            <ContentPresenter
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#2C3135" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    <UserControl.DataContext>
        <local:GuidesTabsViewModel />
    </UserControl.DataContext>

    <StackPanel extensions:OverlayExtensions.IsOverlayHitTestVisible="True">
        <StackPanel Orientation="Horizontal" VerticalAlignment="Top" Background="#2C3135">
            <Button Command="{Binding ShowMinionsCommand}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource {x:Type Button}}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding ActiveViewModel, Converter={StaticResource ObjectTypeConverter}}" Value="{x:Type minions:BattlegroundsMinionsViewModel}">
                                <Setter Property="Background" Value="#23272A" />
                                <Setter Property="BorderThickness" Value="1,0,0,0" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
                <Grid Width="83"
                      Height="49">
                    <Rectangle Fill="White" Height="23" Width="18.2">
                        <Rectangle.OpacityMask>
                            <VisualBrush Visual="{DynamicResource icon_card}" />
                        </Rectangle.OpacityMask>
                    </Rectangle>
                </Grid>
            </Button>

            <Button Command="{Binding ShowCompsCommand}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource {x:Type Button}}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding ActiveViewModel, Converter={StaticResource ObjectTypeConverter}}" Value="{x:Type local:BattlegroundsCompsGuidesViewModel}">
                                <Setter Property="Background" Value="#23272A" />
                                <Setter Property="BorderThickness" Value="1,0,0,0" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
                <Grid Width="83"
                      Height="49">
                    <Rectangle Fill="White" Height="23" Width="31">
                        <Rectangle.OpacityMask>
                            <VisualBrush Visual="{DynamicResource icon_comp}" />
                        </Rectangle.OpacityMask>
                    </Rectangle>
                </Grid>
            </Button>

            <Button Command="{Binding ShowHeroesCommand}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource {x:Type Button}}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding ActiveViewModel, Converter={StaticResource ObjectTypeConverter}}" Value="{x:Type heroes:BattlegroundsHeroGuideListViewModel}">
                                <Setter Property="Background" Value="#23272A" />
                                <Setter Property="BorderThickness" Value="1,0,0,0" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
                <Grid Width="83"
                      Height="49">
                    <Rectangle Fill="White" Height="23" Width="21">
                        <Rectangle.OpacityMask>
                            <VisualBrush Visual="{DynamicResource icon_hero}" />
                        </Rectangle.OpacityMask>
                    </Rectangle>
                </Grid>
            </Button>
        </StackPanel>
        <Border Visibility="{Binding ActiveViewModel, Converter={StaticResource NullableToVisibility}}"
                x:Name="TabsContent"
            >
            <ContentControl x:Name="ActiveContent" Content="{Binding ActiveViewModel}">
                <ContentControl.Resources>
                    <minions:BattlegroundsMinions x:Key="Minions" />
                    <comps:CompGuideList x:Key="Comps" />
                    <heroes:HeroGuide x:Key="Heroes" />

                    <DataTemplate DataType="{x:Type minions:BattlegroundsMinionsViewModel}">
                        <ContentControl Content="{StaticResource Minions}"/>
                    </DataTemplate>
                    <DataTemplate DataType="{x:Type local:BattlegroundsCompsGuidesViewModel}">
                        <ContentControl Content="{StaticResource Comps}"/>
                    </DataTemplate>
                    <DataTemplate DataType="{x:Type heroes:BattlegroundsHeroGuideListViewModel}">
                        <ContentControl Content="{StaticResource Heroes}"/>
                    </DataTemplate>
                </ContentControl.Resources>
            </ContentControl>
        </Border>
    </StackPanel>
</UserControl>
