﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Minions.BattlegroundsMinionType"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Minions"
             mc:Ignorable="d">
    <StackPanel Orientation="Vertical">
        <Canvas Width="{Binding Width, RelativeSource={RelativeSource AncestorType=UserControl}}"
                Height="{Binding Height, RelativeSource={RelativeSource AncestorType=UserControl}}">
            <Border CornerRadius="38">
                <Ellipse Width="{Binding Width, RelativeSource={RelativeSource AncestorType=UserControl}}"
                         Height="{Binding Height, RelativeSource={RelativeSource AncestorType=UserControl}}">
                    <Ellipse.Fill>
                        <ImageBrush
                            x:Name="ImgBrush"
                            ImageSource="{Binding ImageSrc, RelativeSource={RelativeSource AncestorType=local:BattlegroundsMinionType}}"
                        >
                            <ImageBrush.Transform>
                                <ScaleTransform ScaleX="1.1" ScaleY="1.1" CenterX="17" CenterY="35" />
                            </ImageBrush.Transform>
                        </ImageBrush>
                    </Ellipse.Fill>
                </Ellipse>
            </Border>
        </Canvas>
    </StackPanel>
</UserControl>
