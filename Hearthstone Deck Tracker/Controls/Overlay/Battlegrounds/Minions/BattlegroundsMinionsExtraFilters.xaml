<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Minions.BattlegroundsMinionsExtraFilters"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Minions"
             xmlns:extensions="clr-namespace:Hearthstone_Deck_Tracker.Utility.Extensions"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             extensions:OverlayExtensions.IsOverlayHitTestVisible="True"
             d:DataContext="{d:DesignInstance local:BattlegroundsMinionsViewModel}"
             mc:Ignorable="d">
    <Border HorizontalAlignment="Right"
        Background="#23272A"
        CornerRadius="3"
        BorderBrush="#3f4346"
        BorderThickness="1"
        Width="200"
    >
        <StackPanel Orientation="Vertical" HorizontalAlignment="Right">
            <Border BorderThickness="0, 0, 0, 1" BorderBrush="#4A5256" Background="#141617" Padding="0,9,0,9">
                <hearthstoneDeckTracker:OutlinedTextBlock HorizontalAlignment="Center" FontSize="13" Text="{lex:Loc Battlegrounds_Browser_Filter_CardTypes}" Margin="9,0,0,0" FontFamily="/HearthstoneDeckTracker;component/Resources/#Chunkfive"/>
            </Border>
            <ItemsControl Margin="4,4,4,8" ItemsSource="{Binding MinionTypeButtons}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <WrapPanel ItemWidth="38" ItemHeight="40" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <local:BattlegroundsMinionTypeButton
                            Margin="0,1"
                            MinionType="{Binding MinionType}"
                            Active="{Binding Active}"
                            Available="{Binding Available}"
                            Faded="{Binding Faded}"
                            Width="{Binding Size}"
                            Height="{Binding Size}"
                            ClickMinionTypeCommand="{Binding SetActiveMinionTypeCommand, RelativeSource={RelativeSource AncestorType=local:BattlegroundsMinionsExtraFilters}}"
                        />
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
            <Border BorderThickness="0, 1, 0, 0" BorderBrush="#4A5256" Background="#141617" Padding="0,9,0,9">
                <hearthstoneDeckTracker:OutlinedTextBlock HorizontalAlignment="Center" FontSize="13" Text="{lex:Loc Battlegrounds_Browser_Filter_Mechanics}" Margin="9,0,0,0" FontFamily="/HearthstoneDeckTracker;component/Resources/#Chunkfive"/>
            </Border>
            <ItemsControl Name="KeywordIcons" ItemsSource="{Binding KeywordButtons}">
                <ItemsControl.Resources>
                    <Style TargetType="Button">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Grid>
                                        <Border x:Name="border"
                                                BorderThickness="0,1,0,0"
                                                BorderBrush="#3f4346">
                                            <Grid Background="Transparent">
                                                <TextBlock x:Name="contentText"
                                                          Text="{TemplateBinding Content}"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Foreground="{TemplateBinding Foreground}"
                                                          FontSize="{TemplateBinding FontSize}"
                                                          FontWeight="{TemplateBinding FontWeight}"
                                                          />
                                            </Grid>
                                        </Border>
                                        <!-- Left and Right triangle indicators -->
                                        <Grid x:Name="selectedGrid" Visibility="Collapsed">
                                            <Path HorizontalAlignment="Left"
                                                  VerticalAlignment="Center"
                                                  Fill="#ffffcc"
                                                  Data="M 0,14 L 7,7 L 0,0 Z"
                                                  Margin="0,0,0,0" />


                                            <Path HorizontalAlignment="Right"
                                                  VerticalAlignment="Center"
                                                  Fill="#ffffcc"
                                                  Data="M 7,14 L 0,7 L 7,0 Z"
                                                  Margin="0,0,0,0" />
                                        </Grid>
                                    </Grid>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="border" Property="Background" Value="#2C3135"/>
                                        </Trigger>
                                        <DataTrigger Binding="{Binding Active}" Value="True">
                                            <Setter TargetName="border" Property="Background" Value="#36393f"/>
                                            <Setter TargetName="selectedGrid" Property="Visibility" Value="Visible"/>
                                            <Setter Property="Foreground" Value="#ffffcc"/>
                                        </DataTrigger>
                                        <MultiDataTrigger>
                                            <MultiDataTrigger.Conditions>
                                                <Condition Binding="{Binding Active}" Value="True"/>
                                                <Condition Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Self}}" Value="True"/>
                                            </MultiDataTrigger.Conditions>
                                            <Setter TargetName="selectedGrid" Property="Opacity" Value="0.3"/>
                                            <Setter Property="Opacity" Value="0.6"/>
                                        </MultiDataTrigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Setter Property="Foreground" Value="#dcddde"/>
                        <Setter Property="FontSize" Value="11"/>
                        <Setter Property="FontWeight" Value="Bold" />
                        <Setter Property="Cursor" Value="Hand"/>
                        <Setter Property="HorizontalContentAlignment" Value="Left"/>
                        <Setter Property="Height" Value="30"/>
                    </Style>
                </ItemsControl.Resources>
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Button
                            Content="{Binding KeywordName}"
                            Tag="{Binding Keyword}"
                            Command="{Binding SetActiveKeywordCommand, RelativeSource={RelativeSource AncestorType=local:BattlegroundsMinionsExtraFilters}}"
                            CommandParameter="{Binding Keyword}"
                        />
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </StackPanel>
    </Border>
</UserControl>
