﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Minions.BattlegroundsMinionTypesBox"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:lex="http://wpflocalizeextension.codeplex.com"
      lex:LocalizeDictionary.DesignCulture="en"
      lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
      lex:ResxLocalizationProvider.DefaultDictionary="Strings"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:hearthstone_deck_tracker="clr-namespace:Hearthstone_Deck_Tracker"
      xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Minions"
      mc:Ignorable="d"
      d:DesignHeight="450" d:DesignWidth="800">

    <Border BorderBrush="#141617" BorderThickness="1" Background="#23272a" Margin="0,5,0,0" Width="240">
        <StackPanel>
            <Border Background="#1d3657" BorderBrush="#141617" BorderThickness="0,0,0,1">
                <hearthstone_deck_tracker:HearthstoneTextBlock FontSize="14"
                    Margin="7,2"
                    Text="{Binding Title, RelativeSource={RelativeSource AncestorType=local:BattlegroundsMinionTypesBox}}"
                    d:Text="Unavailable"
                 />
            </Border>
            <TextBlock
                Text="{Binding MinionTypesText, RelativeSource={RelativeSource AncestorType=local:BattlegroundsMinionTypesBox}}"
                x:Name="MinionTypesTextBlock"
                d:Text="Dragon, Elemental, Mechs, Murloc, Quilboar"
                Margin="7,5" Foreground="White" Opacity="0.6" FontWeight="SemiBold" TextWrapping="Wrap"
            />
        </StackPanel>
    </Border>
</UserControl>
