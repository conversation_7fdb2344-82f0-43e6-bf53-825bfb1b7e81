﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Minions.BattlegroundsCardsGroup"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Minions"
             xmlns:controls="clr-namespace:Hearthstone_Deck_Tracker.Controls"
             xmlns:hearthstone_deck_tracker="clr-namespace:Hearthstone_Deck_Tracker"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d"
             d:DesignHeight="300" d:DesignWidth="196"
             >
    <Border BorderBrush="#141617" BorderThickness="1" Background="#23272a" Margin="0,5,0,0" Width="196">
        <Grid>
            <StackPanel Background="Transparent">
                <!-- The Transparent background above is required for hit tests -->
                <Border
                    BorderBrush="#141617"
                    BorderThickness="0,0,0,1"
                    Background="{Binding HeaderBackground, RelativeSource={RelativeSource AncestorType=local:BattlegroundsCardsGroup}}"
                >
                    <DockPanel>
                        <DockPanel DockPanel.Dock="Right" Margin="0,0,0,-1" MaxWidth="70" Height="20" VerticalAlignment="Bottom"
                                   Visibility="{Binding SubTitleVisibility, RelativeSource={RelativeSource AncestorType=local:BattlegroundsCardsGroup}}"
                                   d:Visibility="Visible">
                            <Grid Height="30" Width="20" Margin="-20,0,-5,0" ClipToBounds="True" DockPanel.Dock="Left">
                                <Border BorderThickness="1,0,0,0" BorderBrush="#141617" Background="#23272a" Width="20" Height="42" VerticalAlignment="Center">
                                    <Border.LayoutTransform>
                                        <RotateTransform Angle="15"/>
                                    </Border.LayoutTransform>
                                </Border>
                            </Grid>
                            <Border Background="#23272a"
                                    Margin="-8,0,0,0"
                                    BorderBrush="#141617"
                                    BorderThickness="0,1,0,0"
                                    Padding="2,0,0,0"
                                >
                                <Viewbox Stretch="Uniform" StretchDirection="DownOnly" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock
                                        Foreground="#dcddde"
                                        FontSize="10"
                                        FontWeight="Medium"
                                        Margin="4,0,8,0"
                                        d:Text="Mechs"
                                        VerticalAlignment="Center"
                                        HorizontalAlignment="Center"
                                        TextAlignment="Center"
                                        TextWrapping="NoWrap"
                                        Text="{Binding SubTitle, RelativeSource={RelativeSource AncestorType=local:BattlegroundsCardsGroup}}"
                                    />
                                </Viewbox>
                            </Border>
                        </DockPanel>
                        <hearthstone_deck_tracker:HearthstoneTextBlock
                            FontSize="14"
                            Margin="7,2"
                            Text="{Binding Title, RelativeSource={RelativeSource AncestorType=local:BattlegroundsCardsGroup}}"
                            d:Text="Tavern Tier 2"
                            Visibility="{Binding TitleVisibility, RelativeSource={RelativeSource AncestorType=local:BattlegroundsCardsGroup}}"
                            d:Visibility="Visible"
                        />
                    </DockPanel>
                </Border>
                <controls:AnimatedCardList x:Name="CardsList"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
