<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Minions.BattlegroundsMinionTypeButton"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Minions"
             mc:Ignorable="d"
             Cursor="Hand"
             MouseEnter="UserControl_MouseEnter"
             MouseLeave="UserControl_MouseLeave"
             MouseUp="UserControl_MouseUp">
    <Grid>
        <Ellipse Opacity="{Binding IconOpacity, RelativeSource={RelativeSource AncestorType=local:BattlegroundsMinionTypeButton}}">
            <Ellipse.Fill>
                <RadialGradientBrush>
                    <GradientStop Color="Transparent" Offset="0.0"/>
                    <GradientStop Color="Transparent" Offset="0.85"/>
                    <GradientStop Color="#4A5256" Offset="0.86"/>
                    <GradientStop Color="#4A5256" Offset="0.92"/>
                    <GradientStop Color="Transparent" Offset="0.93"/>
                    <GradientStop Color="Transparent" Offset="1"/>
                </RadialGradientBrush>
            </Ellipse.Fill>
        </Ellipse>
        <Ellipse
            Visibility="{Binding GlowVisibility, RelativeSource={RelativeSource AncestorType=local:BattlegroundsMinionTypeButton}}"
            Opacity="{Binding GlowOpacity, RelativeSource={RelativeSource AncestorType=local:BattlegroundsMinionTypeButton}}"
            d:Visibility="Visible">
            <Ellipse.Fill>
                <RadialGradientBrush>
                    <GradientStop Color="Transparent" Offset="0.0"/>
                    <GradientStop Color="Transparent" Offset="0.89"/>
                    <GradientStop Color="#f6f601" Offset="0.92"/>
                    <GradientStop Color="#cdcb08" Offset="0.95"/>
                    <GradientStop Color="#cdcb08" Offset="0.97"/>
                    <GradientStop Color="#f6f601" Offset="1"/>
                </RadialGradientBrush>
            </Ellipse.Fill>
            <Ellipse.Effect>
                <DropShadowEffect Color="#f6f601" BlurRadius="5" ShadowDepth="0"/>
            </Ellipse.Effect>
        </Ellipse>
        <local:BattlegroundsMinionType
            d:Tribe="Beast"
            Width="{Binding Width, RelativeSource={RelativeSource AncestorType=UserControl}}"
            Height="{Binding Height, RelativeSource={RelativeSource AncestorType=UserControl}}"
            Tribe="{Binding MinionType, RelativeSource={RelativeSource AncestorType=local:BattlegroundsMinionTypeButton}}"
            Opacity="{Binding IconOpacity, RelativeSource={RelativeSource AncestorType=local:BattlegroundsMinionTypeButton}}"
            RenderTransformOrigin="0.5,0.5">
            <local:BattlegroundsMinionType.RenderTransform>
                <ScaleTransform ScaleX="0.85" ScaleY="0.85"/>
            </local:BattlegroundsMinionType.RenderTransform>
        </local:BattlegroundsMinionType>
        <Image Margin="1,2,0,0" Visibility="{Binding RemoveIconVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}" Source="/HearthstoneDeckTracker;component/Resources/tier-x.png" RenderOptions.BitmapScalingMode="Fant" />
        <Border
            Height="12"
            Margin="0,0,0,-3"
            VerticalAlignment="Bottom"
            CornerRadius="1"
            Background="#23272a"
        />
        <Border
            Height="12"
            Margin="0,0,0,-3"
            VerticalAlignment="Bottom"
            BorderBrush="#4A5256"
            BorderThickness="1"
            CornerRadius="1"
            Background="#141617"
            Opacity="{Binding IconOpacity, RelativeSource={RelativeSource AncestorType=local:BattlegroundsMinionTypeButton}}"
        >
            <Viewbox Stretch="Uniform" StretchDirection="DownOnly" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="2,1">
                <TextBlock
                    Text="{Binding TribeName, RelativeSource={RelativeSource AncestorType=local:BattlegroundsMinionTypeButton}}"
                    d:Text="Beast"
                    Foreground="#dcddde"
                    FontSize="10"
                />
            </Viewbox>
        </Border>

    </Grid>
</UserControl>
