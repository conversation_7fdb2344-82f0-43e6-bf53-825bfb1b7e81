﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Inspiration.BattlegroundsInspirationOverlayButton"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings">
    <Border Width="49" Height="49" Margin="0,0,2,0" Cursor="Hand"
            RenderTransformOrigin="0.5,0.5" CornerRadius="0,0,3,3" VerticalAlignment="Top" Opacity="0"
            BorderBrush="#141617" BorderThickness="1,0,1,1">
        <Border.Style>
            <Style TargetType="Border">
                <Setter Property="Background" Value="#23272a"/>
                <Setter Property="Opacity" Value="0"/>
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <TranslateTransform Y="-55" />
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsMouseOver, RelativeSource={RelativeSource AncestorType=UserControl}}" Value="True">
                        <Setter Property="Background" Value="#343637"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsEnabled, RelativeSource={RelativeSource AncestorType=UserControl}}" Value="True">
                        <DataTrigger.EnterActions>
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation Storyboard.TargetProperty="Opacity" To="1" Duration="0:0:0:0.2" />
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.(TranslateTransform.Y)" To="0" Duration="0:0:0:0.2">
                                        <DoubleAnimation.EasingFunction>
                                            <CubicEase EasingMode="EaseOut"/>
                                        </DoubleAnimation.EasingFunction>
                                    </DoubleAnimation>
                                </Storyboard>
                            </BeginStoryboard>
                        </DataTrigger.EnterActions>
                        <DataTrigger.ExitActions>
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation Storyboard.TargetProperty="Opacity" To="0" Duration="0:0:0:0.3" BeginTime="0:0:0:0.1" />
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.(TranslateTransform.Y)" To="-55" Duration="0:0:0:0.3" BeginTime="0:0:0:0.1">
                                        <DoubleAnimation.EasingFunction>
                                            <CubicEase EasingMode="EaseOut"/>
                                        </DoubleAnimation.EasingFunction>
                                    </DoubleAnimation>
                                </Storyboard>
                            </BeginStoryboard>
                        </DataTrigger.ExitActions>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </Border.Style>
        <Rectangle Height="29" Width="29" Margin="10">
            <Rectangle.Fill>
                <VisualBrush Visual="{StaticResource icon_inspiration}" />
            </Rectangle.Fill>
        </Rectangle>
    </Border>
</UserControl>
