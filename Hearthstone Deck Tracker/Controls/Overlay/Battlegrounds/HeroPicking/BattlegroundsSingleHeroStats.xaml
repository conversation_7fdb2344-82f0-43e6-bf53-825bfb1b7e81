<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.HeroPicking.BattlegroundsSingleHeroStats"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             xmlns:heroPicking="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.HeroPicking"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d"
             d:DesignHeight="568" d:DesignWidth="266">
    <DockPanel Width="266">

        <Grid DockPanel.Dock="Top" >
            <heroPicking:BattlegroundsHeroHeader DataContext="{Binding BgsHeroHeaderVM}" VerticalAlignment="Top"/>
        </Grid>

        <!-- Hero Portrait Container -->
        <Grid Background="#00FF00FF" Margin="0 -237 0 0" Height="285" IsHitTestVisible="False">
        </Grid>
    </DockPanel>
</UserControl>
