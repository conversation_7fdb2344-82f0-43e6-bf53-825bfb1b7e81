<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.BattlegroundsOpponentInfo"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             xmlns:controls="clr-namespace:Hearthstone_Deck_Tracker.Controls"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d">
    <StackPanel Orientation="Vertical">
        <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
            <StackPanel Orientation="Vertical">
                <Border BorderBrush="#404345" CornerRadius="0 0 0 3" BorderThickness="1 0 1 1" Opacity=".9" Height="150">
                    <Border.Background>
                        <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                            <GradientStop Color="#202427" Offset="0" />
                            <GradientStop Color="#23272A" Offset="1" />
                        </LinearGradientBrush>
                    </Border.Background>
                    <StackPanel Orientation="Vertical" MinWidth="740">
                        <StackPanel Name="BattlegroundsBoard" Orientation="Horizontal" HorizontalAlignment="Center" Margin="10 0 20 0">
                            <StackPanel.Resources>
                                <Style TargetType="controls:BattlegroundsMinion">
                                    <Setter Property="Width" Value="110"/>
                                    <Setter Property="Height" Value="110"/>
                                    <Setter Property="Margin" Value="0,20,-10,20"/>
                                </Style>
                            </StackPanel.Resources>
                        </StackPanel>
                        <hearthstoneDeckTracker:HearthstoneTextBlock
                            x:Name="NotFoughtOpponent"
                            Text="{lex:Loc Overlay_Battlegrounds_Not_Fought_Opponent}"
                            Visibility="Collapsed" FontSize="16" Margin="50 63"
                            HorizontalAlignment="Center"
                        />
                        <hearthstoneDeckTracker:HearthstoneTextBlock
                            x:Name="HeroNoMinionsOnBoard"
                            Text="{lex:Loc Overlay_Battlegrounds_Hero_No_Minions_On_Board}"
                            Visibility="Collapsed" FontSize="16" Margin="50 63"
                            HorizontalAlignment="Center"
                        />
                    </StackPanel>
                </Border>
                <hearthstoneDeckTracker:HearthstoneTextBlock x:Name="BattlegroundsAge" FontSize="16" Margin="0, 5, 0, -5"/>
            </StackPanel>
            <Border BorderBrush="#404345" CornerRadius="0 0 3 0" BorderThickness="1 0 1 1" Opacity=".9" Height="150" VerticalAlignment="Top">
                <Border.Background>
                    <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                        <GradientStop Color="#202427" Offset="0" />
                        <GradientStop Color="#23272A" Offset="1" />
                    </LinearGradientBrush>
                </Border.Background>
                <StackPanel Name="TiersInfo" Orientation="Vertical" Margin="6 10 10 10">
                    <StackPanel Orientation="Vertical">
                        <StackPanel Orientation="Horizontal">
                            <local:BattlegroundsTierTriples
                                Tier="1"
                                Qty="{Binding TriplesTier1, FallbackValue=0, RelativeSource={RelativeSource AncestorType=local:BattlegroundsOpponentInfo}}"
                                Turn="1"
                                Margin="5 0 0 0"
                            />
                            <local:BattlegroundsTierTriples
                                Tier="2"
                                Qty="{Binding TriplesTier2, FallbackValue=0, RelativeSource={RelativeSource AncestorType=local:BattlegroundsOpponentInfo}}"
                                Turn="{Binding TurnTier2, FallbackValue=0, RelativeSource={RelativeSource AncestorType=local:BattlegroundsOpponentInfo}}"
                                Margin="5 0 0 0"
                            />
                            <local:BattlegroundsTierTriples
                                Tier="3"
                                Qty="{Binding TriplesTier3, FallbackValue=0, RelativeSource={RelativeSource AncestorType=local:BattlegroundsOpponentInfo}}"
                                Turn="{Binding TurnTier3, FallbackValue=0, RelativeSource={RelativeSource AncestorType=local:BattlegroundsOpponentInfo}}"
                                Margin="5 0 0 0"
                            />
                        </StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0 5 0 0">
                            <local:BattlegroundsTierTriples
                                Tier="4"
                                Qty="{Binding TriplesTier4, FallbackValue=0, RelativeSource={RelativeSource AncestorType=local:BattlegroundsOpponentInfo}}"
                                Turn="{Binding TurnTier4, FallbackValue=0, RelativeSource={RelativeSource AncestorType=local:BattlegroundsOpponentInfo}}"
                                Margin="5 0 0 0"
                            />
                            <local:BattlegroundsTierTriples
                                Tier="5"
                                Qty="{Binding TriplesTier5, FallbackValue=0, RelativeSource={RelativeSource AncestorType=local:BattlegroundsOpponentInfo}}"
                                Turn="{Binding TurnTier5, FallbackValue=0, RelativeSource={RelativeSource AncestorType=local:BattlegroundsOpponentInfo}}"
                                Margin="5 0 0 0"
                            />
                            <local:BattlegroundsTierTriples
                                Tier="6"
                                Qty="{Binding TriplesTier6, FallbackValue=0, RelativeSource={RelativeSource AncestorType=local:BattlegroundsOpponentInfo}}"
                                Turn="{Binding TurnTier6, FallbackValue=0, RelativeSource={RelativeSource AncestorType=local:BattlegroundsOpponentInfo}}"
                                Margin="5 0 0 0"
                            />
                        </StackPanel>
                    </StackPanel>
                </StackPanel>
            </Border>
        </StackPanel>
    </StackPanel>
</UserControl>

