﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Composition.BattlegroundsCompositionStatsBar"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Composition"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             Width="70" Height="22">
    <Border Background="#FF232526" CornerRadius="3" Width="70" Height="22">
        <Grid>
            <Border x:Name="progressBar" CornerRadius="3" HorizontalAlignment="Left">
                <Border.Background>
                    <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                        <GradientStop Color="#CCC58DC9" Offset="1" />
                        <GradientStop Color="#FFC58DC9" Offset="0" />
                    </LinearGradientBrush>
                </Border.Background>
            </Border>
            <hearthstoneDeckTracker:HearthstoneTextBlock
                x:Name="percentageText"
                Text="0%"
                FontSize="13"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
            />
        </Grid>
    </Border>
</UserControl>
