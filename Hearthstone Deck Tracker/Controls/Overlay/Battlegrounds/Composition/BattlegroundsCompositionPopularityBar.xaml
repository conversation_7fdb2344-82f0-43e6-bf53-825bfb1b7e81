﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Composition.BattlegroundsCompositionPopularityBar"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Composition"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
        <Rectangle Name="BarRect" Height="16" Width="0" Margin="0 4" RadiusX="2" RadiusY="2"
                   StrokeThickness="-1" Stroke="{Binding BorderColor, RelativeSource={RelativeSource AncestorType=local:BattlegroundsCompositionPopularityBar}, FallbackValue=#66FFFFFF}">
            <Rectangle.Fill>
                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                    <GradientStop Color="{Binding GradientColorTop, RelativeSource={RelativeSource AncestorType=local:BattlegroundsCompositionPopularityBar}, FallbackValue=#CCC58DC9}" Offset="1" />
                    <GradientStop Color="{Binding GradientColorBottom, RelativeSource={RelativeSource AncestorType=local:BattlegroundsCompositionPopularityBar}, FallbackValue=#FFC58DC9}" Offset="0" />
                </LinearGradientBrush>
            </Rectangle.Fill>
        </Rectangle>
    </StackPanel>
</UserControl>
