﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Composition.BattlegroundsCompositionPopularity"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.Composition"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             xmlns:extensions="clr-namespace:Hearthstone_Deck_Tracker.Utility.Extensions"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Border Background="#141617" BorderBrush="{StaticResource Tier7Purple}" BorderThickness="1" CornerRadius="5" 
            extensions:OverlayExtensions.IsOverlayHitTestVisible="True" 
            ToolTipService.InitialShowDelay="0" ToolTip="{lex:Loc BattlegroundsHeroPicking_Hero_CompositionTooltip}" >
        <ItemsControl ItemsSource="{Binding Top3Compositions}">
            <ItemsControl.OpacityMask>
                <VisualBrush>
                    <VisualBrush.Visual>
                        <Border Background="Black"
                                CornerRadius="{Binding CornerRadius, RelativeSource={RelativeSource AncestorType=Border}}"
                                Width="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Border}}"
                                Height="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Border}}"
                        />
                    </VisualBrush.Visual>
                </VisualBrush>
            </ItemsControl.OpacityMask>
            <ItemsControl.Style>
                <Style TargetType="{x:Type ItemsControl}">
                    <Style.Triggers>
                        <DataTrigger Value="{x:Null}" Binding="{Binding Top3Compositions}">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate>
                                        <TextBlock Foreground="White" FontSize="12" Text="{lex:Loc BattlegroundsHeroPicking_Compositions_NoData}"
                                                   Margin="0 8" TextAlignment="Center" Width="200" TextTrimming="CharacterEllipsis" />
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </ItemsControl.Style>
            <ItemsControl.ItemTemplate>
                <DataTemplate>
                    <local:BattlegroundsCompositionPopularityRow DataContext="{Binding}"/>
                </DataTemplate>
            </ItemsControl.ItemTemplate>
            <ItemsControl.ItemsPanel>
                <ItemsPanelTemplate>
                    <StackPanel />
                </ItemsPanelTemplate>
            </ItemsControl.ItemsPanel>
        </ItemsControl>
    </Border>
</UserControl>
