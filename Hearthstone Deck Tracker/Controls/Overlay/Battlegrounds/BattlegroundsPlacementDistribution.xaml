﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds.BattlegroundsPlacementDistribution"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d"
             d:DesignHeight="60" d:DesignWidth="200">
    <Border BorderBrush="{StaticResource Tier7Purple}" Background="{StaticResource Tier7Black}" BorderThickness="1" CornerRadius="3">
        <Grid>
            <!-- Has data -->
            <Grid Visibility="{Binding HasData, RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}, Converter={StaticResource BoolToVisibility}}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="2" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="16" />
                </Grid.RowDefinitions>

                <Border Background="#4A5256" BorderThickness="1 0 1 1" BorderBrush="{StaticResource Tier7Purple}" Grid.Row="1" Grid.ColumnSpan="10" Margin="-1 0 -1 -1" CornerRadius="0 0 5 5" />

                <local:BattlegroundsPlacementDistributionBar x:Name="Bar1st" Width="auto" Height="auto" Grid.Column="1" Grid.Row="0" Placement="1" Highlight="True"
                                                             MaxValue="{Binding MaxValue, RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"
                                                             Value="{Binding Values[0], RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}" />

                <Image Grid.Column="1" Grid.Row="1" Height="16" Width="16" Source="/HearthstoneDeckTracker;component/Resources/bgs_crown.png" Margin="0 1 0 0" RenderOptions.BitmapScalingMode="Fant">
                    <Image.RenderTransform>
                        <RotateTransform CenterX="8" CenterY="8" Angle="46" />
                    </Image.RenderTransform>
                </Image>

                <local:BattlegroundsPlacementDistributionBar x:Name="Bar2nd" Width="auto" Height="auto" Grid.Column="2" Grid.Row="0" Placement="2" Highlight="True"
                                                             MaxValue="{Binding MaxValue, RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"
                                                             Value="{Binding Values[1], RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"/>

                <local:BattlegroundsPlacementDistributionBar x:Name="Bar3rd" Width="auto" Height="auto" Grid.Column="3" Grid.Row="0" Placement="3" Highlight="True"
                                                             MaxValue="{Binding MaxValue, RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"
                                                             Value="{Binding Values[2], RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"/>

                <local:BattlegroundsPlacementDistributionBar x:Name="Bar4th" Width="auto" Height="auto" Grid.Column="4" Grid.Row="0" Placement="4" Highlight="True"
                                                             MaxValue="{Binding MaxValue, RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"
                                                             Value="{Binding Values[3], RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"/>

                <TextBlock Text="{Binding Localized4th, RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}" FontSize="10" Grid.Column="4" Grid.Row="1"
                           Foreground="White" VerticalAlignment="Center" HorizontalAlignment="Center" d:Text="4th" />

                <local:BattlegroundsPlacementDistributionBar x:Name="Bar5th" Width="auto" Height="auto" Grid.Column="5" Grid.Row="0" Placement="5" Highlight="False"
                                                             MaxValue="{Binding MaxValue, RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"
                                                             Value="{Binding Values[4], RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"/>

                <local:BattlegroundsPlacementDistributionBar x:Name="Bar6th" Width="auto" Height="auto" Grid.Column="6" Grid.Row="0" Placement="6" Highlight="False"
                                                             MaxValue="{Binding MaxValue, RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"
                                                             Value="{Binding Values[5], RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"/>

                <local:BattlegroundsPlacementDistributionBar x:Name="Bar7th" Width="auto" Height="auto" Grid.Column="7" Grid.Row="0" Placement="7" Highlight="False"
                                                             MaxValue="{Binding MaxValue, RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"
                                                             Value="{Binding Values[6], RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"/>

                <local:BattlegroundsPlacementDistributionBar x:Name="Bar8th" Width="auto" Height="auto" Grid.Column="8" Grid.Row="0" Placement="8" Highlight="False"
                                                             MaxValue="{Binding MaxValue, RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"
                                                             Value="{Binding Values[7], RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}"/>

                <TextBlock Text="{Binding Localized8th, RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}}" FontSize="10" Grid.Column="8" Grid.Row="1"
                           Foreground="White" VerticalAlignment="Center" HorizontalAlignment="Center" d:Text="8th" />
            </Grid>

            <!-- No data -->
            <TextBlock Foreground="White" FontSize="12" Text="{lex:Loc BattlegroundsHeroPicking_PlacementDistribution_NoData}" TextAlignment="Center" VerticalAlignment="Center" MaxWidth="200" TextWrapping="Wrap" TextTrimming="CharacterEllipsis"
                       Visibility="{Binding HasData, RelativeSource={RelativeSource AncestorType=local:BattlegroundsPlacementDistribution}, Converter={StaticResource InverseBoolToVisibility}}"/>
        </Grid>
    </Border>
</UserControl>
