﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Overlay.MulliganPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:utility="clr-namespace:Hearthstone_Deck_Tracker.Utility"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             DataContext="{Binding RelativeSource={RelativeSource Self}}"
             Background="#1D3657" Cursor="{Binding CursorStyle}"
             MouseEnter="UserControl_MouseEnter" MouseLeave="UserControl_MouseLeave"
             MouseLeftButtonUp="UserControl_MouseLeftButtonUp"
             Margin="5" MinHeight="60"
             mc:Ignorable="d">
    <UserControl.Resources>
        <Storyboard x:Key="StoryboardNormal">
            <DoubleAnimation To="0.5" Duration="0:0:0.2" Storyboard.TargetName="BackgroundImage" Storyboard.TargetProperty="Opacity" />
        </Storyboard>
        <Storyboard x:Key="StoryboardHover">
            <DoubleAnimation To="0.8" Duration="0:0:0.2" Storyboard.TargetName="BackgroundImage" Storyboard.TargetProperty="Opacity" />
        </Storyboard>
    </UserControl.Resources>
    <Border BorderThickness="1" BorderBrush="White">
        <Grid>
            <utility:IgnoreSizeDecorator>
                <Image Name="BackgroundImage" Opacity="0.5" Source="{StaticResource MulliganToastBackground}" Stretch="UniformToFill" RenderOptions.BitmapScalingMode="Fant"
                       Visibility="{Binding HasData, Converter={StaticResource BoolToVisibility}}"/>
            </utility:IgnoreSizeDecorator>
            <utility:IgnoreSizeDecorator>
                <Image Source="{StaticResource MulliganToastBackgroundGrey}" Stretch="UniformToFill" RenderOptions.BitmapScalingMode="Fant"
                       Visibility="{Binding HasData, Converter={StaticResource InverseBoolToVisibility}}"/>
            </utility:IgnoreSizeDecorator>
            <DockPanel Margin="10" VerticalAlignment="Center">
                <Image Source="{StaticResource HsReplayIconWhite}" Height="28" Width="28"
                       HorizontalAlignment="Left" RenderOptions.BitmapScalingMode="Fant"/>
                <Grid>
                    <TextBlock Foreground="White" TextAlignment="Center" Margin="20,0"
                               LineHeight="15" LineStackingStrategy="BlockLineHeight"
                               Visibility="{Binding HasData, Converter={StaticResource BoolToVisibility}}">
                        <Run Text="{lex:Loc Toast_Mulligan_Available}" FontWeight="SemiBold" FontSize="17"/>
                        <LineBreak/>
                        <Run Text="{lex:Loc Toast_Mulligan_HSReplaynet}" FontSize="12"/>
                    </TextBlock>
                    <TextBlock Foreground="White" TextAlignment="Center" Margin="20,0"
                               LineHeight="15" LineStackingStrategy="BlockLineHeight"
                               Visibility="{Binding HasData, Converter={StaticResource InverseBoolToVisibility}}">
                        <Run Text="{Binding NoDataLabel, Mode=OneWay}" FontWeight="SemiBold" FontSize="17" />
                        <LineBreak/>
                        <Run Text="{lex:Loc Toast_Mulligan_HSReplaynet}" FontSize="12"/>
                    </TextBlock>
                </Grid>
            </DockPanel>
        </Grid>
    </Border>
</UserControl>
