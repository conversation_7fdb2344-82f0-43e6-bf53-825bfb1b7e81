﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Tooltips.CardTooltip"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:controls="clr-namespace:Hearthstone_Deck_Tracker.Controls"
             xmlns:hdt="clr-namespace:Hearthstone_Deck_Tracker"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             mc:Ignorable="d"
             DataContextChanged="CardTooltip_OnDataContextChanged"
             d:DesignHeight="256" d:DesignWidth="256">
    <UserControl.Resources>
        <controls:CardImageSizeConverter x:Key="CardImageSizeConverter"/>
        <Style TargetType="Image">
            <Setter Property="RenderOptions.BitmapScalingMode" Value="Fant"/>
        </Style>
        <Storyboard x:Key="StoryboardShow">
            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.(ScaleTransform.ScaleX)" To="1" Duration="0:0:0.2" />
            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.(ScaleTransform.ScaleY)" To="1" Duration="0:0:0.2" />
        </Storyboard>
        <Storyboard x:Key="StoryboardShowDelayed">
            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.(ScaleTransform.ScaleX)" To="1" Duration="0:0:0.2" BeginTime="0:0:0.8" />
            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.(ScaleTransform.ScaleY)" To="1" Duration="0:0:0.2" BeginTime="0:0:0.8" />
        </Storyboard>
        <Storyboard x:Key="StoryboardHide">
            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.(ScaleTransform.ScaleX)" To="0" Duration="0:0:0" />
            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.(ScaleTransform.ScaleY)" To="0" Duration="0:0:0" />
        </Storyboard>
        <Storyboard x:Key="StoryboardFadeInDelayed">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" To="1" Duration="0:0:0.2" BeginTime="0:0:0.3" />
        </Storyboard>
        <Storyboard x:Key="StoryboardFadeOutInstant">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" To="0" Duration="0:0:0" />
        </Storyboard>
    </UserControl.Resources>
    <DockPanel DataContext="{Binding ViewModel, RelativeSource={RelativeSource AncestorType=UserControl}}">

        <!-- Primary Image -->
        <Grid Name="PrimaryImage" DockPanel.Dock="{Binding ImageDock}" Visibility="{Binding AssetViewModel, Converter={StaticResource NullableToVisibility}, ConverterParameter={x:Static Visibility.Hidden}}" RenderTransformOrigin="0.5,0.5" VerticalAlignment="Center"
              Margin="{Binding CardImageOffset}"
              MaxWidth="{Binding Path=Bindable.CardImageSize, Source={StaticResource ConfigWrapper}, Converter={StaticResource CardImageSizeConverter}, ConverterParameter={x:Static controls:CardImageSizeType.Width}}"
              MaxHeight="{Binding Path=Bindable.CardImageSize, Source={StaticResource ConfigWrapper}, Converter={StaticResource CardImageSizeConverter}, ConverterParameter={x:Static controls:CardImageSizeType.Height}}">
            <Grid.RenderTransform>
                <ScaleTransform ScaleX="0" ScaleY="0"/>
            </Grid.RenderTransform>
            <Grid.Style>
                <Style TargetType="Grid">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True">
                            <DataTrigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource StoryboardShow}"/>
                            </DataTrigger.EnterActions>
                            <DataTrigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource StoryboardHide}"/>
                            </DataTrigger.ExitActions>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Grid.Style>
            <hdt:HearthstoneTextBlock Text="{Binding Text}" Visibility="{Binding Text, Converter={StaticResource NullableToVisibility}}" HorizontalAlignment="Center" VerticalAlignment="Top" d:Text="Created By" FontSize="16"/>
            <Image Source="{Binding AssetViewModel.Asset}" HorizontalAlignment="Center" />
        </Grid>

        <!-- Secondary Image, e.g. Battlegrounds Triple -->
        <Image DockPanel.Dock="{Binding ImageDock}" Source="{Binding SecondaryAssetViewModel.Asset}" Visibility="{Binding SecondaryAssetViewModel, Converter={StaticResource NullableToVisibility}}" RenderTransformOrigin="0.5,0.5" VerticalAlignment="Center"
              Margin="{Binding CardImageOffset}"
              MaxWidth="{Binding Path=Bindable.CardImageSize, Source={StaticResource ConfigWrapper}, Converter={StaticResource CardImageSizeConverter}, ConverterParameter={x:Static controls:CardImageSizeType.Width}}"
              MaxHeight="{Binding Path=Bindable.CardImageSize, Source={StaticResource ConfigWrapper}, Converter={StaticResource CardImageSizeConverter}, ConverterParameter={x:Static controls:CardImageSizeType.Height}}">
            <Image.RenderTransform>
                <ScaleTransform ScaleX="0" ScaleY="0"/>
            </Image.RenderTransform>
            <Image.Style>
                <Style TargetType="Image">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True">
                            <DataTrigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource StoryboardShowDelayed}"/>
                            </DataTrigger.EnterActions>
                            <DataTrigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource StoryboardHide}"/>
                            </DataTrigger.ExitActions>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Image.Style>
        </Image>

        <controls:GridCardImages DockPanel.Dock="{Binding ImageDock}" Cards="{Binding RelatedCards}" Title="{lex:Loc Related_Cards}" Visibility="{Binding RelatedCards, Converter={StaticResource NullableToVisibility}}" VerticalAlignment="Center">
            <controls:GridCardImages.LayoutTransform>
                <ScaleTransform
                    ScaleX="{Binding Path=Bindable.CardImageSize, Source={StaticResource ConfigWrapper}}"
                    ScaleY="{Binding Path=Bindable.CardImageSize, Source={StaticResource ConfigWrapper}}" />
            </controls:GridCardImages.LayoutTransform>
            <controls:GridCardImages.Style>
                <Style TargetType="controls:GridCardImages">
                    <Setter Property="Opacity" Value="0"/>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True">
                            <DataTrigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource StoryboardFadeInDelayed}"/>
                            </DataTrigger.EnterActions>
                            <DataTrigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource StoryboardFadeOutInstant}"/>
                            </DataTrigger.ExitActions>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </controls:GridCardImages.Style>
        </controls:GridCardImages>
    </DockPanel>
</UserControl>
