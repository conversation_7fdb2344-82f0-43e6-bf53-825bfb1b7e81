<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.DeckPicker.DeckPickerItemLayouts.DeckPickerItemLayoutLegacy"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:utility="clr-namespace:Hearthstone_Deck_Tracker.Utility"
             xmlns:deckPicker="clr-namespace:Hearthstone_Deck_Tracker.Controls.DeckPicker"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance deckPicker:DeckPickerItemViewModel}"
             d:DesignHeight="300" d:DesignWidth="300" Height="52">
    <Border Name="BorderItem" BorderThickness="1"  BorderBrush="{DynamicResource {x:Static SystemColors.ControlDarkDarkBrushKey}}">
         <DockPanel>
            <DockPanel.Background>
                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                    <GradientStop Color="{Binding Deck.ClassColor}" Offset="-0.5" />
                    <GradientStop Color="#FFE5E5E5" Offset="1.5" />
                </LinearGradientBrush>
            </DockPanel.Background>
            <Grid Width="46" Height="46" Margin="5,1,10,0">
                <Image DockPanel.Dock="Left" Width="46" Height="46" Source="{Binding Deck.ClassImage}" RenderOptions.BitmapScalingMode="Fant" />
                <Rectangle Width="18" Height="14" Visibility="{Binding Deck.WildIndicatorVisibility}" Margin="0,2,0,0" HorizontalAlignment="Right" VerticalAlignment="Top"
                           ToolTip="{Binding WildIndicatorTooltip}">
                    <Rectangle.Fill>
                        <VisualBrush Visual="{StaticResource wild_icon}" />
                    </Rectangle.Fill>
                </Rectangle>
            </Grid>
            <Border BorderThickness="1"  BorderBrush="{DynamicResource {x:Static SystemColors.ControlDarkDarkBrushKey}}" Width="48" DockPanel.Dock="Right" Margin="-1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="1.618*"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Text="{Binding Deck.WinPercentString}" Visibility="{Binding Deck.VisibilityStats}" VerticalAlignment="Center" HorizontalAlignment="Center"  FontSize="12" FontWeight="Medium" Foreground="Black"/>
                    <Border BorderThickness="1"  Visibility="{Binding Deck.VisibilityStats}" BorderBrush="{DynamicResource {x:Static SystemColors.ControlDarkDarkBrushKey}}" Width="48" Margin="-1" Grid.Row="1" >
                        <Viewbox Stretch="Uniform" StretchDirection="DownOnly" Margin="2,0">
                            <TextBlock Text="{Binding Deck.WinLossString}" VerticalAlignment="Center" HorizontalAlignment="Center"  FontSize="12" FontWeight="Medium" Foreground="Black"/>
                        </Viewbox>
                    </Border>
                    <TextBlock Grid.Row="0" Grid.RowSpan="2" VerticalAlignment="Center" TextAlignment="Center" Foreground="Black" FontSize="12" Visibility="{Binding Deck.VisibilityNoStats}">
                        <Run Text="{Binding LegacyNoStatsNo, Mode=OneWay}"/>
                        <LineBreak/>
                        <Run Text="{Binding LegacyNoStatsStats, Mode=OneWay}"/>
                    </TextBlock>
                </Grid>
            </Border>
            <StackPanel Margin="0,5">
                <DockPanel>
                    <Image DockPanel.Dock="Left" Source="{x:Static utility:ImageCache.ArchivedBlack}" Width="16"
                           Height="16" Margin="0,0,5,0" VerticalAlignment="Center" HorizontalAlignment="Left"
                           Visibility="{Binding Deck.ArchivedVisibility}"
                           ToolTip="{Binding ArchivedTooltip}" />
                    <DockPanel>
                        <Image Source="{DynamicResource HsReplayIcon}"
                               Visibility="{Binding HsReplayDataIndicatorVisibility}"
                               ToolTip="{Binding DataIndicatorTooltip}"
                               Width="14" RenderOptions.BitmapScalingMode="Fant"
                               DockPanel.Dock="Left" Margin="0,0,4,0"/>
                        <TextBlock Text="{Binding Deck.NameAndVersion}" Margin="0,0,15,0" DockPanel.Dock="Left"
                                   FontSize="17" TextTrimming="CharacterEllipsis"
                                   Foreground="Black"
                                   FontWeight="{Binding FontWeightSelected}" />
                        <Rectangle Width="10" Height="13" Visibility="{Binding Deck.NoteVisibility}" Margin="-10,0,0,0"
                                   ToolTip="{Binding Deck.Note}">
                            <Rectangle.Fill>
                                <VisualBrush Visual="{StaticResource appbar_page_bold_black}" />
                            </Rectangle.Fill>
                        </Rectangle>
                        <Grid DockPanel.Dock="Right" />
                    </DockPanel>
                </DockPanel>
                <DockPanel Margin="0,-2,0,0" Height="19">
                    <Border DockPanel.Dock="Left"
                    PreviewMouseDown="UseButton_OnPreviewMouseDown" PreviewMouseUp="UseButton_OnPreviewMouseUp"
                            Background="#01000000" Visibility="{Binding Source={ x:Static utility:ConfigWrapper.UseButtonVisiblity}}"
                            Width="50">
                        <Border.Style>
                            <Style>
                                <Style.Triggers>
                                    <Trigger Property="Border.IsMouseOver" Value="True">
                                        <Setter Property="TextBlock.FontWeight" Value="Bold" />
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Border.Style>
                        <TextBlock
                            Text="{Binding TextUseButton}"
                            FontSize="13" HorizontalAlignment="Left" VerticalAlignment="Center"
                            Foreground="{DynamicResource GrayBrush2}" />
                    </Border>
                    <Viewbox Margin="10,0,10,0" StretchDirection="DownOnly" Stretch="Fill" DockPanel.Dock="Left">
                        <ItemsControl ItemsSource="{Binding Deck.Tags}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <DockPanel />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border DockPanel.Dock="Right" BorderBrush="{DynamicResource GrayBrush2}"
                                            BorderThickness="1" Margin="2,0,0,0">
                                        <TextBlock Text="{Binding}" FontSize="10"
                                                   Foreground="{DynamicResource GrayBrush2}" VerticalAlignment="Center"
                                                   TextAlignment="Center" TextTrimming="CharacterEllipsis" Margin="4,0" />
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Viewbox>
                    <Grid />
                </DockPanel>
            </StackPanel>
        </DockPanel>
    </Border>

</UserControl>
