<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.DeckPicker.DeckPickerItemLayouts.DeckPickerItemLayout2"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:utility="clr-namespace:Hearthstone_Deck_Tracker.Utility"
             xmlns:deckPicker="clr-namespace:Hearthstone_Deck_Tracker.Controls.DeckPicker"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance deckPicker:DeckPickerItemViewModel}"
             d:DesignHeight="300" d:DesignWidth="300" Height="52">
    <Border BorderBrush="{DynamicResource MenuItemDisabledBrush}" BorderThickness="0,0,0,1">
        <DockPanel>
            <Grid Width="46" Height="46" Margin="5,1,10,0">
                <Image DockPanel.Dock="Left" Width="46" Height="46" Source="{Binding Deck.ClassImage}" RenderOptions.BitmapScalingMode="Fant" />
                <Rectangle Width="18" Height="14" Visibility="{Binding Deck.WildIndicatorVisibility}" Margin="0,2,0,0" HorizontalAlignment="Right" VerticalAlignment="Top"
                           ToolTip="{Binding WildIndicatorTooltip}">
                    <Rectangle.Fill>
                        <VisualBrush Visual="{StaticResource wild_icon}" />
                    </Rectangle.Fill>
                </Rectangle>
            </Grid>
            <Viewbox DockPanel.Dock="Right" MinWidth="70" StretchDirection="DownOnly" Stretch="Fill"
                     HorizontalAlignment="Right" Margin="0,0,2,0">
                <StackPanel>
                    <StackPanel.Resources>
                        <deckPicker:DateConverter x:Key="DateConverter"></deckPicker:DateConverter>
                    </StackPanel.Resources>
                    <TextBlock Text="{Binding Deck.StatsString}" HorizontalAlignment="Left"
                           Foreground="{DynamicResource GrayTextColorBrush}" FontSize="14" />
                    <TextBlock Text="{Binding DateShownOnDeck, Converter={StaticResource DateConverter}}" HorizontalAlignment="Right"
                           Foreground="{DynamicResource GrayTextColorBrush}" FontSize="11" Margin="0, 3, 0, 0"
                           Visibility="{Binding Source={x:Static utility:ConfigWrapper.ShowDateOnDeckVisibility}}"
                               ToolTip="{Binding DateShownOnDeckTooltip}" />
                </StackPanel>
            </Viewbox>
            <StackPanel Margin="0,5">
                <DockPanel>
                    <Image DockPanel.Dock="Left" Source="{x:Static utility:ImageCache.Archived}" Width="16"
                           Height="16" Margin="0,0,5,0" VerticalAlignment="Center" HorizontalAlignment="Left"
                           Visibility="{Binding Deck.ArchivedVisibility}"
                           ToolTip="{Binding ArchivedTooltip}" />
                    <DockPanel>
                        <Image Source="{DynamicResource HsReplayIcon}"
                               Visibility="{Binding HsReplayDataIndicatorVisibility}"
                               ToolTip="{Binding DataIndicatorTooltip}"
                               Width="14" RenderOptions.BitmapScalingMode="Fant"
                               DockPanel.Dock="Left" Margin="0,0,4,0"/>
                        <TextBlock Text="{Binding Deck.NameAndVersion}" Margin="0,0,15,0" DockPanel.Dock="Left"
                                   FontSize="17" TextTrimming="CharacterEllipsis"
                                   Foreground="{DynamicResource TextBrush}"
                                   FontWeight="{Binding FontWeightActiveDeck}" />
                        <Rectangle Width="10" Height="13" Visibility="{Binding Deck.NoteVisibility}" Margin="-10,0,0,0"
                                   ToolTip="{Binding Deck.Note}">
                            <Rectangle.Fill>
                                <VisualBrush Visual="{StaticResource appbar_page_bold}" />
                            </Rectangle.Fill>
                        </Rectangle>
                        <Grid DockPanel.Dock="Right" />
                    </DockPanel>
                </DockPanel>
                <DockPanel Margin="0,-2,0,0" Height="19">
                    <Border DockPanel.Dock="Left" PreviewMouseDown="UseButton_OnPreviewMouseDown" PreviewMouseUp="UseButton_OnPreviewMouseUp"
                            Background="#01000000" Visibility="{Binding Source={ x:Static utility:ConfigWrapper.UseButtonVisiblity}}"
                            Width="50">
                        <Border.Style>
                            <Style>
                                <Style.Triggers>
                                    <Trigger Property="Border.IsMouseOver" Value="True">
                                        <Setter Property="TextBlock.FontWeight" Value="Bold" />
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Border.Style>
                        <TextBlock
                            Text="{Binding TextUseButton}"
                            FontSize="13" HorizontalAlignment="Left" VerticalAlignment="Center"
                            Foreground="{DynamicResource GrayTextColorBrush}" />
                    </Border>
                    <Viewbox Margin="10,0,10,0" StretchDirection="DownOnly" Stretch="Fill" DockPanel.Dock="Left">
                        <ItemsControl ItemsSource="{Binding Deck.Tags}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <DockPanel />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border DockPanel.Dock="Right" BorderBrush="{DynamicResource GrayTextColorBrush}"
                                            BorderThickness="1" Margin="2,0,0,0">
                                        <TextBlock Text="{Binding}" FontSize="10"
                                                   Foreground="{DynamicResource GrayTextColorBrush}" VerticalAlignment="Center"
                                                   TextAlignment="Center" TextTrimming="CharacterEllipsis" Margin="4,0" />
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Viewbox>
                    <Grid />
                </DockPanel>
            </StackPanel>
        </DockPanel>
    </Border>

</UserControl>
