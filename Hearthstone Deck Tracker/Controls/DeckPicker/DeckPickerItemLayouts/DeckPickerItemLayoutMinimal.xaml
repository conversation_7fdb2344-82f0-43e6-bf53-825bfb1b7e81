﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.DeckPicker.DeckPickerItemLayouts.DeckPickerItemLayoutMinimal"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:deckPicker="clr-namespace:Hearthstone_Deck_Tracker.Controls.DeckPicker"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance deckPicker:DeckPickerItemViewModel}"
             d:DesignHeight="300" d:DesignWidth="300" Height="52">
    <Border BorderBrush="{DynamicResource MenuItemDisabledBrush}" BorderThickness="0,0,0,1">
        <DockPanel>
            <Image DockPanel.Dock="Left" Width="46" Height="46" Source="{Binding Deck.ClassImage}"
                   RenderOptions.BitmapScalingMode="Fant" />
            <Grid Margin="0,0,5,0">
                <DockPanel>
                    <DockPanel.Resources>
                        <deckPicker:MarginConverterNegativeLeft x:Key="MarginConverterLeft" />
                        <deckPicker:MarginConverterRight x:Key="MarginConverterRight" />
                    </DockPanel.Resources>
                    <TextBlock Name="TextBlockName" Foreground="{DynamicResource TextBrush}"
                               Text="{Binding Deck.NameAndVersion}"
                               Margin="{Binding ElementName=TextBlockStats, Path=ActualWidth, Converter={StaticResource MarginConverterRight}}"
                               FontSize="17" VerticalAlignment="Center" TextTrimming="CharacterEllipsis"
                               DockPanel.Dock="Left" />
                    <TextBlock Name="TextBlockStats" VerticalAlignment="Center"
                               Foreground="{DynamicResource GrayTextColorBrush}"
                               Text="{Binding Deck.StatsString}"
                               Margin="{Binding ElementName=TextBlockStats, Path=ActualWidth, Converter={StaticResource MarginConverterLeft}}" />
                    <Grid />
                </DockPanel>
            </Grid>
        </DockPanel>

    </Border>

</UserControl>
