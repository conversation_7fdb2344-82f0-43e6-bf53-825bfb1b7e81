#region

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Forms;
using System.Windows.Input;
using System.Windows.Media;
using Hearthstone_Deck_Tracker.Annotations;
using Hearthstone_Deck_Tracker.Controls.DeckPicker.DeckPickerItemLayouts;
using Hearthstone_Deck_Tracker.Enums;
using Hearthstone_Deck_Tracker.Hearthstone;
using Hearthstone_Deck_Tracker.Utility;
using Hearthstone_Deck_Tracker.Utility.Extensions;
using Hearthstone_Deck_Tracker.Utility.Logging;
using static System.ComponentModel.ListSortDirection;
using static System.Windows.Visibility;
using KeyEventArgs = System.Windows.Input.KeyEventArgs;
using ListView = System.Windows.Controls.ListView;
using Hearthstone_Deck_Tracker.Windows;
using MahApps.Metro;
using DeckType = Hearthstone_Deck_Tracker.Enums.DeckType;

#endregion

namespace Hearthstone_Deck_Tracker.Controls.DeckPicker
{
	/// <summary>
	/// Interaction logic for DeckPicker.xaml
	/// </summary>
	public partial class DeckPicker : INotifyPropertyChanged
	{
		private const string LocLink = "DeckPicker_ContextMenu_LinkUrl";
		private const string LocLinkNew = "DeckPicker_ContextMenu_LinkNewUrl";

		public delegate void SelectedDeckHandler(DeckPicker sender, List<Deck> deck);

		private readonly DeckPickerClassItem _archivedClassItem;
		private readonly Dictionary<Deck, DeckPickerItemViewModel> _cachedDeckPickerItems = new();
		private readonly ObservableCollection<DeckPickerClassItem> _classItems;
		private ObservableCollection<DeckType>? _deckTypeItems;
		private bool _ignoreSelectionChange;
		private DateTime _lastActiveDeckPanelClick = DateTime.MinValue;
		private bool _reselectingClasses;
		public bool ChangedSelection;
		private bool _searchBarVisibile;
		private bool _archivedClassVisible;

		public DeckPicker()
		{
			InitializeComponent();
			_classItems =
				new ObservableCollection<DeckPickerClassItem>(
					Enum.GetValues(typeof(HeroClassAll)).OfType<HeroClassAll>().Select(x => new DeckPickerClassItem {DataContext = x}));
			_archivedClassItem = _classItems.ElementAt((int)HeroClassAll.Archived);
			_classItems.Remove(_archivedClassItem);
			ListViewClasses.ItemsSource = _classItems;
			DeckList.Instance.ActiveDeckChanged += deck =>
			{
				if(deck != null)
					SelectDeckAndAppropriateView(deck, true);
				else
					ListViewDecks.SelectedItem = null;

				OnPropertyChanged(nameof(ActiveDeck));
				OnPropertyChanged(nameof(VisibilityNoDeck));
				UpdateDeckModeToggleButton();
				RefreshDisplayedDecks();
			};

			DeckList.Instance.Decks.CollectionChanged += DeckListDecksOnCollectionChanged;

			Deck.ArchivedChanged += deck =>
			{
				SelectDeckAndAppropriateView(deck);
				UpdateArchivedClassVisibility();
			};

			ThemeManager.IsThemeChanged += (_, _) =>
			{
				UpdateDeckModeToggleButton();
			};
		}

		private async void DeckListDecksOnCollectionChanged(object sender, object args)
		{
			if(await Debounce.WasCalledAgain(100))
				return;
			UpdateDecks();
			UpdateArchivedClassVisibility();
		}


		public ObservableCollection<DeckPickerItemViewModel> DisplayedDecks { get; } = new();

		public List<Deck> SelectedDecks => ListViewDecks.SelectedItems.Cast<DeckPickerItemViewModel>().Select(x => x.Deck).ToList();

		public ObservableCollection<HeroClassAll> SelectedClasses { get; } = new();

		public void ReloadUI()
		{
			_deckTypeItems = null;
			OnPropertyChanged(nameof(DeckTypeItems));
			RefreshDisplayedDecks();
		}

		public bool ArchivedClassVisible
		{
			get => _archivedClassVisible;
			set
			{
				_archivedClassVisible = value;
				OnPropertyChanged();
			}
		}

		public bool SearchBarVisibile
		{
			get => _searchBarVisibile;
			set
			{
				_searchBarVisibile = value;
				OnPropertyChanged(nameof(VisibilitySearchBar));
				OnPropertyChanged(nameof(VisibilitySearchIcon));
			}
		}

		public string? DeckNameFilter { get; set; }

		public Visibility VisibilitySearchIcon => SearchBarVisibile ? Collapsed : Visible;

		public Visibility VisibilitySearchBar => SearchBarVisibile ? Visible : Collapsed;

		public ObservableCollection<DeckType> DeckTypeItems => _deckTypeItems ??= new ObservableCollection<DeckType>(new() {
			DeckType.All,
			DeckType.Standard,
			DeckType.Wild,
			DeckType.Twist,
			DeckType.Arena,
			DeckType.Dungeon,
			DeckType.Brawl,
		});

		public Deck? ActiveDeck => DeckList.Instance.ActiveDeck;

		public Visibility VisibilityNoDeck => DeckList.Instance.ActiveDeck == null ? Visible : Collapsed;

		public event PropertyChangedEventHandler? PropertyChanged;

		[NotifyPropertyChangedInvocator]
		internal virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
		{
			PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
		}

		public event SelectedDeckHandler? OnSelectedDeckChanged;

		private void Selector_OnSelectionChanged(object sender, SelectionChangedEventArgs e)
		{
			if(_reselectingClasses)
				return;

			var removedPickerClassItems = e.RemovedItems.OfType<DeckPickerClassItem>();
			var addedPickerClassItems = e.AddedItems.OfType<DeckPickerClassItem>().ToList();
			var addedClasses = PickerClassItemsAsEnum(addedPickerClassItems).ToList();
			if(addedClasses.Contains(HeroClassAll.All))
			{
				_reselectingClasses = true;
				var senderList = ((ListView)sender);
				senderList.UnselectAll();
				foreach(var item in senderList.Items)
				{
					if(item is DeckPickerClassItem dpci)
					{
						var hca = (HeroClassAll)dpci.DataContext;

						switch(hca)
						{
							case HeroClassAll.All:
								senderList.SelectedItems.Add(item);
								SelectPickerClassItem(dpci);
								break;
							case HeroClassAll.Archived:
								if(!SelectedClasses.Contains(HeroClassAll.Archived))
								{
									if(addedClasses.Contains(HeroClassAll.Archived))
									{
										senderList.SelectedItems.Add(item);
										SelectPickerClassItem(dpci);
									}
								}
								else
								{
									if(PickerClassItemsAsEnum(removedPickerClassItems).Contains(HeroClassAll.Archived))
										DeselectPickerClassItem(dpci);
									else
										senderList.SelectedItems.Add(item);
								}
								break;
							default:
								DeselectPickerClassItem(dpci);
								break;
						}
					}
				}
				_reselectingClasses = false;
			}
			else
			{
				DeckPickerClassItem? removedAllClassItem = null;
				foreach(var dpci in removedPickerClassItems)
				{
					var heroClass = dpci.DataContext as HeroClassAll?;
					switch(heroClass)
					{
						case null:
							continue;
						case HeroClassAll.All:
							// We remove this from SelectedClasses now but we don't raise it's OnDeselected event yet,
							// instead store a reference to it in case we want to quietly add this back to the
							// SelectedClasses list later
							SelectedClasses.Remove(heroClass.Value);
							removedAllClassItem = dpci;
							break;
						default:
							DeselectPickerClassItem(dpci);
							break;
					}
				}

				var allIsSelected = SelectedClasses.Contains(HeroClassAll.All);
				foreach(var dpci in addedPickerClassItems)
				{
					var heroClass = dpci.DataContext as HeroClassAll?;
					if(heroClass == null)
						continue;

					if(allIsSelected && heroClass != HeroClassAll.Archived)
					{
						_reselectingClasses = true;
						((ListView)sender).SelectedItems.Remove(dpci);
						_reselectingClasses = false;
						continue;
					}

					SelectPickerClassItem(dpci);
				}

				if(SelectedClasses.Count == 0)
				{
					var senderList = (ListView)sender;
					if(removedAllClassItem == null)
					{
						var dpciAll = PickerClassItemFromEnum(senderList, HeroClassAll.All);

						// Select 'All', raising its OnSelected event
						_reselectingClasses = true;
						senderList.SelectedItems.Add(dpciAll);
						SelectPickerClassItem(dpciAll);
						_reselectingClasses = false;
					}
					else
					{
						// If there are no selected classes, and we earlier removed 'All', quietly add it back
						_reselectingClasses = true;
						senderList.SelectedItems.Add(removedAllClassItem);
						SelectedClasses.Add(HeroClassAll.All);
						_reselectingClasses = false;

						// And make sure we do not raise its OnDeselected event if we were going to
						removedAllClassItem = null;
					}
				}

				// If we removed the 'All' class earlier, raise the DeckPickerClassItem's OnDeselected event now
				removedAllClassItem?.OnDelselected();
			}

			UpdateDecks();
		}

		private void SelectPickerClassItem(DeckPickerClassItem dpci)
		{
#pragma warning disable IDE0019
			var heroClass = dpci.DataContext as HeroClassAll?;
			if(heroClass != null && !SelectedClasses.Contains(heroClass.Value))
			{
				SelectedClasses.Add(heroClass.Value);
				dpci.OnSelected();
			}
#pragma warning restore IDE0019
		}

		private void DeselectPickerClassItem(DeckPickerClassItem dpci)
		{
#pragma warning disable IDE0019
			var heroClass = dpci.DataContext as HeroClassAll?;
			if(heroClass != null && SelectedClasses.Remove(heroClass.Value))
				dpci.OnDelselected();
#pragma warning restore IDE0019
		}

		private static IEnumerable<HeroClassAll?> PickerClassItemsAsEnum(IEnumerable<DeckPickerClassItem> items)
		{
			return items.Select(x => x.DataContext as HeroClassAll?).Where(x => x != null);
		}

		private static DeckPickerClassItem PickerClassItemFromEnum(ListView sender, HeroClassAll heroClass)
		{
			var items = sender.Items.OfType<DeckPickerClassItem>().Where(x => (x.DataContext as HeroClassAll?).HasValue);
			return items.FirstOrDefault(x => (x.DataContext as HeroClassAll?) == heroClass);
		}

		public void SelectClass(HeroClassAll heroClass)
		{
			if(!SelectedClasses.Contains(heroClass))
			{
				var dpci = PickerClassItemFromEnum(ListViewClasses, heroClass);
				ListViewClasses.SelectedItem = dpci;
			}
		}

		public void UpdateDecks(bool reselectActiveDeck = true, IEnumerable<Deck>? forceUpdate = null)
		{
			var selectedDeck = SelectedDecks.FirstOrDefault();
			var decks =
				DeckList.Instance.Decks.Where(
				                              d =>
				                              (string.IsNullOrEmpty(DeckNameFilter)
				                               || d.Name.ToLowerInvariant().Contains(DeckNameFilter!.ToLowerInvariant()))
				                              && DeckMatchesSelectedDeckType(d) && DeckMatchesSelectedTags(d)
				                              && (SelectedClasses.Any(
				                                                      c =>
				                                                      ((c.ToString() == "All" || d.Class == c.ToString()) && !d.Archived)
				                                                      || (c.ToString() == "Archived" && d.Archived)))).ToList();


			if(forceUpdate == null)
				forceUpdate = new List<Deck>();
			foreach(var deck in DisplayedDecks.Where(dpi => !decks.Contains(dpi.Deck) || forceUpdate.Contains(dpi.Deck)).ToList())
				DisplayedDecks.Remove(deck);
			foreach(var deck in decks.Where(d => !DisplayedDecks.Select(x => x.Deck).Contains(d)))
			{
				var dpi = GetDeckPickerItemFromCache(deck);
				if(dpi != null)
				{
					DisplayedDecks.Add(dpi);
					dpi.RefreshProperties();
				}
			}
			Sort();
			if(selectedDeck != null && reselectActiveDeck && decks.Contains(selectedDeck))
				SelectDeck(selectedDeck);
		}

		public void UpdateDeck(Deck deck)
		{
			var vm = DisplayedDecks.FirstOrDefault(x => Equals(x.Deck, deck));
			if(vm == null)
				return;
			vm.RefreshProperties();
			Sort(true);
			ListViewDecks.ScrollIntoView(vm);
		}

		private DeckPickerItemViewModel? GetDeckPickerItemFromCache(Deck deck)
		{
			if(deck == null)
				return null;
			if(_cachedDeckPickerItems.TryGetValue(deck, out var dpi))
				return dpi;
			Type layout;
			switch(Config.Instance.DeckPickerItemLayout)
			{
				case DeckLayout.Layout1:
					layout = typeof(DeckPickerItemLayout1);
					break;
				case DeckLayout.Layout2:
					layout = typeof(DeckPickerItemLayout2);
					break;
				case DeckLayout.Legacy:
					layout = typeof(DeckPickerItemLayoutLegacy);
					break;
				default:
					layout = typeof(DeckPickerItemLayout1);
					break;
			}
			dpi = new DeckPickerItemViewModel(deck, layout);
			_cachedDeckPickerItems.Add(deck, dpi);
			return dpi;
		}

		public void ClearFromCache(Deck deck) => _cachedDeckPickerItems.Remove(deck);

		public void UpdateArchivedClassVisibility()
		{
			if(DeckList.Instance.Decks.Any(d => d.Archived))
			{
				if(!_classItems.Contains(_archivedClassItem))
				{
					_classItems.Add(_archivedClassItem);
					ArchivedClassVisible = true;
				}
			}
			else
			{
				if (_classItems.Remove(_archivedClassItem))
					ArchivedClassVisible = false;

				SelectedClasses.Remove(HeroClassAll.Archived);
				if(SelectedClasses.Count == 0)
					SelectClass(HeroClassAll.All);
			}
		}

		private bool IsConstructedDeck(Deck deck) => !deck.IsBrawlDeck && !deck.IsDungeonDeck && !deck.IsDuelsDeck && !deck.IsArenaDeck;

		private bool DeckMatchesSelectedDeckType(Deck deck)
		{
			switch(Config.Instance.SelectedDeckPickerDeckType)
			{
				case DeckType.All:
					return true;
				case DeckType.Arena:
					return deck.IsArenaDeck;
				case DeckType.Dungeon:
					return deck.IsDungeonDeck;
				case DeckType.Duels:
					return deck.IsDuelsDeck;
				case DeckType.Brawl:
					return deck.IsBrawlDeck;
				case DeckType.Standard:
					return IsConstructedDeck(deck) && deck.StandardViable;
				case DeckType.Wild:
					return IsConstructedDeck(deck) && !deck.IsClassicDeck && (Config.Instance.DeckPickerWildIncludesStandard || !deck.StandardViable);
				case DeckType.Classic:
					return IsConstructedDeck(deck) && deck.IsClassicDeck;
				case DeckType.Twist:
					return IsConstructedDeck(deck) && deck.IsTwistDeck;
				default:
					return false;
			}
		}

		private void Sort(bool refresh = false)
		{
			var view = (CollectionView)CollectionViewSource.GetDefaultView(DisplayedDecks);

			var sorting = new List<SortDescription>();
			if(Config.Instance.SortDecksFavoritesFirst)
				sorting.Add(new SortDescription("Favorite", Descending));

			if(Config.Instance.SortDecksByClass && Config.Instance.SelectedDeckPickerDeckType != DeckType.Arena
			   || Config.Instance.SortDecksByClassArena && Config.Instance.SelectedDeckPickerDeckType == DeckType.Arena)
				sorting.Add(new SortDescription("Class", Ascending));

			var deckSorting = Config.Instance.SelectedDeckPickerDeckType == DeckType.Arena
				                  ? Config.Instance.SelectedDeckSortingArena : Config.Instance.SelectedDeckSorting;
			switch(deckSorting)
			{
				case "Most Played":
					sorting.Add(new SortDescription("NumGames", Descending));
					break;
				case "Name":
					sorting.Add(new SortDescription("DeckName", Ascending));
					break;
				case "Last Played":
					sorting.Add(new SortDescription("LastPlayed", Descending));
					break;
				case "Last Played (new first)":
					sorting.Add(new SortDescription("LastPlayedNewFirst", Descending));
					break;
				case "Last Edited":
					sorting.Add(new SortDescription("LastEdited", Descending));
					break;
				case "Tag":
					sorting.Add(new SortDescription("TagList", Ascending));
					break;
				case "Win Rate":
					sorting.Add(new SortDescription("WinPercent", Descending));
					break;
			}

			// Fallback to ensure consistent order
			sorting.Add(new SortDescription("LastEdited", Descending));
			sorting.Add(new SortDescription("DeckId", Descending));

			if(view.SortDescriptions.Select(x => x.PropertyName).SequenceEqual(sorting.Select(x => x.PropertyName)))
			{
				if(refresh)
					view.Refresh();
				return;
			}

			view.SortDescriptions.Clear();
			foreach(var s in sorting)
				view.SortDescriptions.Add(s);
		}

		private void ListViewDecks_OnSelectionChanged(object sender, SelectionChangedEventArgs e)
		{
			foreach(var deck in e.AddedItems.Cast<DeckPickerItemViewModel>())
				deck.IsSelected = true;
			foreach(var deck in e.RemovedItems.Cast<DeckPickerItemViewModel>())
				deck.IsSelected = false;
			OnSelectedDeckChanged?.Invoke(this, SelectedDecks);
		}

		public async void SelectDeckAndAppropriateView(Deck deck, bool forceUpdate = false)
		{
			ClearFromCache(deck);
			if((DeckType?)ListViewDeckType.SelectedItem != DeckType.All)
			{
				if(deck.IsArenaDeck)
					SelectDeckType(DeckType.Arena);
				else if(deck.IsDungeonDeck)
					SelectDeckType(DeckType.Dungeon);
				else if(deck.IsDuelsDeck)
					SelectDeckType(DeckType.Duels);
				else if(deck.IsBrawlDeck)
					SelectDeckType(DeckType.Brawl);
				else if(IsConstructedDeck(deck) && (DeckType?)ListViewDeckType.SelectedItem != DeckType.Wild)
				{
					if(deck.StandardViable)
						SelectDeckType(DeckType.Standard);
					else if (deck.IsClassicDeck)
						SelectDeckType(DeckType.Classic);
					else if (deck.IsTwistDeck)
						SelectDeckType(DeckType.Twist);
					else
						SelectDeckType(DeckType.Wild);
				}
			}
			if(deck.Archived)
				SelectClass(HeroClassAll.Archived);
			else if(!SelectedClasses.Contains(HeroClassAll.All) && Enum.TryParse(deck.Class, out HeroClassAll deckClass) && !SelectedClasses.Contains(deckClass))
				SelectClass(deckClass);

			if(!DeckMatchesSelectedTags(deck))
			{
				if(Config.Instance.TagOperation == TagFilerOperation.Or)
				{
					var missingTags = deck.Tags.Where(tag => !Config.Instance.SelectedTags.Contains(tag)).ToList();
					if(missingTags.Any())
					{
						Config.Instance.SelectedTags.AddRange(missingTags);
						Log.Info("Added missing tags so the deck shows up: " + missingTags.Aggregate((c, n) => c + ", " + n));
					}
					else
					{
						Config.Instance.SelectedTags.Add("None");
						Log.Info("Added missing tags so the deck shows up: None");
					}
				}
				else
				{
					Config.Instance.SelectedTags = new List<string> {"All"};
					Log.Info("Set tags to ALL so the deck shows up");
				}
				Config.Save();
				this.ParentMainWindow()?.SortFilterDecksFlyout.SetSelectedTags(Config.Instance.SelectedTags);
			}

			UpdateDecks(false, forceUpdate: forceUpdate ? new[] { deck } : null);
			SelectDeck(deck);
			var dpi = DisplayedDecks.FirstOrDefault(x => Equals(x.Deck, deck));
			if(dpi != null)
				ListViewDecks.ScrollIntoView(dpi);
		}

		private void SelectDeck(Deck? deck)
		{
			if(deck == null)
				return;
			ChangedSelection = true;
			var dpi = DisplayedDecks.FirstOrDefault(x => Equals(x.Deck, deck));
			if(ListViewDecks.SelectedItem != dpi)
			{
				if(dpi == null)
				{
					if(deck.Archived)
						SelectClass(HeroClassAll.Archived);
					else
					{
						if(Enum.TryParse(deck.Class, out HeroClassAll heroClass))
							SelectClass(heroClass);
					}

					var deckType = (DeckType)ListViewDeckType.SelectedIndex;
					if(deckType != DeckType.All && deck.IsArenaDeck != (deckType == DeckType.Arena))
						SelectDeckType(DeckType.All);

					UpdateDecks();
					dpi = DisplayedDecks.FirstOrDefault(x => Equals(x.Deck, deck));
					if(dpi == null)
					{
						ChangedSelection = false;
						return;
					}
				}
				ListViewDecks.SelectedItem = dpi;
			}
			ChangedSelection = false;
		}

		public void RefreshDisplayedDecks()
		{
			foreach(var deckPickerItem in DisplayedDecks)
				deckPickerItem.RefreshProperties();
		}

		private bool DeckMatchesSelectedTags(Deck deck)
		{
			var selectedTags = Config.Instance.SelectedTags;
			return selectedTags.Any(t => t == "All")
			       || (Config.Instance.TagOperation == TagFilerOperation.Or
				           ? selectedTags.Any(t => deck.Tags.Contains(t) || t == "None" && deck.Tags.Count == 0)
				           : selectedTags.All(t => deck.Tags.Contains(t) || t == "None" && deck.Tags.Count == 0));
		}

		private async void ListViewDecks_OnMouseDoubleClick(object sender, MouseButtonEventArgs e)
		{
			//wait for doubleclick to be over to not reselect the deck
			await Task.Delay(SystemInformation.DoubleClickTime); // Not sure if this is actually needed.

			if(SelectedDecks.FirstOrDefault() is Deck deck)
				DeckList.Instance.ActiveDeck = deck;
		}

		private void ListViewDeckType_OnSelectionChanged(object sender, SelectionChangedEventArgs e)
		{
			if(_ignoreSelectionChange || !Core.Initialized)
				return;
			if(e.AddedItems.Count > 0)
			{
				var selected = (DeckType)ListViewDeckType.SelectedItem;
				if(Config.Instance.SelectedDeckPickerDeckType != selected)
				{
					Config.Instance.SelectedDeckPickerDeckType = selected;
					Config.Save();
				}
				UpdateDecks();
			}
			else
				ListViewDeckType.SelectedIndex = 0;
		}

		public void SelectDeckType(DeckType selectedDeckType, bool ignoreSelectionChange = false)
		{
			var index = DeckTypeItems.IndexOf(selectedDeckType);
			if(ListViewDeckType.SelectedIndex == index)
				return;
			if(ignoreSelectionChange)
				_ignoreSelectionChange = true;
			ListViewDeckType.SelectedIndex = index;
			Config.Instance.SelectedDeckPickerDeckType = selectedDeckType;
			if(ignoreSelectionChange)
				_ignoreSelectionChange = false;
		}

		private void RectangleSearchIcon_OnPreviewMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
		{
			SearchBarVisibile = true;
			TextBoxSearchBar.Focus();
		}

		private void RectangleCloseIcon_OnPreviewMouseLeftButtonUp(object sender, MouseButtonEventArgs e) => CloseSearchField();

		private void TextBoxSearchBar_OnPreviewKeyDown(object sender, KeyEventArgs e)
		{
			if(e.Key == Key.Enter)
			{
				DeckNameFilter = TextBoxSearchBar.Text;
				UpdateDecks();
				e.Handled = true;
			}
			else if(e.Key == Key.Escape)
				CloseSearchField();
		}

		private void CloseSearchField()
		{
			var updateDecks = !string.IsNullOrEmpty(DeckNameFilter);
			TextBoxSearchBar.Clear();
			DeckNameFilter = null;
			SearchBarVisibile = false;
			if(updateDecks)
				UpdateDecks();
		}

		private void ContextMenu_OnOpened(object sender, RoutedEventArgs e)
		{
			if(this.ParentMainWindow() is not { } window)
				return;
			var selectedDecks = window.DeckPickerList.SelectedDecks;
			if(!selectedDecks.Any())
				return;
			window.TagControlEdit.SetSelectedTags(selectedDecks);
			MenuItemQuickSetTag.ItemsSource = window.TagControlEdit.Tags;
			MenuItemMoveDecktoArena.Visibility = selectedDecks.First().IsArenaDeck ? Collapsed : Visible;
			MenuItemMoveDeckToConstructed.Visibility = selectedDecks.First().IsArenaDeck ? Visible : Collapsed;
			MenuItemMissingCards.Visibility = selectedDecks.First().MissingCards.Any() ? Visible : Collapsed;
			MenuItemSetDeckUrl.Visibility = selectedDecks.First().IsArenaDeck ? Collapsed : Visible;
			MenuItemSetDeckUrl.Header = string.IsNullOrEmpty(selectedDecks.First().Url) ? LocUtil.Get(LocLink, true) : LocUtil.Get(LocLinkNew, true);
			MenuItemUpdateDeck.Visibility = string.IsNullOrEmpty(selectedDecks.First().Url) ? Collapsed : Visible;
			MenuItemOpenUrl.Visibility = string.IsNullOrEmpty(selectedDecks.First().Url) ? Collapsed : Visible;
			MenuItemArchive.Visibility = selectedDecks.Any(d => !d.Archived) ? Visible : Collapsed;
			MenuItemUnarchive.Visibility = selectedDecks.Any(d => d.Archived) ? Visible : Collapsed;
			SeparatorDeck1.Visibility = selectedDecks.First().IsArenaDeck ? Collapsed : Visible;
			MenuItemUseDeck.Visibility =
				SeparatorUseDeck.Visibility =
				selectedDecks.First().Equals(DeckList.Instance.ActiveDeck) ? Collapsed : Visible;
			MenuItemExportDeck.Visibility = selectedDecks.First().IsArenaDeck ? Collapsed : Visible;
			MenuItemVersionHistory.Visibility = selectedDecks.First().HasVersions ? Visible : Collapsed;
		}


		private void BtnEditDeck_Click(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.ShowDeckEditorFlyout(SelectedDecks.FirstOrDefault(), false);
		private void BtnNotes_Click(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.ShowDeckNotesDialog(SelectedDecks.FirstOrDefault());
		private void BtnTags_Click(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.ShowTagEditDialog(SelectedDecks);
		private void BtnMoveDeckToArena_Click(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.MoveDecksToArena(SelectedDecks);
		private void BtnMoveDeckToConstructed_Click(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.MoveDecksToConstructed(SelectedDecks);
		private void MenuItemMissingDust_OnClick(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.ShowMissingCardsMessage(SelectedDecks.FirstOrDefault(), false).Forget();
		private void BtnSetDeckUrl_Click(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.SetDeckUrl(SelectedDecks.FirstOrDefault());
		private void BtnUpdateDeck_Click(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.UpdateDeckFromWeb(SelectedDecks.FirstOrDefault());
		private void BtnOpenDeckUrl_Click(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.OpenDeckUrl(SelectedDecks.FirstOrDefault());
		private void BtnArchiveDeck_Click(object sender, RoutedEventArgs e)
		{
			foreach (var deck in SelectedDecks)
				deck.Archive(true);
		}

		private void BtnUnarchiveDeck_Click(object sender, RoutedEventArgs e)
		{
			foreach (var deck in SelectedDecks)
				deck.Archive(false);
		}

		private void BtnDeleteDeck_Click(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.ShowDeleteDecksMessage(SelectedDecks);
		private void BtnCloneDeck_Click(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.ShowCloneDeckDialog(SelectedDecks.FirstOrDefault());
		private void BtnCloneSelectedVersion_Click(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.ShowCloneDeckVersionDialog(SelectedDecks.FirstOrDefault());
		private void BtnName_Click(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.ShowEditDeckNameDialog(SelectedDecks.FirstOrDefault());
		private void BtnExportDeck_Click(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.ShowExportFlyout(SelectedDecks.FirstOrDefault());
		private void BtnScreenshotCards_Click(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.ShowScreenshotFlyout();
		private void MenuItemVersionHistory_OnClick(object sender, RoutedEventArgs e) => this.ParentMainWindow()?.ShowDeckHistoryFlyout();

		private void ActiveDeckPanel_OnPreviewMouseDown(object sender, MouseButtonEventArgs e)
		{
			if((DateTime.Now - _lastActiveDeckPanelClick).TotalMilliseconds < SystemInformation.DoubleClickTime)
			{
				if(ActiveDeck != null)
					SelectDeckAndAppropriateView(ActiveDeck);
				_lastActiveDeckPanelClick = DateTime.MinValue;
			}
			else
				_lastActiveDeckPanelClick = DateTime.Now;
		}

		private void BtnUseDeck_Click(object sender, RoutedEventArgs e)
		{
			var deck = SelectedDecks.FirstOrDefault();
			if(deck != null)
				DeckList.Instance.ActiveDeck = deck;
		}

		private void RectangleSortIcon_OnPreviewMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
		{
			if(this.ParentMainWindow() is {} window)
				window.FlyoutSortFilter.IsOpen = !window.FlyoutSortFilter.IsOpen;
		}

		private void RectangleUseNoDeckIcon_OnPreviewMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
		{
			if(DeckList.Instance.ActiveDeck == null)
				DeckList.Instance.ActiveDeck = DeckList.Instance.GetLastUsedDeck();
			else
				DeckList.Instance.ActiveDeck = null;
			UpdateDeckModeToggleButton();
		}

		private void UpdateDeckModeToggleButton()
		{
			OnPropertyChanged(nameof(BorderDeckModeBackground));
			OnPropertyChanged(nameof(BorderDeckModeTextBrush));
		}

		public SolidColorBrush BorderDeckModeBackground
			=> DeckList.Instance.ActiveDeck == null ? (SolidColorBrush)FindResource("AccentColorBrush") : new SolidColorBrush(Colors.Transparent);

		public SolidColorBrush BorderDeckModeTextBrush
			=> DeckList.Instance.ActiveDeck == null ? new SolidColorBrush(Colors.White) : (SolidColorBrush)FindResource("TextBrush");

		private void ListViewDecks_OnKeyUp(object sender, KeyEventArgs e)
		{
			if(e.Key == Key.Delete)
				this.ParentMainWindow()?.ShowDeleteDecksMessage(SelectedDecks);
		}

		private void ListViewDecks_OnPreviewMouseRightButtonUp(object sender, MouseButtonEventArgs e)
		{
			if(!SelectedDecks.Any())
				e.Handled = true;
		}

		private void DeckPicker_OnLoaded(object sender, RoutedEventArgs e)
		{
			UpdateArchivedClassVisibility();
			SelectClass(Config.Instance.SelectedDeckPickerClasses.FirstOrDefault(x => x != HeroClassAll.Archived)); // Default is "All"
			SelectDeckType(Config.Instance.SelectedDeckPickerDeckType);
			if(DeckList.Instance.ActiveDeck != null)
				SelectDeckAndAppropriateView(DeckList.Instance.ActiveDeck);
		}
	}
}
