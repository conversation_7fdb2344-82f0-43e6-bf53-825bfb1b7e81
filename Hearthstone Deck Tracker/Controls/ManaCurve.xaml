<UserControl x:Class="Hearthstone_Deck_Tracker.ManaCurve"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             xmlns:utility="clr-namespace:Hearthstone_Deck_Tracker.Utility"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d"
             d:DesignHeight="300" Width="235">
    <DockPanel>
        <DockPanel Name="ListViewStatType" DockPanel.Dock="Top" Height="28">
            <Border BorderThickness="1"
                Width="100" Height="28" DockPanel.Dock="Right" Margin="2,-2,0,0"
                BorderBrush="{DynamicResource TextBrush}" PreviewMouseLeftButtonUp="ManaCurveMechanics_OnPreviewMouseLeftButtonUp">
                <Grid>
                    <Grid.Style>
                        <Style>
                            <Style.Triggers>
                                <Trigger Property="Border.IsMouseOver" Value="True">
                                    <Setter Property="Border.Background" Value="{DynamicResource AccentColorBrush3}" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Grid.Style>
                    <TextBlock Name="TextBlockManaCurveMechanics" Text="{lex:LocTextUpper ManaCurve_Button_Mechanics}" Margin="2" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="14"
                           FontWeight="SemiBold"/>
                </Grid>
            </Border>


            <ComboBox Name="ComboBoxStatType" Height="28" Margin="0,-2,0,0"
                      utility:ComboBoxHelper.SelectionChanged="ComboBoxStatType_OnSelectionChanged">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding DisplayName}" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="14"
                           FontWeight="SemiBold"/>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>

        </DockPanel>
        
        <Border BorderThickness="1" BorderBrush="{DynamicResource TextBrush}" Margin="0,2,0,0">
            <Grid>
                <Grid Name="GridManaCurve" Background="{DynamicResource {x:Static SystemColors.ControlDarkDarkBrushKey}}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Rectangle Fill="{DynamicResource {x:Static SystemColors.ControlDarkBrushKey}}" Grid.Row="1"
                           Grid.ColumnSpan="8" Margin="1" />
                <local:ManaCostBar x:Name="ManaCostBar0" Width="auto" Height="auto" Grid.Column="0" Grid.Row="0" />
                <TextBlock Text="0" FontSize="14" Grid.Column="0" Grid.Row="1" FontWeight="SemiBold" Foreground="Black"
                           VerticalAlignment="Center" HorizontalAlignment="Center" />

                <local:ManaCostBar x:Name="ManaCostBar1" Width="auto" Height="auto" Grid.Column="1" Grid.Row="0"
                                   BorderBrush="{DynamicResource {x:Static SystemColors.ControlDarkDarkBrush}}" />

                <TextBlock Text="1" FontSize="14" Grid.Column="1" Grid.Row="1" FontWeight="SemiBold" Foreground="Black"
                           VerticalAlignment="Center" HorizontalAlignment="Center" />

                <local:ManaCostBar x:Name="ManaCostBar2" Width="auto" Height="auto" Grid.Column="2" Grid.Row="0"
                                   BorderBrush="{DynamicResource {x:Static SystemColors.ControlDarkDarkBrush}}" />
                <TextBlock Text="2" FontSize="14" Grid.Column="2" Grid.Row="1" FontWeight="SemiBold" Foreground="Black"
                           VerticalAlignment="Center" HorizontalAlignment="Center" />

                <local:ManaCostBar x:Name="ManaCostBar3" Width="auto" Height="auto" Grid.Column="3" Grid.Row="0"
                                   BorderBrush="{DynamicResource {x:Static SystemColors.ControlDarkDarkBrush}}" />
                <TextBlock Text="3" FontSize="14" Grid.Column="3" Grid.Row="1" FontWeight="SemiBold" Foreground="Black"
                           VerticalAlignment="Center" HorizontalAlignment="Center" />

                <local:ManaCostBar x:Name="ManaCostBar4" Width="auto" Height="auto" Grid.Column="4" Grid.Row="0"
                                   BorderBrush="{DynamicResource {x:Static SystemColors.ControlDarkDarkBrush}}" />
                <TextBlock Text="4" FontSize="14" Grid.Column="4" Grid.Row="1" FontWeight="SemiBold" Foreground="Black"
                           VerticalAlignment="Center" HorizontalAlignment="Center" />

                <local:ManaCostBar x:Name="ManaCostBar5" Width="auto" Height="auto" Grid.Column="5" Grid.Row="0"
                                   BorderBrush="{DynamicResource {x:Static SystemColors.ControlDarkDarkBrush}}" />
                <TextBlock Text="5" FontSize="14" Grid.Column="5" Grid.Row="1" FontWeight="SemiBold" Foreground="Black"
                           VerticalAlignment="Center" HorizontalAlignment="Center" />

                <local:ManaCostBar x:Name="ManaCostBar6" Width="auto" Height="auto" Grid.Column="6" Grid.Row="0"
                                   BorderBrush="{DynamicResource {x:Static SystemColors.ControlDarkDarkBrush}}" />
                <TextBlock Text="6" FontSize="14" Grid.Column="6" Grid.Row="1" FontWeight="SemiBold" Foreground="Black"
                           VerticalAlignment="Center" HorizontalAlignment="Center" />

                <local:ManaCostBar x:Name="ManaCostBar7" Width="auto" Height="auto" Grid.Column="7" Grid.Row="0"
                                   BorderBrush="{DynamicResource {x:Static SystemColors.ControlDarkDarkBrush}}" />
                <TextBlock Text="7" FontSize="14" Grid.Column="7" Grid.Row="1" FontWeight="SemiBold" Foreground="Black"
                           VerticalAlignment="Center" HorizontalAlignment="Center" />
                <TextBlock Text="+" Margin="14,0,4,0" FontSize="16" Grid.Column="7" Grid.Row="1" Foreground="Black" />
            </Grid>

                <Border Name="BorderMechanics" Margin="10" Width="160" VerticalAlignment="Center" HorizontalAlignment="Center" BorderBrush="{DynamicResource TextBrush}" BorderThickness="1" Visibility="Collapsed">
                    <Grid>
                        <ScrollViewer VerticalScrollBarVisibility="Auto" Background="{DynamicResource WindowBackgroundBrush}">
                            <ItemsControl Name="ItemsControlMechanics">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding DisplayValue}" FontSize="14"
                           FontWeight="SemiBold" Margin="2,0,0,0"/>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                        <TextBlock Name="TextBlockNoMechanics" Text="None"  Background="{DynamicResource WindowBackgroundBrush}" FontSize="14"
                           FontWeight="SemiBold" Margin="2,0,0,0"/>
                    </Grid>
                </Border>
            </Grid>
        </Border>
    </DockPanel>
</UserControl>
