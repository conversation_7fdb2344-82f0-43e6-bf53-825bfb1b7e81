﻿<UserControl x:Class="Hearthstone_Deck_Tracker.ManaCostBar"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls"
             mc:Ignorable="d"
             d:DesignHeight="300" d:DesignWidth="300"
             Background="{DynamicResource {x:Static SystemColors.ControlDarkBrushKey}}"
             BorderBrush="{DynamicResource {x:Static SystemColors.ActiveBorderBrush}}" BorderThickness="1">
    <UserControl.ToolTip>
        <local:ManaCostBarToolTip x:Name="McbToolTip" />
    </UserControl.ToolTip>
    <UserControl.Resources>
        <Style x:Key="{x:Type ToolTip}" TargetType="{x:Type ToolTip}">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderBrush" Value="Transparent" />
        </Style>
    </UserControl.Resources>
    <Grid>
        <StackPanel Orientation="Vertical" VerticalAlignment="Bottom" Margin="0,1,0,0">
            <Rectangle Name="WeaponsRect" Height="0" Stroke="#FFD7AABE" StrokeThickness="-1">
                <Rectangle.Fill>
                    <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                        <GradientStop Color="#FFAA1E28" Offset="0" />
                        <GradientStop Color="#FFD7AABE" Offset="1" />
                    </LinearGradientBrush>
                </Rectangle.Fill>
            </Rectangle>
            <Rectangle Name="SpellsRect" Height="0" Stroke="#FFA0C8D7" StrokeThickness="-1">
                <Rectangle.Fill>
                    <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                        <GradientStop Color="#FF1E6EAA" Offset="0" />
                        <GradientStop Color="#FFA0C8D7" Offset="1" />
                    </LinearGradientBrush>
                </Rectangle.Fill>
            </Rectangle>
            <Rectangle Name="MinionsRect" Height="0" Stroke="#FFC8D7A0" StrokeThickness="-1">
                <Rectangle.Fill>
                    <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                        <GradientStop Color="#FF6EAA1E" Offset="0.003" />
                        <GradientStop Color="#FFC8D7A0" Offset="1" />
                    </LinearGradientBrush>
                </Rectangle.Fill>
            </Rectangle>
            <Rectangle Name="HeroesRect" Height="0" Stroke="#FFEAD595" StrokeThickness="-1">
                <Rectangle.Fill>
                    <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                        <GradientStop Color="#FF9E7E1F" Offset="0.003" />
                        <GradientStop Color="#FFEAD595" Offset="1" />
                    </LinearGradientBrush>
                </Rectangle.Fill>
            </Rectangle>
        </StackPanel>
        <TextBlock Name="TextBlockCount" FontWeight="SemiBold" Text= "0" Height="26" Foreground="Black"
               VerticalAlignment="Bottom" HorizontalAlignment="Center" FontSize="12"/>

    </Grid>
</UserControl>
