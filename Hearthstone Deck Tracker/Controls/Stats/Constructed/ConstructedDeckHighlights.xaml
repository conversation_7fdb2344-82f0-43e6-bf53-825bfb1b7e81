﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Stats.Constructed.ConstructedDeckHighlights"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:converters="clr-namespace:Hearthstone_Deck_Tracker.Controls.Stats.Converters"
             DataContextChanged="ConstructedDeckHighlights_OnDataContextChanged"
             mc:Ignorable="d" Name="UserControlConstructedDeckHighlights"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             d:DesignHeight="300" d:DesignWidth="300" Width="280">
    <UserControl.Resources>
        <converters:IsNullConverter x:Key="IsNullConverter" />
        <!-- Fallback values don't allow for properties to be bound to a DynamicResource.
             In this case: Foreground. If the binding is null, the fallback needs to be Black or White,
             depening on whether the control is on a window or flyout.
             Solution: Have another textblock that has the fallback value and hides depending on the DataContext.. -->
        <Style TargetType="TextBlock" x:Key="StyleVisibilityCollapsedIfDataContextNull">
            <Style.Triggers>
                <DataTrigger Binding="{Binding ElementName=UserControlConstructedDeckHighlights, Path=DataContext, Converter={StaticResource IsNullConverter}}" Value="True">
                    <Setter Property="Visibility" Value="Collapsed"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
        <Style TargetType="TextBlock" x:Key="StyleVisibilityCollapsedIfDataContextNotNull">
            <Style.Triggers>
                <DataTrigger Binding="{Binding ElementName=UserControlConstructedDeckHighlights, Path=DataContext, Converter={StaticResource IsNullConverter}}" Value="False">
                    <Setter Property="Visibility" Value="Collapsed"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>
    <StackPanel Orientation="Horizontal">
        <StackPanel Margin="5,0,10,0" VerticalAlignment="Center" Visibility="{Binding Path=ImageVisiblity, RelativeSource={RelativeSource AncestorType=UserControl}}">
            <Image Source="{Binding Path=ClassImage, RelativeSource={RelativeSource AncestorType=UserControl}}" Width="48" Height="48" RenderOptions.BitmapScalingMode="Fant"/>
            <TextBlock Text="{Binding Path=ClassName, RelativeSource={RelativeSource AncestorType=UserControl}}" FontWeight="SemiBold" HorizontalAlignment="Center"/>
        </StackPanel>
        <StackPanel>
            <StackPanel Orientation="Horizontal" Visibility="{Binding Path=ImageVisiblity, RelativeSource={RelativeSource AncestorType=UserControl}}">
                <TextBlock VerticalAlignment="Center" Text="{lex:LocText Stats_Constructed_DeckHightlights_Label_Deck, Suffix=' '}"/>
                <TextBlock VerticalAlignment="Center" Style="{StaticResource StyleVisibilityCollapsedIfDataContextNull}"
                           Text="{Binding DeckName}" TextTrimming="CharacterEllipsis">
                </TextBlock>
                <TextBlock VerticalAlignment="Center" Text="-" Style="{StaticResource StyleVisibilityCollapsedIfDataContextNotNull}"/>
            </StackPanel>
            <StackPanel Orientation="Horizontal">
                <TextBlock VerticalAlignment="Center" Text="{lex:LocText Stats_Constructed_DeckHightlights_Label_Winrate, Suffix=' '}"/>
                <TextBlock VerticalAlignment="Center" Foreground="{Binding WinRateTextBrush}" Style="{StaticResource StyleVisibilityCollapsedIfDataContextNull}">
                    <TextBlock.Text>
                        <MultiBinding StringFormat="{}{0}-{1} ({2}%)">
                            <Binding Path="Wins"/>
                            <Binding Path="Losses"/>
                            <Binding Path="WinRatePercent"/>
                        </MultiBinding>
                    </TextBlock.Text>
                </TextBlock>
                <TextBlock VerticalAlignment="Center" Text="-" Style="{StaticResource StyleVisibilityCollapsedIfDataContextNotNull}"/>
            </StackPanel>
            <StackPanel Orientation="Horizontal">
                <TextBlock VerticalAlignment="Center" Text="{lex:LocText Stats_Constructed_DeckHightlights_Label_TimePlayed, Suffix=' '}"/>
                <TextBlock VerticalAlignment="Center">
                    <TextBlock.Text>
                        <Binding StringFormat="{}{0:0.0} hours" Path="TotalDuration.TotalHours" FallbackValue="0"/>
                    </TextBlock.Text>
                </TextBlock>
            </StackPanel>
            <StackPanel Orientation="Horizontal">
                <TextBlock VerticalAlignment="Center" Text="{lex:LocText Stats_Constructed_DeckHightlights_Label_AverageGame, Suffix=' '}"/>
                <TextBlock VerticalAlignment="Center">
                    <TextBlock.Text>
                        <MultiBinding StringFormat="{}{0:0.0} min, {1:0.0} turns">
                            <Binding Path="AverageDuration.TotalMinutes"/>
                            <Binding Path="AverageTurns"/>
                        </MultiBinding>
                    </TextBlock.Text>
                </TextBlock>
            </StackPanel>
            <StackPanel Orientation="Horizontal">
                <TextBlock VerticalAlignment="Center" Text="{lex:LocText Stats_Constructed_DeckHightlights_Label_BestMatchup, Suffix=' '}"/>
                <TextBlock VerticalAlignment="Center" Foreground="{Binding BestMatchup.WinRateTextBrush}" Style="{StaticResource StyleVisibilityCollapsedIfDataContextNull}">
                    <TextBlock.Text>
                        <MultiBinding StringFormat="{}{0} ({1}-{2}, {3}%)">
                            <Binding Path="BestMatchup.Class"/>
                            <Binding Path="BestMatchup.Wins"/>
                            <Binding Path="BestMatchup.Losses"/>
                            <Binding Path="BestMatchup.WinRatePercent"/>
                        </MultiBinding>
                    </TextBlock.Text>
                </TextBlock>
                <TextBlock VerticalAlignment="Center" Text="{lex:LocText Stats_Constructed_DeckHightlights_Label_BestMatchupFallback, Suffix=' '}" Style="{StaticResource StyleVisibilityCollapsedIfDataContextNotNull}"/>
            </StackPanel>
            <StackPanel Orientation="Horizontal">
                <TextBlock VerticalAlignment="Center" Text="{lex:LocText Stats_Constructed_DeckHightlights_Label_WorstMatchup, Suffix=' '}"/>
                <TextBlock VerticalAlignment="Center" Foreground="{Binding WorstMatchup.WinRateTextBrush}" Style="{StaticResource StyleVisibilityCollapsedIfDataContextNull}">
                    <TextBlock.Text>
                        <MultiBinding StringFormat="{}{0} ({1}-{2}, {3}%)">
                            <Binding Path="WorstMatchup.Class"/>
                            <Binding Path="WorstMatchup.Wins"/>
                            <Binding Path="WorstMatchup.Losses"/>
                            <Binding Path="WorstMatchup.WinRatePercent"/>
                        </MultiBinding>
                    </TextBlock.Text>
                </TextBlock>
                <TextBlock VerticalAlignment="Center" Text="{lex:LocText Stats_Constructed_DeckHightlights_Label_WorstMatchupFallback, Suffix=' '}" Style="{StaticResource StyleVisibilityCollapsedIfDataContextNotNull}"/>
            </StackPanel>
        </StackPanel>
    </StackPanel>
</UserControl>
