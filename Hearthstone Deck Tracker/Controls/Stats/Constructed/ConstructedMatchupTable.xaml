﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Stats.Constructed.ConstructedMatchupTable"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls.Stats.Constructed"
             xmlns:utility="clr-namespace:Hearthstone_Deck_Tracker.Utility"
             xmlns:compiledStats="clr-namespace:Hearthstone_Deck_Tracker.Stats.CompiledStats"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="300">
    <DataGrid Name="DataGridMatchups" ItemsSource="{Binding Path=Matchups, Source={x:Static compiledStats:ConstructedStats.Instance}, IsAsync=True, NotifyOnTargetUpdated=True}" 
               AutoGenerateColumns="False" CanUserAddRows="False" CanUserDeleteRows="False" CanUserResizeRows="False" IsReadOnly="True" CanUserSortColumns="False" CanUserResizeColumns="False" CanUserReorderColumns="False">
        <DataGrid.Columns>
            <DataGridTemplateColumn Header="{lex:Loc Stats_Constructed_MatchupTable_Label}" SortMemberPath="Class">
                <DataGridTemplateColumn.CellTemplate>
                    <DataTemplate>
                        <Grid>
                            <Image Source="{Binding ClassImage}" Width="30" Height="30" RenderOptions.BitmapScalingMode="Fant" ToolTip="{Binding Class}" />
                            <TextBlock Text="{Binding Text}" Visibility="{Binding TextVisibility}" FontWeight="SemiBold"
                                       VerticalAlignment="Center" HorizontalAlignment="Center" TextAlignment="Center"/>
                        </Grid>
                    </DataTemplate>
                </DataGridTemplateColumn.CellTemplate>
            </DataGridTemplateColumn>
            <DataGridTextColumn Binding="{Binding Deathknight.Summary}" MinWidth="47">
                <DataGridTextColumn.HeaderTemplate>
                    <DataTemplate>
                        <Image RenderOptions.BitmapScalingMode="Fant" Source="{x:Static utility:ImageCache.Deathknight}" ToolTip="{lex:Loc Deathknight}" Width="24" Height="24" />
                    </DataTemplate>
                </DataGridTextColumn.HeaderTemplate>
                <DataGridTextColumn.ElementStyle>
                    <Style TargetType="TextBlock">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                        <Setter Property="Foreground" Value="{Binding Deathknight.WinRateTextBrush}" />
                    </Style>
                </DataGridTextColumn.ElementStyle>
            </DataGridTextColumn>
            <DataGridTextColumn Binding="{Binding DemonHunter.Summary}" MinWidth="47">
                <DataGridTextColumn.HeaderTemplate>
                    <DataTemplate>
                        <Image RenderOptions.BitmapScalingMode="Fant" Source="{x:Static utility:ImageCache.DemonHunter}" ToolTip="{lex:Loc DemonHunter}" Width="24" Height="24" />
                    </DataTemplate>
                </DataGridTextColumn.HeaderTemplate>
                <DataGridTextColumn.ElementStyle>
                    <Style TargetType="TextBlock">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                        <Setter Property="Foreground" Value="{Binding DemonHunter.WinRateTextBrush}" />
                    </Style>
                </DataGridTextColumn.ElementStyle>
            </DataGridTextColumn>
            <DataGridTextColumn Binding="{Binding Druid.Summary}" MinWidth="47">
                <DataGridTextColumn.HeaderTemplate>
                    <DataTemplate>
                        <Image RenderOptions.BitmapScalingMode="Fant" Source="{x:Static utility:ImageCache.Druid}" ToolTip="{lex:Loc Druid}" Width="24" Height="24" />
                    </DataTemplate>
                </DataGridTextColumn.HeaderTemplate>
                <DataGridTextColumn.ElementStyle>
                    <Style TargetType="TextBlock">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                        <Setter Property="Foreground" Value="{Binding Druid.WinRateTextBrush}" />
                    </Style>
                </DataGridTextColumn.ElementStyle>
            </DataGridTextColumn>
            <DataGridTextColumn Binding="{Binding Hunter.Summary}" MinWidth="47">
                <DataGridTextColumn.HeaderTemplate>
                    <DataTemplate>
                        <Image RenderOptions.BitmapScalingMode="Fant" Source="{x:Static utility:ImageCache.Hunter}" ToolTip="{lex:Loc Hunter}" Width="24" Height="24" />
                    </DataTemplate>
                </DataGridTextColumn.HeaderTemplate>
                <DataGridTextColumn.ElementStyle>
                    <Style TargetType="TextBlock">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                        <Setter Property="Foreground" Value="{Binding Hunter.WinRateTextBrush}" />
                    </Style>
                </DataGridTextColumn.ElementStyle>
            </DataGridTextColumn>
            <DataGridTextColumn Binding="{Binding Mage.Summary}" MinWidth="47">
                <DataGridTextColumn.HeaderTemplate>
                    <DataTemplate>
                        <Image RenderOptions.BitmapScalingMode="Fant" Source="{x:Static utility:ImageCache.Mage}" ToolTip="{lex:Loc Mage}" Width="24" Height="24" />
                    </DataTemplate>
                </DataGridTextColumn.HeaderTemplate>
                <DataGridTextColumn.ElementStyle>
                    <Style TargetType="TextBlock">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                        <Setter Property="Foreground" Value="{Binding Mage.WinRateTextBrush}" />
                    </Style>
                </DataGridTextColumn.ElementStyle>
            </DataGridTextColumn>
            <DataGridTextColumn Binding="{Binding Paladin.Summary}" MinWidth="47">
                <DataGridTextColumn.HeaderTemplate>
                    <DataTemplate>
                        <Image RenderOptions.BitmapScalingMode="Fant" Source="{x:Static utility:ImageCache.Paladin}" ToolTip="{lex:Loc Paladin}" Width="24" Height="24" />
                    </DataTemplate>
                </DataGridTextColumn.HeaderTemplate>
                <DataGridTextColumn.ElementStyle>
                    <Style TargetType="TextBlock">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                        <Setter Property="Foreground" Value="{Binding Paladin.WinRateTextBrush}" />
                    </Style>
                </DataGridTextColumn.ElementStyle>
            </DataGridTextColumn>
            <DataGridTextColumn Binding="{Binding Priest.Summary}" MinWidth="47">
                <DataGridTextColumn.HeaderTemplate>
                    <DataTemplate>
                        <Image RenderOptions.BitmapScalingMode="Fant" Source="{x:Static utility:ImageCache.Priest}" ToolTip="{lex:Loc Priest}" Width="24" Height="24" />
                    </DataTemplate>
                </DataGridTextColumn.HeaderTemplate>
                <DataGridTextColumn.ElementStyle>
                    <Style TargetType="TextBlock">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                        <Setter Property="Foreground" Value="{Binding Priest.WinRateTextBrush}" />
                    </Style>
                </DataGridTextColumn.ElementStyle>
            </DataGridTextColumn>
            <DataGridTextColumn Binding="{Binding Rogue.Summary}" MinWidth="47">
                <DataGridTextColumn.HeaderTemplate>
                    <DataTemplate>
                        <Image RenderOptions.BitmapScalingMode="Fant" Source="{x:Static utility:ImageCache.Rogue}" ToolTip="{lex:Loc Rogue}" Width="24" Height="24" />
                    </DataTemplate>
                </DataGridTextColumn.HeaderTemplate>
                <DataGridTextColumn.ElementStyle>
                    <Style TargetType="TextBlock">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                        <Setter Property="Foreground" Value="{Binding Rogue.WinRateTextBrush}" />
                    </Style>
                </DataGridTextColumn.ElementStyle>
            </DataGridTextColumn>
            <DataGridTextColumn Binding="{Binding Shaman.Summary}" MinWidth="47">
                <DataGridTextColumn.HeaderTemplate>
                    <DataTemplate>
                        <Image RenderOptions.BitmapScalingMode="Fant" Source="{x:Static utility:ImageCache.Shaman}" ToolTip="{lex:Loc Shaman}" Width="24" Height="24" />
                    </DataTemplate>
                </DataGridTextColumn.HeaderTemplate>
                <DataGridTextColumn.ElementStyle>
                    <Style TargetType="TextBlock">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                        <Setter Property="Foreground" Value="{Binding Shaman.WinRateTextBrush}" />
                    </Style>
                </DataGridTextColumn.ElementStyle>
            </DataGridTextColumn>
            <DataGridTextColumn Binding="{Binding Warlock.Summary}" MinWidth="47">
                <DataGridTextColumn.HeaderTemplate>
                    <DataTemplate>
                        <Image RenderOptions.BitmapScalingMode="Fant" Source="{x:Static utility:ImageCache.Warlock}" ToolTip="{lex:Loc Warlock}" Width="24" Height="24" />
                    </DataTemplate>
                </DataGridTextColumn.HeaderTemplate>
                <DataGridTextColumn.ElementStyle>
                    <Style TargetType="TextBlock">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                        <Setter Property="Foreground" Value="{Binding Warlock.WinRateTextBrush}" />
                    </Style>
                </DataGridTextColumn.ElementStyle>
            </DataGridTextColumn>
            <DataGridTextColumn Binding="{Binding Warrior.Summary}" MinWidth="47">
                <DataGridTextColumn.HeaderTemplate>
                    <DataTemplate>
                        <Image RenderOptions.BitmapScalingMode="Fant" Source="{x:Static utility:ImageCache.Warrior}" ToolTip="{lex:Loc Warrior}" Width="24" Height="24" />
                    </DataTemplate>
                </DataGridTextColumn.HeaderTemplate>
                <DataGridTextColumn.ElementStyle>
                    <Style TargetType="TextBlock">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                        <Setter Property="Foreground" Value="{Binding Warrior.WinRateTextBrush}" />
                    </Style>
                </DataGridTextColumn.ElementStyle>
            </DataGridTextColumn>
            <DataGridTextColumn Binding="{Binding Neutral.Summary}" MinWidth="47" Visibility="{Binding NeutralVisiblity, Source={x:Static compiledStats:ConstructedStats.Instance}}">
                <DataGridTextColumn.HeaderTemplate>
                    <DataTemplate>
                        <Image RenderOptions.BitmapScalingMode="Fant" Source="{x:Static utility:ImageCache.Neutral}" ToolTip="{lex:Loc Neutral}" Width="24" Height="24" />
                    </DataTemplate>
                </DataGridTextColumn.HeaderTemplate>
                <DataGridTextColumn.ElementStyle>
                    <Style TargetType="TextBlock">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                        <Setter Property="Foreground" Value="{Binding Neutral.WinRateTextBrush}" />
                    </Style>
                </DataGridTextColumn.ElementStyle>
            </DataGridTextColumn>
            <DataGridTextColumn Header="{lex:Loc Stats_Constructed_MatchupTable_Total}" Binding="{Binding Total.Summary}" MinWidth="47">
                <DataGridTextColumn.ElementStyle>
                    <Style TargetType="TextBlock">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                        <Setter Property="Foreground" Value="{Binding Total.WinRateTextBrush}" />
                    </Style>
                </DataGridTextColumn.ElementStyle>
            </DataGridTextColumn>
        </DataGrid.Columns>
    </DataGrid>
</UserControl>
