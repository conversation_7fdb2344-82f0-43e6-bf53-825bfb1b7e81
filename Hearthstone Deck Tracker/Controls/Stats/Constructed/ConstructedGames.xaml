<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Stats.Constructed.ConstructedGames"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:compiledStats="clr-namespace:Hearthstone_Deck_Tracker.Stats.CompiledStats"
             mc:Ignorable="d" Loaded="ConstructedGames_OnLoaded"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             d:DesignHeight="300" d:DesignWidth="800">
    <GroupBox Header="{lex:LocTextUpper Stats_Constructed_Games_Label_Matches}">
        <DockPanel>
            <StackPanel DockPanel.Dock="Bottom" Orientation="Horizontal" >
                <Button Margin="4,4,0,4" MinWidth="120" Click="ButtonAdd_OnClick" IsEnabled="{Binding ButtonAddGameIsEnabled, RelativeSource={RelativeSource AncestorType=UserControl}}"
                        ToolTip="{Binding ButtonAddGameIsEnabledToolTip, RelativeSource={RelativeSource AncestorType=UserControl}}" ToolTipService.ShowOnDisabled="True">
                    <StackPanel Orientation="Horizontal">
                        <Rectangle Width="12">
                            <Rectangle.Fill>
                                <VisualBrush Visual="{Binding AddIconVisual, RelativeSource={RelativeSource AncestorType=UserControl}}"/>
                            </Rectangle.Fill>
                        </Rectangle>
                        <TextBlock Text="{lex:LocTextUpper Stats_Constructed_Games_Button_AddGame}" Margin="4,0,0,0"/>
                    </StackPanel>
                </Button>
                <StackPanel Orientation="Horizontal" Visibility="{Binding MultiSelectPanelVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}">
                    <Button Margin="4,4,0,4" MinWidth="120" Click="ButtonMultiMove_OnClick" ToolTip="All selected games must have the same player hero."
                        ToolTipService.ShowOnDisabled="True" IsEnabled="{Binding ButtonMultiMoveEnabled, RelativeSource={RelativeSource AncestorType=UserControl}}">
                        <StackPanel Orientation="Horizontal">
                            <Rectangle Width="12">
                                <Rectangle.Fill>
                                    <VisualBrush Visual="{Binding MoveIconVisual, RelativeSource={RelativeSource AncestorType=UserControl}}"></VisualBrush>
                                </Rectangle.Fill>
                            </Rectangle>
                            <TextBlock Text="{lex:LocTextUpper Stats_Constructed_Games_Button_Move}" Margin="4,0,0,0"/>
                        </StackPanel>
                    </Button>
                    <Button Margin="4,4,0,4" MinWidth="120" Click="ButtonMultiDelete_OnClick" Background="Red">
                        <StackPanel Orientation="Horizontal">
                            <Rectangle Width="12">
                                <Rectangle.Fill>
                                    <VisualBrush Visual="{Binding DeleteIconVisual, RelativeSource={RelativeSource AncestorType=UserControl}}"></VisualBrush>
                                </Rectangle.Fill>
                            </Rectangle>
                            <TextBlock Text="{lex:LocTextUpper Stats_Constructed_Games_Button_Delete}" Margin="4,0,0,0"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </StackPanel>
            <DataGrid Name="DataGridGames" SelectedIndex="0" SelectedItem="{Binding SelectedGame, RelativeSource={RelativeSource AncestorType=UserControl}, Mode=OneWayToSource, IsAsync=True}" 
                      ItemsSource="{Binding Path=FilteredGames, Source={x:Static compiledStats:ConstructedStats.Instance}, IsAsync=True, NotifyOnTargetUpdated=True}" 
                      RowDetailsVisibilityMode="{Binding RowDetailVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                      SelectionChanged="DataGridGames_OnSelectionChanged" TargetUpdated="DataGridGames_OnTargetUpdated" 
                      AutoGenerateColumns="False" CanUserAddRows="False" CanUserDeleteRows="False" CanUserResizeRows="False" IsReadOnly="True" CanUserResizeColumns="True">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="{lex:Loc Stats_Constructed_Games_Table_Deck}" Binding="{Binding DeckNameAndVersion}" Width="75">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                <Setter Property="HorizontalAlignment" Value="Center" />
                                <Setter Property="ToolTip" Value="{Binding DeckNameAndVersion}"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTemplateColumn Header="{lex:Loc Stats_Constructed_Games_Table_Class}" SortMemberPath="PlayerHero">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Image Source="{Binding PlayerHeroImage}" Width="30" Height="30" RenderOptions.BitmapScalingMode="Fant" ToolTip="{Binding PlayerHero}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="{lex:Loc Stats_Constructed_Games_Table_Vs}" SortMemberPath="OpponentHero">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Image Source="{Binding OpponentHeroImage}" Width="30" Height="30" RenderOptions.BitmapScalingMode="Fant" ToolTip="{Binding OpponentHero}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTextColumn Header="{lex:Loc Stats_Constructed_Games_Table_Name}" Binding="{Binding OpponentName}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="{lex:Loc Stats_Constructed_Games_Table_Result}" Binding="{Binding Result}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center" />
                                <Setter Property="Foreground" Value="{Binding ResultTextColor}"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="{lex:Loc Stats_Constructed_Games_Table_Mode}" Binding="{Binding GameMode}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="{lex:Loc Stats_Constructed_Games_Table_Format}" Binding="{Binding Format}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="{lex:Loc Stats_Constructed_Games_Table_Rank}" Binding="{Binding RankString}" SortMemberPath="SortableRank">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="{lex:Loc Stats_Constructed_Games_Table_Turns}" Binding="{Binding Turns}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="{lex:Loc Stats_Constructed_Games_Table_Coin}" Binding="{Binding GotCoin}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="{lex:Loc Stats_Constructed_Games_Table_Started}" Binding="{Binding StartTime, StringFormat={}{0:dd MMM yyyy HH:mm}}" SortMemberPath="StartTime">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="{lex:Loc Stats_Constructed_Games_Table_Duration}" Binding="{Binding Duration}" SortMemberPath="SortableDuration">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="{lex:Loc Stats_Constructed_Games_Table_Region}" Binding="{Binding Region}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="{lex:Loc Stats_Constructed_Games_Table_Replay}" Binding="{Binding ReplayState}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="{lex:Loc Stats_Constructed_Games_Table_Note}" Binding="{Binding Note}" />
                </DataGrid.Columns>
                <DataGrid.RowDetailsTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal" Margin="10,0,0,0">
                            <Button Margin="4,4,0,4" MinWidth="70"  Click="ButtonShowReplay_OnClick" Background="Green" IsEnabled="{Binding HasReplayFile}">
                                <StackPanel Orientation="Horizontal">
                                    <Rectangle Width="12">
                                        <Rectangle.Fill>
                                            <VisualBrush Visual="{Binding ReplayIconVisual, RelativeSource={RelativeSource AncestorType=UserControl}}"></VisualBrush>
                                        </Rectangle.Fill>
                                    </Rectangle>
                                    <TextBlock Text="{lex:LocTextUpper Stats_Constructed_Games_Table_Button_Replay}" Margin="4,0,0,0"/>
                                </StackPanel>
                            </Button>
                            <Button Margin="4,4,0,4" MinWidth="70" Click="ButtonShowOppDeck_OnClick" IsEnabled="{Binding Path=CanGetOpponentDeck}">
                                <StackPanel Orientation="Horizontal">
                                    <Rectangle Width="12">
                                        <Rectangle.Fill>
                                            <VisualBrush Visual="{Binding OppDeckIconVisual, RelativeSource={RelativeSource AncestorType=UserControl}}"></VisualBrush>
                                        </Rectangle.Fill>
                                    </Rectangle>
                                    <TextBlock Text="{lex:LocTextUpper Stats_Constructed_Games_Table_Button_OpponentDeck}" Margin="4,0,0,0"/>
                                </StackPanel>
                            </Button>
                            <Button Margin="4,4,0,4" MinWidth="70" Click="ButtonEdit_OnClick">
                                <StackPanel Orientation="Horizontal">
                                    <Rectangle Width="12">
                                        <Rectangle.Fill>
                                            <VisualBrush Visual="{Binding EditIconVisual, RelativeSource={RelativeSource AncestorType=UserControl}}"></VisualBrush>
                                        </Rectangle.Fill>
                                    </Rectangle>
                                    <TextBlock Text="{lex:LocTextUpper Stats_Constructed_Games_Table_Button_Edit}" Margin="4,0,0,0"/>
                                </StackPanel>
                            </Button>
                            <Button Margin="4,4,0,4" MinWidth="70" Click="ButtonEditNote_OnClick">
                                <StackPanel Orientation="Horizontal">
                                    <Rectangle Width="12">
                                        <Rectangle.Fill>
                                            <VisualBrush Visual="{Binding NoteIconVisual, RelativeSource={RelativeSource AncestorType=UserControl}}"></VisualBrush>
                                        </Rectangle.Fill>
                                    </Rectangle>
                                    <TextBlock Text="{lex:LocTextUpper Stats_Constructed_Games_Table_Button_Note}" Margin="4,0,0,0"/>
                                </StackPanel>
                            </Button>
                            <Button Margin="4,4,0,4" MinWidth="70" Click="ButtonMove_OnClick">
                                <StackPanel Orientation="Horizontal">
                                    <Rectangle Width="12">
                                        <Rectangle.Fill>
                                            <VisualBrush Visual="{Binding MoveIconVisual, RelativeSource={RelativeSource AncestorType=UserControl}}"></VisualBrush>
                                        </Rectangle.Fill>
                                    </Rectangle>
                                    <TextBlock Text="{lex:LocTextUpper Stats_Constructed_Games_Table_Button_Move}" Margin="4,0,0,0"/>
                                </StackPanel>
                            </Button>
                            <Button Margin="4,4,0,4" MinWidth="70" Click="ButtonDelete_OnClick" Background="Red">
                                <StackPanel Orientation="Horizontal">
                                    <Rectangle Width="12">
                                        <Rectangle.Fill>
                                            <VisualBrush Visual="{Binding DeleteIconVisual, RelativeSource={RelativeSource AncestorType=UserControl}}"></VisualBrush>
                                        </Rectangle.Fill>
                                    </Rectangle>
                                    <TextBlock Text="{lex:LocTextUpper Stats_Constructed_Games_Table_Button_Delete}" Margin="4,0,0,0"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </DataTemplate>
                </DataGrid.RowDetailsTemplate>
            </DataGrid>
        </DockPanel>
        </GroupBox>
</UserControl>
