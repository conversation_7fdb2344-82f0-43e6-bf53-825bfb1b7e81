﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Stats.ArenaFilters"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:converters="clr-namespace:Hearthstone_Deck_Tracker.Controls.Stats.Converters"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="300">
    <UserControl.Resources>
        <converters:CustomTimeframeVisibilityConverter x:Key="CustomTimeframeVisibilityConverter" />
        <converters:CustomTimeframeSeasonVisibilityConverter x:Key="CustomTimeframeSeasonVisibilityConverter" />
    </UserControl.Resources>
    <StackPanel>
        <DockPanel Margin="0,5,0,0">
            <Label Content="{lex:Loc Stats_Arena_Filters_Label_Time}" Width="60"/>
            <ComboBox Name="ComboBoxTimeframe" SelectedIndex="3" SelectionChanged="ComboBoxTimeframe_OnSelectionChanged">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding Path=., Converter={StaticResource EnumDescriptionConverter}}" />
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>
        </DockPanel>
        <DockPanel Margin="0,5,0,0" Visibility="{Binding ElementName=ComboBoxTimeframe, Path=SelectedItem, Converter={StaticResource CustomTimeframeVisibilityConverter}}">
            <Label Content="{lex:Loc Stats_Arena_Filters_Label_Start}" Margin="10,0,0,0" Width="50"/>
            <DatePicker Name="DatePickerCustomTimeFrameStart" DockPanel.Dock="Right"
                                    SelectedDate="{Binding Path=ArenaStatsTimeFrameCustomStart, Source={StaticResource ConfigWrapper}}"
                                    SelectedDateChanged="DatePickerCustomTimeFrame_OnSelectedDateChanged" />
        </DockPanel>
        <DockPanel Margin="0,5,0,0" Visibility="{Binding ElementName=ComboBoxTimeframe, Path=SelectedItem, Converter={StaticResource CustomTimeframeVisibilityConverter}}">
            <Label Content="{lex:Loc Stats_Arena_Filters_Label_End}" Margin="10,0,0,0" Width="50"/>
            <DatePicker Name="DatePickerCustomTimeFrameEnd" DockPanel.Dock="Right"
                                    SelectedDate="{Binding Path=ArenaStatsTimeFrameCustomEnd, Source={StaticResource ConfigWrapper}}"
                                    SelectedDateChanged="DatePickerCustomTimeFrame_OnSelectedDateChanged" />
        </DockPanel>
        <DockPanel Margin="0,5,0,0" Visibility="{Binding ElementName=ComboBoxTimeframe, Path=SelectedItem, Converter={StaticResource CustomTimeframeSeasonVisibilityConverter}}">
            <Label Content="{lex:Loc Stats_Arena_Filters_Label_Season}" Width="60"/>
            <Grid VerticalAlignment="Center">
                <Grid.RowDefinitions>
                    <RowDefinition Height="22*"/>
                    <RowDefinition Height="9*"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <TextBox Name="TextBoxCustomSeasonMin" Height="26" TextAlignment="Center" Grid.Column="0" Grid.Row="0" LostFocus="TextBoxCustomSeasonMin_OnLostFocus"
                         PreviewTextInput="TextBox_OnPreviewTextInput_DigitsOnly" PreviewKeyUp="TextBox_OnEnter">
                    <TextBox.Text>
                        <Binding Path="ArenaStatsCustomSeasonMin" Source="{StaticResource ConfigWrapper}">
                            <Binding.ValidationRules>
                                <ExceptionValidationRule/>
                            </Binding.ValidationRules>
                        </Binding>
                    </TextBox.Text>
                </TextBox>
                <Label Content="to" Grid.Column="1" Grid.Row="0"/>
                <TextBox Name="TextBoxCustomSeasonMax" Height="26" TextAlignment="Center" Grid.Column="2" Grid.Row="0" LostFocus="TextBoxCustomSeasonMax_OnLostFocus"
                         PreviewTextInput="TextBox_OnPreviewTextInput_DigitsOnly" PreviewKeyUp="TextBox_OnEnter">
                    <TextBox.Text>
                        <Binding Path="ArenaStatsCustomSeasonMax" Source="{StaticResource ConfigWrapper}">
                            <Binding.ValidationRules>
                                <ExceptionValidationRule/>
                            </Binding.ValidationRules>
                        </Binding>
                    </TextBox.Text>
                </TextBox>
            </Grid>
        </DockPanel>
        <DockPanel Margin="0,5,0,0">
            <Label Content="{lex:Loc Stats_Arena_Filters_Label_Class}" Width="60"/>
            <ComboBox Name="ComboBoxClass" SelectedIndex="0" SelectionChanged="ComboBoxClass_OnSelectionChanged">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal" Height="24">
                            <Image Source="{Binding ClassImage}" MaxWidth="24" MaxHeight="24" RenderOptions.BitmapScalingMode="Fant" 
                                   Margin="0,0,3,0" Visibility="{Binding ImageVisibility}" />
                            <TextBlock Text="{Binding HeroClass, Converter={StaticResource EnumDescriptionConverter}}" VerticalAlignment="Center" />
                        </StackPanel>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>
        </DockPanel>
        <DockPanel Margin="0,5,0,0">
            <Label Content="{lex:Loc Stats_Arena_Filters_Label_Region}" Width="60"/>
            <ComboBox Name="ComboBoxRegion" SelectedIndex="0" SelectionChanged="ComboBoxRegion_OnSelectionChanged">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding Path=., Converter={StaticResource EnumDescriptionConverter}}" />
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>
        </DockPanel>
        <CheckBox Name="CheckBoxArchived" Content="{lex:Loc Stats_Arena_Filters_CheckBox_Archive}" Margin="5,5,0,0" 
                  IsChecked="{Binding Path=ArenaStatsIncludeArchived, Source={StaticResource ConfigWrapper}}"
                  Checked="CheckBoxArchived_OnChecked" Unchecked="CheckBoxArchived_OnUnchecked" />
    </StackPanel>
</UserControl>
