﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Stats.Arena.Charts.ChartWinsLossVsClass"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:metroChart="clr-namespace:De.TorstenMandelkow.MetroChart;assembly=De.TorstenMandelkow.MetroChart"
             xmlns:stats="clr-namespace:Hearthstone_Deck_Tracker.Stats"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="300">
    <metroChart:StackedColumnChart Name="StackedColumnChartWinsByClass" Background="Transparent" ChartTitleVisibility="Collapsed" ChartLegendVisibility="Collapsed" SelectedBrush="Transparent" SeriesSource="{Binding Path=SeriesSourceWins, RelativeSource={RelativeSource AncestorType=UserControl}}">
        <metroChart:StackedColumnChart.SeriesTemplate>
            <DataTemplate>
                <metroChart:ChartSeries SeriesTitle="{Binding Index}" DisplayMember="Name" ValueMember="Value" BrushMember="Brush" ItemsSource="{Binding ItemsSource}" />
            </DataTemplate>
        </metroChart:StackedColumnChart.SeriesTemplate>
    </metroChart:StackedColumnChart>
</UserControl>
