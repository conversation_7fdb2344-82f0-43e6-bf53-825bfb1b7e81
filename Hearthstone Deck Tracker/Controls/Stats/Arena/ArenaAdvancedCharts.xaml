﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Stats.Arena.ArenaAdvancedCharts"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:charts="clr-namespace:Hearthstone_Deck_Tracker.Controls.Stats.Arena.Charts"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d"
             d:DesignHeight="1000" d:DesignWidth="800">
    <DockPanel>
        <ScrollViewer>
            <StackPanel>
                <GroupBox Header="{lex:LocTextUpper Stats_Arena_Advanced_Label_Note}" MaxWidth="700">
                    <StackPanel Orientation="Horizontal">
                        <Button Style="{DynamicResource MetroCircleButtonStyle}" Width="36" Height="36" Margin="-5" Click="Button_Click">
                            <Rectangle Width="14" Height="16">
                                <Rectangle.Fill>
                                    <VisualBrush Visual="{DynamicResource appbar_refresh}" />
                                </Rectangle.Fill>
                            </Rectangle>
                        </Button>
                        <Label Content="{lex:Loc Stats_Arena_Advanced_Warning}"/>
                    </StackPanel>
                </GroupBox>
                <GroupBox Header="{lex:LocTextUpper Stats_Arena_Advanced_Label_Distribution}" Height="400" MaxWidth="700">
                    <charts:ChartWinsByClass/>
                </GroupBox>
                <GroupBox Header="{lex:LocTextUpper Stats_Arena_Advanced_Label_WinLoss}" Height="400" MaxWidth="700">
                    <charts:ChartWinsLossVsClass/>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
    </DockPanel>
</UserControl>
