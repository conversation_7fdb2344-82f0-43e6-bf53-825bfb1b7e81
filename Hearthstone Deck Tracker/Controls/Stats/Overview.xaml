<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.Stats.Overview"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:lex="http://wpflocalizeextension.codeplex.com"
             lex:LocalizeDictionary.DesignCulture="en"
             lex:ResxLocalizationProvider.DefaultAssembly="HearthstoneDeckTracker"
             lex:ResxLocalizationProvider.DefaultDictionary="Strings"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="800">
    <DockPanel>
        <DockPanel>
            <GroupBox Header="{lex:LocTextUpper Stats_Overview_Label_Menu}" DockPanel.Dock="Top">
                <TreeView Name="TreeViewStats" Height="200" SelectedItemChanged="TreeViewStats_OnSelectedItemChanged">
                    <TreeViewItem Name="TreeViewItemArenaRuns" IsExpanded="True"
                                  Selected="TreeViewItemArenaRuns_OnSelected" Header="{lex:Loc Stats_Overview_Tree_Label_Arena}"
                                  DataContext="{Binding ArenaStatsSummary, RelativeSource={RelativeSource AncestorType=UserControl}}">
                        <TreeViewItem Name="TreeViewItemArenaRunsSummary"
                                      Selected="TreeViewItemArenaRunsSummary_OnSelected" Header="{lex:Loc Stats_Overview_Tree_Label_Arena_Summary}"
                                      IsSelected="True"
                                      DataContext="{Binding ArenaStatsSummary, RelativeSource={RelativeSource AncestorType=UserControl}}" />
                        <TreeViewItem Name="TreeViewItemArenaRunsOverview"
                                      Selected="TreeViewItemArenaRunsOverview_OnSelected" Header="{lex:Loc Stats_Overview_Tree_Label_Arena_Runs}"
                                      DataContext="{Binding ArenaRuns, RelativeSource={RelativeSource AncestorType=UserControl}}" />
                        <TreeViewItem Name="TreeViewItemArenaRunsAdvanced"
                                      Selected="TreeViewItemArenaRunsAdvanced_OnSelected" Header="{lex:Loc Stats_Overview_Tree_Label_Arena_Advanced}"
                                      DataContext="{Binding ArenaAdvancedCharts, RelativeSource={RelativeSource AncestorType=UserControl}}" />
                    </TreeViewItem>
                    <TreeViewItem Name="TreeViewItemConstructed" Header="{lex:Loc Stats_Overview_Tree_Label_Constructed}" IsExpanded="True"
                                      Selected="TreeViewItemConstructedSummary_OnSelected"
                                      DataContext="{Binding ConstructedSummary, RelativeSource={RelativeSource AncestorType=UserControl}}" >
                        <TreeViewItem Name="TreeViewItemConstructedSummary" 
                                      Selected="TreeViewItemConstructedSummary_OnSelected" Header="{lex:Loc Stats_Overview_Tree_Label_Constructed_Summary}"
                                      DataContext="{Binding ConstructedSummary, RelativeSource={RelativeSource AncestorType=UserControl}}" />
                        <TreeViewItem Name="TreeViewItemConstructedGames" 
                                      Selected="TreeViewItemConstructedGames_OnSelected" Header="{lex:Loc Stats_Overview_Tree_Label_Constructed_Matches}"
                                      DataContext="{Binding ConstructedGames, RelativeSource={RelativeSource AncestorType=UserControl}}" />
                        <TreeViewItem Name="TreeViewItemConstructedCharts" 
                                      Selected="TreeViewItemConstructedCharts_OnSelected" Header="{lex:Loc Stats_Overview_Tree_Label_Constructed_Charts}"
                                      DataContext="{Binding ConstructedCharts, RelativeSource={RelativeSource AncestorType=UserControl}}" />
                    </TreeViewItem>
                </TreeView>
            </GroupBox>
            <GroupBox Width="230">
                <GroupBox.Header>
                    <DockPanel>
                        <Button Name="ButtonMore" Style="{DynamicResource MetroCircleButtonStyle}" Content="..." Foreground="White" 
                                Width="32" Height="32" Margin="5, -9, -7, -9" Click="ButtonMore_OnClick" DockPanel.Dock="Right">
                            <Button.ContextMenu>
                                <ContextMenu Name="ButtonMoreContextMenu">
                                    <CheckBox Name="CheckBoxPercent" DockPanel.Dock="Right" Content="{lex:LocTextUpper Stats_Overview_Filters_CheckBox_AutoRefresh}" Margin="-25,0,5,0" Foreground="{DynamicResource TextBrush}" 
                                  IsChecked="{Binding Path=StatsAutoRefresh, Source={StaticResource ConfigWrapper}}"/>
                                    <MenuItem Header="{lex:LocTextUpper Stats_Overview_Filters_Label_Reset}" Click="MenuItemReset_OnClick"/>
                                </ContextMenu>
                            </Button.ContextMenu>
                        </Button>
                        <Button Style="{DynamicResource MetroCircleButtonStyle}" Width="32" Height="32" Margin="-9" Click="ButtonRefresh_OnClick" DockPanel.Dock="Right">
                            <Rectangle Width="12" Height="14">
                                <Rectangle.Fill>
                                    <VisualBrush Visual="{DynamicResource appbar_refresh_white}" />
                                </Rectangle.Fill>
                            </Rectangle>
                        </Button>
                        <TextBlock Text="{lex:LocTextUpper Stats_Overview_Label_Filters}" Foreground="White"/>
                    </DockPanel>
                </GroupBox.Header>
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                    <ContentControl Name="ContentControlFilter"/>
                </ScrollViewer>
            </GroupBox>
        </DockPanel>
        <ContentControl Name="ContentControlOptions" Margin="-6,0,0,0"
                        Content="{Binding ElementName=TreeViewStats, Path=SelectedItem.DataContext}" />
    </DockPanel>
</UserControl>
