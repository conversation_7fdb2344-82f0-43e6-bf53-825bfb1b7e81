﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.DeckLens"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker.Controls"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <StackPanel Name="Container"  Width="221">
        <Border Background="#23272A" BorderBrush="#141617" BorderThickness="2,2,2,0" CornerRadius="3,3,0,0" Width="Auto" HorizontalAlignment="Left" Panel.ZIndex="1" Margin="2,5,2,0">
            <StackPanel Orientation="Horizontal" Margin="5,2" HorizontalAlignment="Left">
                <Rectangle Fill="White" Width="17" Height="17">
                    <Rectangle.OpacityMask>
                        <VisualBrush Visual="{StaticResource icon_magnifying_glass}" />
                    </Rectangle.OpacityMask>
                </Rectangle>
                <TextBlock Text="{Binding Label, RelativeSource={RelativeSource AncestorType=local:DeckLens}}"
                    FontWeight="SemiBold" Foreground="White" FontSize="14" Margin="5,0,0,0" />
            </StackPanel>
        </Border>
        <Border Background="#23272A" BorderBrush="#141617" BorderThickness="2" Margin="2,-2,2,5" Padding="1">
            <local:AnimatedCardList x:Name="CardList" Width="209"/>
        </Border>
    </StackPanel>
</UserControl>
