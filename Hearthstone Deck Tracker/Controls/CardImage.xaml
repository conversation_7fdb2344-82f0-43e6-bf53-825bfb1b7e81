﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.CardImage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:controls="clr-namespace:Hearthstone_Deck_Tracker.Controls"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             mc:Ignorable="d"
             SizeChanged="UserControl_SizeChanged"
             d:DesignHeight="256" d:DesignWidth="256">
    <UserControl.Resources>
        <Storyboard x:Key="StoryboardExpand">
            <DoubleAnimation Storyboard.TargetName="CardImageGrid" Duration="0:0:0.2" Storyboard.TargetProperty="RenderTransform.(ScaleTransform.ScaleX)" To="1" From="0" />
            <DoubleAnimation Storyboard.TargetName="CardImageGrid" Duration="0:0:0.2" Storyboard.TargetProperty="RenderTransform.(ScaleTransform.ScaleY)" To="1" From="0" />
        </Storyboard>
        <controls:CardImageSizeConverter x:Key="CardImageSizeConverter"/>
    </UserControl.Resources>
    <Grid x:Name="CardImageGrid" RenderTransformOrigin="0.5,0.5"
         MaxWidth="{Binding Path=Bindable.CardImageSize, Source={StaticResource ConfigWrapper}, Converter={StaticResource CardImageSizeConverter}, ConverterParameter={x:Static controls:CardImageSizeType.Width}}"
         MaxHeight="{Binding Path=Bindable.CardImageSize, Source={StaticResource ConfigWrapper}, Converter={StaticResource CardImageSizeConverter}, ConverterParameter={x:Static controls:CardImageSizeType.Height}}">
        <Grid.RenderTransform>
            <ScaleTransform/>
        </Grid.RenderTransform>
        <hearthstoneDeckTracker:HearthstoneTextBlock x:Name="CreatedByInfo" HorizontalAlignment="Center" VerticalAlignment="Top" Visibility="{Binding CreatedByVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}" Text="{Binding CreatedByText, RelativeSource={RelativeSource AncestorType=UserControl}}" d:Text="Created By" FontSize="16"/>
        <Image HorizontalAlignment="Center" Margin="0 12 0 0"
              MaxWidth="{Binding Path=Bindable.CardImageSize, Source={StaticResource ConfigWrapper}, Converter={StaticResource CardImageSizeConverter}, ConverterParameter={x:Static controls:CardImageSizeType.Width}}"
              MaxHeight="{Binding Path=Bindable.CardImageSize, Source={StaticResource ConfigWrapper}, Converter={StaticResource CardImageSizeConverter}, ConverterParameter={x:Static controls:CardImageSizeType.Height}}"
              Source="{Binding LoadingImageSource, TargetNullValue={x:Null}, RelativeSource={RelativeSource AncestorType=UserControl}}"
              d:Source="{StaticResource LoadingMinion}"
              RenderOptions.BitmapScalingMode="Fant">
        </Image>
        <Image x:Name="CardImageDisplay" HorizontalAlignment="Center" Margin="0 12 0 0"
              MaxWidth="{Binding Path=Bindable.CardImageSize, Source={StaticResource ConfigWrapper}, Converter={StaticResource CardImageSizeConverter}, ConverterParameter={x:Static controls:CardImageSizeType.Width}}"
              MaxHeight="{Binding Path=Bindable.CardImageSize, Source={StaticResource ConfigWrapper}, Converter={StaticResource CardImageSizeConverter}, ConverterParameter={x:Static controls:CardImageSizeType.Height}}"
              Source="{Binding CardAsset, TargetNullValue={x:Null}, RelativeSource={RelativeSource AncestorType=UserControl}}" RenderOptions.BitmapScalingMode="Fant">
        </Image>
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="12*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="0.1*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="6.6*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="4*"/>
            </Grid.RowDefinitions>
            <Canvas Grid.Column="1" Grid.Row="1">
                <Canvas.RenderTransform>
                    <ScaleTransform ScaleX="{Binding IconScaling, RelativeSource={RelativeSource AncestorType=UserControl}}" ScaleY="{Binding IconScaling, RelativeSource={RelativeSource AncestorType=UserControl}}"/>
                </Canvas.RenderTransform>
                <Grid ClipToBounds="False" Canvas.Top="-20" Canvas.Left="-20" Visibility="{Binding QuestionmarkVisibility, RelativeSource={RelativeSource AncestorType=UserControl}}" d:Visibility="Collapsed">
                    <Ellipse Width="40" Height="40" Fill="#141617" />
                    <Ellipse Width="36" Height="36" Fill="#23272A" />
                    <hearthstoneDeckTracker:HearthstoneTextBlock x:Name="UnknownIndicator" HorizontalAlignment="Center" VerticalAlignment="Center" Text="?" FontSize="28" />
                </Grid>
            </Canvas>
        </Grid>
    </Grid>
</UserControl>
