﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.DeckView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Hearthstone_Deck_Tracker"
             xmlns:controls1="clr-namespace:Hearthstone_Deck_Tracker.Controls"
             xmlns:deckSetIcons="clr-namespace:Hearthstone_Deck_Tracker.Controls.DeckSetIcons"
             mc:Ignorable="d"
             d:DesignHeight="300" d:DesignWidth="300">
    <UserControl.Resources>
        <SolidColorBrush x:Key="StandardColor" Color="White"></SolidColorBrush>
        <LinearGradientBrush x:Key="WildColorGrad" StartPoint="0,0" EndPoint="1,1" >
            <GradientStop Color="#FF0D8D0D" Offset="0.25" />
            <GradientStop Color="#FFC7C715" Offset="0.5" />
            <GradientStop Color="#FF0D8D0D" Offset="0.75" />
        </LinearGradientBrush>
        <LinearGradientBrush x:Key="StandardColorGrad" StartPoint="0,0" EndPoint="1,0" >
            <GradientStop Color="#FF8F59DC" Offset="0.1" />
            <GradientStop Color="#FF3617C9" Offset="0.5" />
            <GradientStop Color="#FF8F59DC" Offset="0.9" />
        </LinearGradientBrush>
        <Canvas x:Key="mode_standard" Clip="M0,0 V128 H128 V0 H0 Z">
            <Path Fill="{DynamicResource StandardColor}" Data="M22.2,44.5c0.2,3.2-0.5,6.4,0.8,9.5c2.9,7.2,4.5,14.7,6.4,22.1c0.8,3.2,2.4,5.6,4.8,7.7c2.9,2.4,8.7,2.5,12,0.2c2.3-1.7,3.3-7.7,1.8-10.9c-2.2-4.6-1.7-7.9,2.1-11.2c2.9-2.6,4.2-5.7,4.2-9.5c0-1.4,0-2.8,0-4.9C56,51.4,56,54.6,55.2,58c-0.4,1.7-0.7,3.3,0.3,4.9c4.2,7,4.4,14.4,1.6,21.9c-0.8,2.1-0.7,4.3-0.1,6.5c1.7,6,4.5,11.5,7.3,17c0.7,1.4,1.6,1.6,2.8,0.7c2.1-1.5,4.2-3,6.3-4.6c9.1-7.1,15.7-15.8,18.8-27c0.5-1.8,0.7-3.1-1.6-3.5c-10.6-1.6-18.3-21.3-9.2-31c1.2-1.3,1.2-2.3,0.2-3.7c-2.3-3.2-5.2-5.8-8.6-7.8c-14.2-8-28.5-7.8-42.9-0.5c-1.6,0.8-3.1,1.2-5.1,0.8c-4.6-0.8-9,1.2-13.6,1.8c2.3,2,4.5,4.2,7.1,5.2C21.6,39.9,22,41.9,22.2,44.5z M29.8,46.7c2.6,3.8,2.6,3.9-1.9,5.8C28.1,50.4,28.3,48.4,29.8,46.7z"/>
            <Path Fill="{DynamicResource StandardColor}" Data="M124.8,55.7c-0.1-1.2-0.2-2.4-1.1-3.3c-5-5.5-12.7-7.3-17.7-12.9c-0.4-0.4-1.3-0.3-2-0.6c-2.6-1-4.5-2.7-5.4-5.9c4.8,4.6,6.2,5,7.5,2.5c2.3,4.4,2.8,5,4.5,6.2c1.1,0.7,2.3,1.6,3.6,1.3c1.7-0.5,0-2.3,1-3.4c1.7,4,4,7.3,8,9.2c0.8-0.9,0.3-1.6,0.2-2.2c-6.2-22.6-21.6-36.4-42.7-44.8c-0.7-0.3-1.6-0.8-2.2,0c-0.7,0.9-0.5,1.9,0,2.8c0.5,1,1.2,1.9,1.8,2.7c3.2,4.4,6.5,8.6,9.5,13.1c3.7,5.7,4.1,12,2.6,18.4c-0.9,3.8-2.3,4.6-6,3.8c-1.6-0.3-2.8,0-3.6,1.4c-3.3,5.1-3.9,10.6-1.7,16.3c2.3,5.7,6,9.9,12.6,10.4c2.1,0.2,2.4,1,2,2.9c-1.6,8-4.6,15.3-9.3,21.9c-10.1,14.2-24.3,21.8-41,25.3c-2.2,0.5-4.6,0.4-7,1.6c3,1.7,5.9,2.5,9.1,2c5.9-0.9,11.4-2.9,16.9-5c16.5-6.1,29.2-16.6,37.6-32.1c0.2-0.3,0.3-1,1.4-0.5c-10.2,21.4-27.3,33.8-49.9,39.4c24.6,0.5,41-12.7,54.3-32.1c-6.4,13.8-17.4,22.7-30,30c1,0.1,1.8-0.1,2.5-0.3c18.8-6.9,32.4-19.2,40.4-37.7c1.2-2.9,1.2-5-1.6-6.8c-1.8-1.2-3-3.2-4.2-5c-0.6-0.9-1.3-1.8-2.3-2.3c-5.5-2.7-8.4-8.1-12.3-12.3c-1.4-1.5-1.7-3.7-1.8-5.9c3.5,6.9,4.9,8.1,6.5,5.9c1,2.9,2.3,5.6,4.7,7.6c0.8,0.7,1.7,1.8,2.8,1.4c1.2-0.4,0.2-1.9,1-3c0.8,4.7,3.1,8.3,6.9,11c2.6,1.8,2.9,1.8,3.6-1.2C125.3,69.1,125.1,62.4,124.8,55.7z"/>
            <Path Fill="{DynamicResource StandardColor}" Data="M29.5,29.7c13.2-7.2,26.8-7.9,40.6-2.2c5.4,2.2,10,5.7,13.3,10.6c1-0.7,0.8-1.6,1-2.3c0.5-2.2-0.7-4.2-0.9-6.3c1.7-0.7,2.4,1.5,4.1,1.2c-0.2-2.8-1.4-5-3.3-6.8c-1.9-1.9-4.5-2.4-7-3c1.4-2.1,3.2-3.9,2.3-6.8c-4.2,2.8-4.4,2.6-9.2,0.9c-1.9-0.7-2.3-2.6-4.1-3c-4.4-1-8.7-0.9-13,2.1c1.2-2.2,3.2-2.4,4.4-3.7c-0.1-0.2-0.1-0.4-0.2-0.4c-0.3-0.1-0.5-0.3-0.8-0.3c-5.5-1.2-10.2,0.6-14.6,3.8c-1.5,1.1-2.3,1.3-2.8-0.8c-0.4-1.8-1.3-3.3-3.4-4.2c0.6,5.3-2.1,8.9-5.5,12.2c-2,2-3.2,4.3-3.6,7c-0.2,1-0.7,2.2,0.2,2.8C27.8,31.2,28.7,30.1,29.5,29.7z"/>
            <Path Fill="{DynamicResource StandardColor}" Data="M43.3,118.8c0.2-1.2-1.3-1.2-2.2-1.6c-13-5.2-24.4-12.7-33.6-23.4c-1.4-1.6-2.7-3.1-4.5-5c-0.2,1.7,0.4,2.4,0.8,3.2c5.7,10.8,13.7,19.5,24.2,25.9c4.2,2.6,8.1,3.6,12.6,2C41.6,119.6,43.1,119.9,43.3,118.8z"/>
            <Path Fill="{DynamicResource StandardColor}" Data="M32.4,99.7c-0.2,1.5,0.4,2.1,0.7,2.8c2.6,4.8,6.2,8.8,10.1,12.5c3.7,3.5,5,3.8,9.7,1.6c1.3-0.6,1.7-1.1,0.3-2C46,110.4,39.1,105.6,32.4,99.7z"/>
            <Path Fill="{DynamicResource StandardColor}" Data="M60.2,112.9c0-0.3-0.2-0.8-0.4-1c-3.8-2.4-7-5.6-10.3-8.8c0.7,4.4,2.8,8,5.4,11.3C55.6,115.4,60,114.1,60.2,112.9z"/>
        </Canvas>
        <Canvas x:Key="mode_wild" Clip="M 72,72 L 200,72 200,200 72,200 72,72 Z">
            <Path Fill="{DynamicResource StandardColor}" Data="F 1 M 111.79,90.899 C 110.478,95.709 114.195,102.923 114.195,102.923 108.948,105.328 93.864,96.583 93.864,96.583 77.731,96.206 66.604,83.418 66.604,83.418 L 82.38,68.969 C 84.169,71.486 90.802,77.563 95.612,77.563 100.422,77.563 105.45,74.94 106.324,67.507 107.198,60.074 101.218,56.108 99.25,56.108 97.282,56.108 93.598,55.832 86.43,62.042 78.937,68.532 60.196,85.871 60.196,85.871 60.196,97.676 47.079,101.393 47.079,101.393 47.298,98.551 45.767,94.834 45.767,94.834 39.646,97.457 28.059,99.644 17.347,92.211 6.635,84.778 7.291,69.912 7.291,69.912 L 3.575,63.572 9.696,58.981 C 11.008,52.641 16.91,46.957 16.91,46.957 23.031,25.97 36.148,24.658 36.148,24.658 35.711,33.403 39.427,37.556 39.427,37.556 58.009,37.119 68.74,53.075 68.74,53.075 L 52.475,67.228 C 47.447,61.325 38.233,57.088 34.739,56.936 28.398,56.66 24.644,61.412 24.538,69.066 24.451,75.328 30.09,78.22 32.947,78.026 37.013,77.75 37.634,69.617 37.634,69.617 37.634,69.617 41.877,72.251 41.907,77.061 41.92,79.225 46.205,75.815 46.205,75.815 L 72.22,52.86 69.815,33.84 89.272,39.961 C 89.272,39.961 101.753,33.556 114.194,47.175 117.421,48.234 120.581,44.02 120.581,44.02 122.688,48.498 119.44,55.046 119.44,55.046 119.44,55.046 134.714,75.882 111.79,90.899 Z" RenderTransform="1,0,0,1,72,72"/>
        </Canvas>
        <Canvas x:Key="mode_arena"  Clip="M 72,72 L 721,72 721,1050 72,1050 72,72 Z">
            <Path Fill="{StaticResource StandardColor}" Data="M 17.537,117.564 L 17.497,135 494.441,135 494.441,117.564 17.538,117.564 Z M 17.46,153 L 17.354,200 24,200 C 24,182.327 38.3269,168 56,168 73.6731,168 88,182.327 88,200 L 104.033,200 C 104.02,199.775 104.009,199.549 104,199.324 104,181.651 118.327,167.324 136,167.324 153.673,167.324 168,181.651 168,199.324 167.995,199.549 167.987,199.775 167.977,200 L 183.76,200 C 183.76,182.327 198.087,168 215.76,168 233.433,168 247.76,182.327 247.76,200 L 263.793,200 C 263.78,199.775 263.769,199.55 263.76,199.324 263.76,181.651 278.087,167.324 295.76,167.324 313.433,167.324 327.76,181.651 327.76,199.324 327.754,199.549 327.746,199.775 327.736,200 L 344.066,200 C 344.035,199.559 344.013,199.118 344,198.676 344,181.003 358.327,166.676 376,166.676 393.673,166.676 408,181.003 408,198.676 407.993,199.118 407.978,199.559 407.953,200 L 424.1,200 C 424.046,199.335 424.012,198.668 424,198 424,180.327 438.327,166 456,166 473.673,166 488,180.327 488,198 487.997,198.667 487.974,199.334 487.93,200 L 494.44,200 494.44,153 17.46,153 Z M 15.324,216 L 15.524,312 24.194,312 C 25.8361,271.153 39.5789,240.043 56,240 72.4341,239.999 86.1966,271.121 87.84,312 L 104.193,312 C 105.835,271.152 119.579,240.042 136,240 152.434,239.999 166.197,271.121 167.84,312 L 184.193,312 C 185.835,271.152 199.579,240.042 216,240 232.434,239.999 246.197,271.121 247.84,312 L 264.193,312 C 265.835,271.152 279.579,240.042 296,240 312.434,239.999 326.197,271.121 327.84,312 L 344.193,312 C 345.835,271.152 359.579,240.042 376,240 392.434,239.999 406.197,271.121 407.84,312 L 424.193,312 C 425.835,271.152 439.579,240.042 456,240 472.434,239.999 486.197,271.121 487.84,312 L 495.764,312 496.162,216 15.324,216 Z M 14.598,328 L 15.275,488 41.05,488 C 41.6551,409.497 69.6563,346.572 104,346.537 138.338,346.592 166.329,409.512 166.934,488 L 193.137,488 C 194.994,411.722 222.583,352.034 256,352 289.398,352.094 316.958,411.766 318.813,488 L 345.137,488 C 346.993,411.722 374.583,352.034 408,352 441.398,352.094 468.958,411.766 470.813,488 L 495.518,488 496.871,328 14.6,328 Z" RenderTransform="1.26749,0,0,1.26749,72.0004,236.64"/>
        </Canvas>
        <Canvas x:Key="mode_brawl" Clip="M 72,72 L 721,72 721,1050 72,1050 72,72 Z">
            <Path Fill="{StaticResource StandardColor}" Data="F 1 M 384.47,9.938 L 320.436,26.03 181.03,165.438 C 134.732,211.738 182.2,253.425 132.78,302.845 L 51.187,384.439 C 49.957,384.385 48.719,384.381 47.469,384.469 41.533,384.889 33.897,387.353 27.529,393.719 21.164,400.086 19.106,407.316 18.686,413.251 18.268,419.186 19.556,424.808 21.593,430.721 25.669,442.544 33.835,455.151 45.311,466.626 56.788,478.102 69.361,486.268 81.186,490.346 87.098,492.383 92.751,493.67 98.686,493.251 104.622,492.831 111.601,490.524 117.968,484.157 124.333,477.791 127.016,470.405 127.435,464.47 127.705,460.653 127.288,456.94 126.405,453.22 L 204.779,374.844 C 254.199,325.424 295.887,372.894 342.186,326.594 L 503.124,165.656 396.124,177.812 403.904,76.219 327.47,104 C 330.876,69.28 350.743,39.927 384.47,9.937 Z M 385.687,228.844 L 399.125,242.281 333.781,307.595 C 308.465,332.912 282.181,333.155 259.907,333.251 237.633,333.349 219.087,333.176 196.407,355.845 L 118.782,434.345 C 115.788,429.045 112.077,423.717 107.689,418.532 L 182.971,342.408 C 209.711,315.668 237.411,314.412 259.907,314.314 282.402,314.217 299.364,315.171 320.345,294.189 L 385.688,228.846 Z M 40.907,404.344 C 43.745,404.31 47.943,404.786 52.187,406.25 61.889,409.595 73.61,417.11 84.219,427.72 94.827,438.328 102.342,450.048 105.686,459.75 107.359,464.6 107.476,469.1 107.28,471.875 107,473.292 107.046,473.302 105.687,473.5 102.913,473.696 98.787,473.173 93.937,471.5 84.241,468.157 72.542,460.6 61.937,450 51.329,439.39 43.782,427.67 40.437,417.97 38.765,413.118 37.991,408.775 38.187,406 38.225,404.45 38.207,404.453 39.781,404.375 40.129,404.35 40.501,404.349 40.907,404.345 Z" RenderTransform="1.26749,0,0,1.26749,72.0004,236.64"/>
        </Canvas>
        <Canvas x:Key="mode_adventure" Clip="M 72,72 L 721,72 721,1050 72,1050 72,72 Z">
            <Path Fill="{StaticResource StandardColor}" Data="F 1 M 242.563,27.656 C 232.501,27.689 222.437,27.861 212.375,28.156 L 218.469,93.75 157.374,31.187 C 126.118,33.757 94.88,37.842 63.624,43.719 L 97.874,72.124 49.811,71.561 C 84.969,171.729 56.747,254.136 49.499,363.561 L 145.249,469.405 C 155.249,468.079 165.105,466.982 174.812,466.061 L 196.342,423.905 214.874,452.749 256.374,419.155 257.187,462.249 C 340.13,462.007 414.921,471.347 492.874,470.062 464.724,397.052 461.744,326.5 460.999,260.905 L 427.749,241.78 460.437,207.906 C 460.007,186.573 459.095,165.9 456.812,145.969 L 399.374,146.687 449.624,105.03 C 445.781,89.553 440.572,74.63 433.499,60.312 L 403.969,36.406 C 350.169,30.918 296.362,27.482 242.561,27.656 Z M 301.281,64.626 L 308.376,81.25 332.97,138.875 347.062,119.125 355.406,107.437 362.719,119.813 403.374,188.937 387.249,198.407 353.874,141.719 311.436,201.124 296.249,190.249 320.249,156.655 298.279,105.249 273.719,145.312 285.686,167.937 269.156,176.687 250.906,142.187 226.061,200.374 208.874,193.03 241.404,116.844 249.094,98.814 258.249,116.124 263.562,126.156 291.812,80.031 301.28,64.626 Z M 106.439,120.876 L 106.532,120.876 106.658,120.906 106.752,120.936 111.532,122.062 111.627,122.094 111.752,122.124 116.282,123.314 116.408,123.344 116.533,123.374 120.815,124.624 120.941,124.656 121.065,124.686 125.159,126 125.284,126.03 125.41,126.094 125.63,126.156 119.785,142.686 119.442,143.781 119.412,143.781 119.192,143.721 115.63,142.564 115.38,142.501 111.567,141.376 111.317,141.313 107.255,140.251 107.035,140.188 102.692,139.188 102.599,139.158 102.474,139.126 103.629,134.22 106.442,120.875 Z M 144.313,134.25 L 146.97,135.78 147.156,135.875 147.312,136 149.969,137.656 150.124,137.781 150.312,137.907 152.749,139.595 152.937,139.72 153.124,139.875 155.404,141.625 155.594,141.75 155.749,141.906 157.874,143.686 158.03,143.844 158.22,144 160.156,145.844 160.344,146 160.5,146.188 161.344,147.063 147.814,159.938 147.344,159.438 146.969,159.094 145.749,157.938 145.655,157.844 145.405,157.624 144,156.44 143.844,156.315 143.656,156.158 142.094,154.938 141.906,154.814 141.75,154.689 139.97,153.471 139.656,153.281 139.626,153.251 137.344,151.813 135,150.47 144.313,134.25 Z M 173,166.625 L 173.25,167.469 173.313,167.719 173.406,167.969 173.906,170.029 173.969,170.249 173.999,170.499 174.405,172.562 174.435,172.812 174.499,173.062 174.779,175.124 174.811,175.344 174.811,175.594 174.999,177.654 175.029,177.874 175.029,178.094 175.124,180.154 175.124,182.874 175.094,183.062 174.999,185.124 174.999,185.344 174.969,185.53 174.779,187.594 174.779,187.75 174.749,187.938 174.593,189.094 156.063,186.688 156.187,185.906 156.217,185.562 156.343,184.219 156.343,184.155 156.373,183.843 156.437,182.155 156.437,180.468 156.374,179.278 156.249,177.685 155.969,175.623 155.655,174.06 155.28,172.654 155.25,172.498 155.125,172.154 173,166.624 Z M 151.75,202.345 L 169.03,209.405 167.937,212.062 167.875,212.219 167.812,212.374 166.187,215.999 166.125,216.124 166.062,216.249 164.375,219.719 164.345,219.812 164.281,219.905 162.595,223.249 162.532,223.312 162.502,223.374 160.814,226.53 160.784,226.594 160.752,226.656 160.439,227.219 144.096,218.155 144.346,217.718 144.408,217.592 145.938,214.717 146.033,214.56 147.565,211.59 147.659,211.372 149.129,208.342 149.253,208.092 150.659,204.997 150.784,204.684 151.754,202.341 Z M 135.28,236.438 L 152.97,242.468 152.813,242.875 152.781,242.938 151.751,246.438 150.845,249.875 150.815,250.095 150.751,250.281 150.095,253.595 149.657,256.282 149.627,256.502 149.595,256.752 149.407,258.658 130.813,256.658 131.095,254.283 131.095,254.033 131.125,253.815 131.72,250.222 131.75,250.002 131.78,249.814 132.562,246.096 132.594,245.876 132.656,245.69 133.626,241.846 133.656,241.658 133.719,241.471 134.905,237.531 134.968,237.345 135.028,237.157 135.278,236.437 Z M 343.125,253.813 L 344,253.906 344.22,253.906 344.406,253.936 346.312,254.186 346.532,254.219 346.719,254.249 348.593,254.593 348.78,254.623 349,254.655 350.813,255.061 351.031,255.124 351.221,255.187 353.001,255.655 353.221,255.718 353.407,255.778 355.127,256.342 355.345,256.404 355.565,256.467 357.22,257.091 357.437,257.185 357.625,257.278 359.25,257.968 359.47,258.06 359.656,258.154 361.25,258.904 361.438,259.029 361.656,259.124 363.186,259.968 363.376,260.06 363.563,260.185 364.781,260.935 355.031,276.873 353.907,276.185 353.813,276.123 353.063,275.717 352.657,275.527 351.907,275.122 351.72,275.059 351.5,274.934 350.25,274.434 349.406,274.122 348.126,273.684 347.844,273.622 347.688,273.559 346.75,273.309 346.406,273.247 346.344,273.217 345.344,272.997 343.874,272.717 343.656,272.684 343.469,272.654 342.029,272.466 341.969,272.466 341.469,272.433 343.124,253.808 Z M 321.905,255.313 L 324.281,267.188 325.751,273.625 325.563,273.655 323.563,274.095 323.407,274.125 323.313,274.155 321.313,274.688 321.095,274.75 319.001,275.344 318.781,275.406 316.626,276.094 316.406,276.156 314.189,276.906 313.969,277 311.657,277.844 311.47,277.906 309.312,278.75 302.5,261.344 304.844,260.438 304.938,260.375 305.063,260.345 307.75,259.375 307.844,259.312 307.969,259.282 310.593,258.406 310.718,258.344 310.81,258.314 313.404,257.5 313.529,257.47 313.624,257.437 316.187,256.719 316.28,256.686 316.406,256.656 318.906,255.999 319.031,255.969 319.157,255.937 321.595,255.405 321.72,255.375 321.844,255.343 321.906,255.313 Z M 284,271.156 L 294.97,286.312 293.25,287.532 293.156,287.625 293.062,287.688 289.906,289.844 289.812,289.906 289.687,289.969 286.469,292.061 286.374,292.124 286.28,292.187 283,294.187 282.906,294.249 282.781,294.312 279.471,296.218 279.376,296.278 279.251,296.342 278.188,296.936 269.251,280.529 270.126,280.029 270.314,279.937 273.251,278.249 273.471,278.124 276.345,276.374 276.532,276.249 279.346,274.405 279.566,274.28 282.502,272.25 284.002,271.156 Z M 150.47,273.876 L 150.594,274.313 150.656,274.593 151.126,275.969 151.186,276.093 152.094,278.28 152.156,278.47 152.75,279.656 152.875,279.876 153.719,281.376 153.843,281.563 154.813,283 154.905,283.125 156,284.53 156.094,284.625 157.374,286.031 143.564,298.626 142.188,297.126 141.968,296.906 141.781,296.656 140.157,294.626 139.97,294.376 139.78,294.096 138.312,291.938 138.125,291.688 137.969,291.408 136.624,289.126 136.468,288.846 136.311,288.563 135.123,286.188 134.998,285.876 134.873,285.596 133.843,283.096 133.717,282.782 133.622,282.472 132.747,279.877 132.653,279.564 132.559,279.252 132.372,278.532 150.466,273.877 Z M 380.78,277.188 L 381.625,278.5 381.688,278.656 381.781,278.781 382.751,280.471 382.814,280.595 382.907,280.72 383.845,282.47 383.907,282.594 383.97,282.75 384.875,284.53 384.938,284.656 385,284.781 385.844,286.657 385.906,286.782 385.969,286.877 386.779,288.815 386.812,288.94 386.874,289.033 387.654,291.003 387.687,291.128 387.749,291.253 388.499,293.283 388.529,293.378 388.562,293.472 389.28,295.564 389.313,295.658 389.343,295.783 389.5,296.283 371.687,301.909 371.594,301.597 371.5,301.377 370.937,299.722 370.875,299.502 370.281,297.908 370.189,297.688 369.595,296.158 369.501,295.938 368.876,294.502 368.782,294.252 368.158,292.877 368.033,292.627 367.378,291.314 367.253,291.064 366.597,289.846 366.441,289.596 365.784,288.44 365.598,288.158 364.942,287.096 380.785,277.19 Z M 253.375,288.094 L 260.345,305.438 257.345,306.656 257.25,306.686 257.156,306.719 253.562,308.061 253.469,308.124 253.343,308.154 249.749,309.404 249.655,309.437 249.53,309.467 245.906,310.655 245.812,310.685 245.719,310.718 242.062,311.81 241.968,311.842 241.873,311.872 241.685,311.904 236.717,293.904 236.903,293.842 240.153,292.872 240.341,292.81 243.561,291.78 243.778,291.717 247.185,290.497 247.185,290.527 250.373,289.31 250.593,289.216 253.373,288.09 Z M 169.625,294.25 L 169.875,294.375 169.938,294.405 172.156,295.281 172.531,295.407 174.501,296.095 174.751,296.157 174.907,296.22 177.001,296.812 177.126,296.844 177.346,296.906 179.906,297.531 182.22,298.031 182.406,298.064 182.562,298.094 185.312,298.532 183.844,307.252 183.75,307.752 182.53,317.032 182.344,317.002 182.188,316.972 179.125,316.439 178.937,316.409 178.781,316.377 175.751,315.752 175.595,315.722 175.407,315.689 172.47,314.971 172.313,314.907 172.125,314.845 169.281,314.032 169.095,313.972 168.875,313.908 166.125,312.971 165.939,312.907 165.719,312.813 163.064,311.783 162.876,311.689 162.656,311.595 162.001,311.315 169.626,294.251 Z M 219.563,297.78 L 222.406,316.25 219.062,316.75 218.937,316.78 218.812,316.78 215.25,317.25 215.125,317.25 215.031,317.28 211.471,317.625 211.345,317.655 211.22,317.655 207.72,317.938 207.469,317.938 203.999,318.125 203.749,318.125 202.593,318.155 202.187,299.469 203.093,299.436 203.343,299.436 206.313,299.28 209.563,299.063 209.563,299.031 212.873,298.721 213.063,298.688 213.123,298.688 216.218,298.282 216.342,298.252 216.436,298.252 219.561,297.782 Z M 394.063,315.78 L 394.438,318.156 394.468,318.281 394.468,318.439 395.188,323.657 395.188,323.782 395.218,323.908 395.781,329.314 395.781,329.533 396.251,335.097 377.626,336.597 377.188,331.253 377.158,331.033 376.658,326.128 376.658,326.065 376.626,325.847 376.001,321.191 375.971,320.909 375.595,318.784 394.065,315.784 Z M 326.5,346.595 C 345.393,350.055 371.413,362.782 395.375,380.47 402.835,370.635 409.8,360.07 416.688,349.375 L 446.75,362.345 C 442.064,374.555 431.76,387.863 418.187,399.531 435.204,415.697 449.057,434.046 455.187,452.471 441.381,434.363 424.117,420.741 404.907,409.657 381.52,425.503 352.225,435.587 325.281,430.095 348.196,425.712 365.925,413.535 381.095,397.501 364.633,389.997 347.297,383.624 329.845,377.407 L 326.501,346.595 Z" RenderTransform="1.26749,0,0,1.26749,72.0004,236.64"/>
        </Canvas>
        <Canvas x:Key="mode_duels" Clip="M 72,72 L 721,72 721,1050 72,1050 72,72 Z">
            <Path Fill="{DynamicResource StandardColor}" Data="F1 M512,512z M0,0z M66.54,18.002C66.213,17.995 65.885,17.997 65.56,18.008 61.496,18.144 57.455,19.642 54.17,22.543 46.662,29.175 45.952,40.637 52.584,48.145 56.978,53.119 63.49,55.09 69.57,53.937L127.408,119.412 77.035,163.91 101.223,191.29C110.913,169.922,123.478,151.806,138.65,136.64L145.56,172.828C170.652,166.538,195.394,162.265,219.926,159.955L196.014,132.885 157.354,120.402C174.471,107.502,194.088,97.432,215.974,89.928L191.784,62.543 141.414,107.039 83.494,41.469C85.284,35.634 84.111,29.039 79.774,24.129 76.276,20.169 71.434,18.099 66.539,18.001z M450.937,18.002C446.042,18.102 441.202,20.17 437.705,24.129 433.367,29.039 432.191,35.635 433.982,41.472L376.062,107.04 325.692,62.543 301.504,89.928C323.388,97.432,343.004,107.501,360.124,120.4L321.464,132.885 298.209,159.209C322.919,161.072,347.576,164.915,372.327,170.669L378.825,136.639C393.998,151.805,406.565,169.921,416.255,191.289L440.44,163.909 390.068,119.411 447.906,53.936C453.986,55.089 460.499,53.118 464.893,48.144 471.523,40.636 470.813,29.174 463.307,22.542 460.022,19.642 455.981,18.142 451.917,18.007 451.591,17.997 451.264,17.994 450.937,18.001z M264.512,176.512C224.952,176.414 185.045,181.738 143.879,192.607 141.833,283.055 178.363,401.957 262.349,452.512 343.644,403.382 384.751,282.61 382.901,192.598 343.151,182.102 303.991,176.61 264.511,176.512z M147.336,330.012L60.47,428.35 48.27,492.244 110.17,472.25 178.66,394.715C165.8,374.607,155.414,352.685,147.336,330.012z M375.539,336.122C366.849,358.36,355.962,379.756,342.833,399.264L407.306,472.25 469.204,492.244 457.004,428.35 375.538,336.12z" />
        </Canvas>
        <Canvas x:Key="mode_classic" Clip="M0,0 V128 H128 V0 H0 Z">
            <Path Fill="{StaticResource StandardColor}" Data="F0 M128,128z M0,0z M38.02,86.703C43.9589,89.5746 52.739,93.0778 57.534,94.3488 55.9389,96.073 54.3343,97.8355 53.1616,99.1894 49.3668,103.57 47.3992,104.276 37.3146,104.276 30.9964,104.276 20.0276,109 20.0276,109 20.0276,109 18.5527,104.276 18.5871,99.5527 18.6266,94.1216 23.269,86.4717 23.269,86.4717 23.269,86.4717 32.0784,87.4477 38.019,86.7028z" />
            <Path Fill="{StaticResource StandardColor}" Data="F0 M128,128z M0,0z M34.027,84.608C30.4273,84.8633 26.0365,84.7771 22.189,83.5651 15.6339,81.4999 12.5004,79.8284 9.584,74.4813 8.79195,73.0292 8.5318,71.6766 8.2763,70.3476 8.075,69.3008 7.87652,68.2687 7.42314,67.2142 4.99504,61.5667 -0.500160000000001,53.0432 -0.500160000000001,53.0432 -0.500160000000001,53.0432 5.51694,48.6186 9.58384,47.2297 13.73304,45.8127 20.74884,46.1394 20.74884,46.1394L24.09034,49.6712C22.26904,53.6261 21.00004,57.0033 21.00004,58.5724 21.00004,61.0123 29.03304,80.3954 32.24604,83.3774 32.60844,83.7137 33.22124,84.1319 34.02674,84.6076z" />
            <Path Fill="{StaticResource StandardColor}" Data="F0 M128,128z M0,0z M39.327,26.4C37.9821,27.1444 36.9273,27.8479 36.2628,28.4804 34.3933,30.26 28.8961,39.6684 25.0378,47.6634L23.2708,45.7757C23.2708,45.7757 21.2731,44.3223 22.1894,35.9651 22.8012,30.3856 23.6667,22.7841 26.1514,21.7941 28.4357,20.8842 30.0897,21.2142 32.3814,21.6716 32.5825,21.7117 32.7885,21.7528 33.0002,21.7941 34.4363,22.0744 36.8905,24.0818 39.3271,26.3998z" />
            <Path Fill="{StaticResource StandardColor}" Data="F0 M128,128z M0,0z M89.599,100.05C92.6898,96.9955 97.5683,90.6463 101.664,83.429 104.637,82.7602 108.625,81.7459 108.625,81.7459 108.625,81.7459 112.472,87.8688 113.667,92.2829 114.927,96.937 113.667,104.6369 113.667,104.6369 113.667,104.6369 108.107,102.2959 102.863,101.3669 99.2947,100.7349 94.3482,100.3209 89.599,100.0499z" />
            <Path Fill="{StaticResource StandardColor}" Data="F0 M128,128z M0,0z M103.1,80.808C106.151,74.9857 108.525,68.805 108.965,63.452 109.269,59.7648 107.763,54.9512 105.225,49.791 105.284,49.6627 105.338,49.5355 105.388,49.4099 106.621,46.3105 106.829,40.6894 106.829,40.6894 106.829,40.6894 113.044,39.9452 116.913,40.6893 121.43,41.5583 127.433,44.3388 127.717,45.413 128.784,49.4479 126.692,54.2793 124.511,59.318 123.746,61.0854 122.97,62.8784 122.315,64.6713 119.706,71.815 118.082,73.1116 116.255,74.5699 115.999,74.7741 115.739,74.9815 115.472,75.2086 112.094,78.0808 107.186,79.8029 103.1,80.8089z" />
            <Path Fill="{StaticResource StandardColor}" Data="F0 M128,128z M0,0z M103.9,47.236C99.585,39.2963 93.201,30.856 87.429,24.606 88.0565,23.7693 88.7663,23.0203 89.54,22.5211 89.7641,22.3765 89.9896,22.2271 90.2159,22.0772 92.1052,20.8256 94.0465,19.5397 95.6625,20.7043 98.1835,22.5211 104.6662,36.6923 104.6662,42.8683 104.6662,44.3281 104.3432,45.8443 103.9002,47.2357z" />
            <Path Fill="{StaticResource StandardColor}" Data="F1 M128,128z M0,0z M72.235,79.21C73.0695,76.488 73.3789,74.7684 72.9561,71.9516 72.4948,68.8782 71.8963,66.8896 69.711,64.6933 67.25,62.2199 60.6967,61.427 60.6967,61.427 60.6967,61.427 60.6967,69.285 58.5333,70.1371 57.4512,70.5632 51.5224,65.8501 51.3218,61.427 51.2811,60.5298 57.3469,52.6993 63.2208,51.9911 64.6627,51.8172 78.1658,58.8699 79.4468,67.5971 80.2304,72.9361 78.9604,76.3974 76.2017,81.0251 74.0383,84.6543 52.4037,101.7121 52.4037,101.7121 52.4037,101.7121 55.4531,103.6071 63.2207,102.8001 70.7181,102.0221 82.9067,98.5642 84.8547,97.3566 88.8207,94.8979 102.8837,76.3076 103.9647,63.2426 105.0467,50.1776 80.5277,21.2666 74.0377,20.0556 69.7108,19.2477 43.3887,27.5962 38.7017,32.0316 36.0172,34.572 24.9997,54.5326 24.9997,58.8876 24.9997,61.0651 32.2111,78.3646 35.0957,81.0256 37.9803,83.6871 58.5337,92.0876 60.6967,91.1876 63.2207,90.1376 71.4007,81.9333 72.2347,79.2116z" />
            <Path Fill="{StaticResource StandardColor}" Data="F1 M128,128z M0,0z M72.235,79.21C73.0695,76.488 73.3789,74.7684 72.9561,71.9516 72.4948,68.8782 71.8963,66.8896 69.711,64.6933 67.25,62.2199 60.6967,61.427 60.6967,61.427 60.6967,61.427 60.6967,69.285 58.5333,70.1371 57.4512,70.5632 51.5224,65.8501 51.3218,61.427 51.2811,60.5298 57.3469,52.6993 63.2208,51.9911 64.6627,51.8172 78.1658,58.8699 79.4468,67.5971 80.2304,72.9361 78.9604,76.3974 76.2017,81.0251 74.0383,84.6543 52.4037,101.7121 52.4037,101.7121 52.4037,101.7121 55.4531,103.6071 63.2207,102.8001 70.7181,102.0221 82.9067,98.5642 84.8547,97.3566 88.8207,94.8979 102.8837,76.3076 103.9647,63.2426 105.0467,50.1776 80.5277,21.2666 74.0377,20.0556 69.7108,19.2477 43.3887,27.5962 38.7017,32.0316 36.0172,34.572 24.9997,54.5326 24.9997,58.8876 24.9997,61.0651 32.2111,78.3646 35.0957,81.0256 37.9803,83.6871 58.5337,92.0876 60.6967,91.1876 63.2207,90.1376 71.4007,81.9333 72.2347,79.2116z" />
        </Canvas>
        <Canvas x:Key="mode_twist" Clip="M0,0 V128 H128 V0 H0 Z"> <!-- TODO -->
            <Path Fill="{StaticResource StandardColor}" Data="F0 M128,128z M0,0z M38.02,86.703C43.9589,89.5746 52.739,93.0778 57.534,94.3488 55.9389,96.073 54.3343,97.8355 53.1616,99.1894 49.3668,103.57 47.3992,104.276 37.3146,104.276 30.9964,104.276 20.0276,109 20.0276,109 20.0276,109 18.5527,104.276 18.5871,99.5527 18.6266,94.1216 23.269,86.4717 23.269,86.4717 23.269,86.4717 32.0784,87.4477 38.019,86.7028z" />
            <Path Fill="{StaticResource StandardColor}" Data="F0 M128,128z M0,0z M34.027,84.608C30.4273,84.8633 26.0365,84.7771 22.189,83.5651 15.6339,81.4999 12.5004,79.8284 9.584,74.4813 8.79195,73.0292 8.5318,71.6766 8.2763,70.3476 8.075,69.3008 7.87652,68.2687 7.42314,67.2142 4.99504,61.5667 -0.500160000000001,53.0432 -0.500160000000001,53.0432 -0.500160000000001,53.0432 5.51694,48.6186 9.58384,47.2297 13.73304,45.8127 20.74884,46.1394 20.74884,46.1394L24.09034,49.6712C22.26904,53.6261 21.00004,57.0033 21.00004,58.5724 21.00004,61.0123 29.03304,80.3954 32.24604,83.3774 32.60844,83.7137 33.22124,84.1319 34.02674,84.6076z" />
            <Path Fill="{StaticResource StandardColor}" Data="F0 M128,128z M0,0z M39.327,26.4C37.9821,27.1444 36.9273,27.8479 36.2628,28.4804 34.3933,30.26 28.8961,39.6684 25.0378,47.6634L23.2708,45.7757C23.2708,45.7757 21.2731,44.3223 22.1894,35.9651 22.8012,30.3856 23.6667,22.7841 26.1514,21.7941 28.4357,20.8842 30.0897,21.2142 32.3814,21.6716 32.5825,21.7117 32.7885,21.7528 33.0002,21.7941 34.4363,22.0744 36.8905,24.0818 39.3271,26.3998z" />
            <Path Fill="{StaticResource StandardColor}" Data="F0 M128,128z M0,0z M89.599,100.05C92.6898,96.9955 97.5683,90.6463 101.664,83.429 104.637,82.7602 108.625,81.7459 108.625,81.7459 108.625,81.7459 112.472,87.8688 113.667,92.2829 114.927,96.937 113.667,104.6369 113.667,104.6369 113.667,104.6369 108.107,102.2959 102.863,101.3669 99.2947,100.7349 94.3482,100.3209 89.599,100.0499z" />
            <Path Fill="{StaticResource StandardColor}" Data="F0 M128,128z M0,0z M103.1,80.808C106.151,74.9857 108.525,68.805 108.965,63.452 109.269,59.7648 107.763,54.9512 105.225,49.791 105.284,49.6627 105.338,49.5355 105.388,49.4099 106.621,46.3105 106.829,40.6894 106.829,40.6894 106.829,40.6894 113.044,39.9452 116.913,40.6893 121.43,41.5583 127.433,44.3388 127.717,45.413 128.784,49.4479 126.692,54.2793 124.511,59.318 123.746,61.0854 122.97,62.8784 122.315,64.6713 119.706,71.815 118.082,73.1116 116.255,74.5699 115.999,74.7741 115.739,74.9815 115.472,75.2086 112.094,78.0808 107.186,79.8029 103.1,80.8089z" />
            <Path Fill="{StaticResource StandardColor}" Data="F0 M128,128z M0,0z M103.9,47.236C99.585,39.2963 93.201,30.856 87.429,24.606 88.0565,23.7693 88.7663,23.0203 89.54,22.5211 89.7641,22.3765 89.9896,22.2271 90.2159,22.0772 92.1052,20.8256 94.0465,19.5397 95.6625,20.7043 98.1835,22.5211 104.6662,36.6923 104.6662,42.8683 104.6662,44.3281 104.3432,45.8443 103.9002,47.2357z" />
            <Path Fill="{StaticResource StandardColor}" Data="F1 M128,128z M0,0z M72.235,79.21C73.0695,76.488 73.3789,74.7684 72.9561,71.9516 72.4948,68.8782 71.8963,66.8896 69.711,64.6933 67.25,62.2199 60.6967,61.427 60.6967,61.427 60.6967,61.427 60.6967,69.285 58.5333,70.1371 57.4512,70.5632 51.5224,65.8501 51.3218,61.427 51.2811,60.5298 57.3469,52.6993 63.2208,51.9911 64.6627,51.8172 78.1658,58.8699 79.4468,67.5971 80.2304,72.9361 78.9604,76.3974 76.2017,81.0251 74.0383,84.6543 52.4037,101.7121 52.4037,101.7121 52.4037,101.7121 55.4531,103.6071 63.2207,102.8001 70.7181,102.0221 82.9067,98.5642 84.8547,97.3566 88.8207,94.8979 102.8837,76.3076 103.9647,63.2426 105.0467,50.1776 80.5277,21.2666 74.0377,20.0556 69.7108,19.2477 43.3887,27.5962 38.7017,32.0316 36.0172,34.572 24.9997,54.5326 24.9997,58.8876 24.9997,61.0651 32.2111,78.3646 35.0957,81.0256 37.9803,83.6871 58.5337,92.0876 60.6967,91.1876 63.2207,90.1376 71.4007,81.9333 72.2347,79.2116z" />
            <Path Fill="{StaticResource StandardColor}" Data="F1 M128,128z M0,0z M72.235,79.21C73.0695,76.488 73.3789,74.7684 72.9561,71.9516 72.4948,68.8782 71.8963,66.8896 69.711,64.6933 67.25,62.2199 60.6967,61.427 60.6967,61.427 60.6967,61.427 60.6967,69.285 58.5333,70.1371 57.4512,70.5632 51.5224,65.8501 51.3218,61.427 51.2811,60.5298 57.3469,52.6993 63.2208,51.9911 64.6627,51.8172 78.1658,58.8699 79.4468,67.5971 80.2304,72.9361 78.9604,76.3974 76.2017,81.0251 74.0383,84.6543 52.4037,101.7121 52.4037,101.7121 52.4037,101.7121 55.4531,103.6071 63.2207,102.8001 70.7181,102.0221 82.9067,98.5642 84.8547,97.3566 88.8207,94.8979 102.8837,76.3076 103.9647,63.2426 105.0467,50.1776 80.5277,21.2666 74.0377,20.0556 69.7108,19.2477 43.3887,27.5962 38.7017,32.0316 36.0172,34.572 24.9997,54.5326 24.9997,58.8876 24.9997,61.0651 32.2111,78.3646 35.0957,81.0256 37.9803,83.6871 58.5337,92.0876 60.6967,91.1876 63.2207,90.1376 71.4007,81.9333 72.2347,79.2116z" />
        </Canvas>
    </UserControl.Resources>
    <Grid Name="WindowGrid">
        <DockPanel Name="StackPanelMain">
            <!-- Deck Title/Header -->
            <Border BorderBrush="Black" BorderThickness="1" Height="40" Width="217" x:Name="DeckTitleContainer" DockPanel.Dock="Top">
                <DockPanel Name="DeckTitlePanel" Height="40" Width="217" LastChildFill="True">
                    <local:HearthstoneTextBlock x:Name="LblDeckTitle" FontSize="16" Margin="4,2" Text="Deck title" TextAlignment="Left" VerticalAlignment="Bottom"/>
                </DockPanel>
            </Border>
            <!-- Format/Tags -->
            <DockPanel x:Name="DeckFormatPanel" Height="24" Width="217" DockPanel.Dock="Top">
                <DockPanel.Background>
                    <ImageBrush ImageSource="pack://application:,,,/Resources/tactile_noise.png"
                                Stretch="None" ViewboxUnits="Absolute" AlignmentX="Left" AlignmentY="Top"
                                Viewport="0,0,48,48" TileMode="Tile" ViewportUnits="Absolute"/>
                </DockPanel.Background>
                <StackPanel DockPanel.Dock="Left" Margin="0,0,2,0" Orientation="Horizontal">
                    <Rectangle Name="RectIconStandard" Width="16" Height="16" Margin="6,0,4,0">
                        <Rectangle.Fill>
                            <VisualBrush Stretch="Uniform" Visual="{StaticResource mode_standard}" />
                        </Rectangle.Fill>
                    </Rectangle>
                    <Rectangle Name="RectIconWild" Width="22" Height="22" Margin="6,0,4,0">
                        <Rectangle.Fill>
                            <VisualBrush Stretch="Uniform" Visual="{StaticResource mode_wild}" />
                        </Rectangle.Fill>
                    </Rectangle>
                    <Rectangle Name="RectIconArena" Width="18" Height="18" Margin="6,0,4,0">
                        <Rectangle.Fill>
                            <VisualBrush Stretch="Uniform" Visual="{StaticResource mode_arena}" />
                        </Rectangle.Fill>
                    </Rectangle>
                    <Rectangle Name="RectIconBrawl" Width="16" Height="16" Margin="6,0,4,0">
                        <Rectangle.Fill>
                            <VisualBrush Stretch="Uniform" Visual="{StaticResource mode_brawl}" />
                        </Rectangle.Fill>
                    </Rectangle>
                    <Rectangle Name="RectIconAdventure" Width="16" Height="16" Margin="6,0,4,0">
                        <Rectangle.Fill>
                            <VisualBrush Stretch="Uniform" Visual="{StaticResource mode_adventure}" />
                        </Rectangle.Fill>
                    </Rectangle>
                    <Rectangle Name="RectIconDuels" Width="16" Height="16" Margin="6,0,4,0">
                        <Rectangle.Fill>
                            <VisualBrush Stretch="Uniform" Visual="{StaticResource mode_duels}" />
                        </Rectangle.Fill>
                    </Rectangle>
                    <Rectangle Name="RectIconClassic" Width="16" Height="16" Margin="6,0,4,0">
                        <Rectangle.Fill>
                            <VisualBrush Stretch="Uniform" Visual="{StaticResource mode_classic}" />
                        </Rectangle.Fill>
                    </Rectangle>
                    <Rectangle Name="RectIconTwist" Width="16" Height="16" Margin="6,0,4,0">
                        <Rectangle.Fill>
                            <VisualBrush Stretch="Uniform" Visual="{StaticResource mode_twist}" />
                        </Rectangle.Fill>
                    </Rectangle>
                </StackPanel>
                <TextBlock x:Name="LblDeckFormat" Margin="0,0,2,0" FontSize="14" Height="20" Foreground="White">Wild</TextBlock>
                <TextBlock x:Name="LblDeckTag" DockPanel.Dock="Right" HorizontalAlignment="Right" Margin="4,0" FontSize="14" Height="20" Foreground="White">MidRange</TextBlock>
            </DockPanel>
            <!-- hsdecktracker -->
            <Border BorderBrush="White" BorderThickness="0,1,0,0" Height="24" Width="217" x:Name="BrandContainer" DockPanel.Dock="Bottom">
                <StackPanel Orientation="Horizontal" Height="24" Width="217">
                    <StackPanel.Background>
                        <ImageBrush ImageSource="pack://application:,,,/Resources/embroidery.png"
                                Stretch="UniformToFill" AlignmentX="Left" AlignmentY="Top"/>
                    </StackPanel.Background>
                    <Rectangle Margin="4" Width="16" Height="16" DockPanel.Dock="Left">
                        <Rectangle.Fill>
                            <VisualBrush Visual="{StaticResource hdticon}" />
                        </Rectangle.Fill>
                    </Rectangle>
                    <TextBlock Margin="4,0" FontSize="14" Height="20" Foreground="White">HSReplay.net</TextBlock>
                </StackPanel>
            </Border>
            <!-- Sets/Dust Panel -->
            <DockPanel MinHeight="34" Width="217" x:Name="SetDustPanel" DockPanel.Dock="Bottom">
                <DockPanel.Background>
                    <ImageBrush ImageSource="pack://application:,,,/Resources/tactile_noise.png"
                                Stretch="None" ViewboxUnits="Absolute" AlignmentX="Left" AlignmentY="Top"
                                Viewport="0,0,48,48" TileMode="Tile" ViewportUnits="Absolute"/>
                </DockPanel.Background>
                <!-- Dust Info -->
                <StackPanel Orientation="Horizontal" Margin="4,6,4,4" DockPanel.Dock="Right" HorizontalAlignment="Right" VerticalAlignment="Top">
                    <Image Source="pack://application:,,,/Resources/dust.png" Height="20" Width="20"/>
                    <TextBlock x:Name="LblDustCost" Margin="2,0" FontSize="14" Foreground="White" VerticalAlignment="Center">12800</TextBlock>
                </StackPanel>
                <!-- Set Icons -->
                <deckSetIcons:DeckSetIconsView x:Name="SetIcons" Fill="White" Margin="2,4" VerticalAlignment="Center" Padding="2" />
            </DockPanel>
            <!-- Card List -->
            <Viewbox Name="ViewBoxPlayer" StretchDirection="DownOnly" Stretch="Fill">
                <controls1:AnimatedCardList x:Name="ListViewPlayer">
                    <controls1:AnimatedCardList.Resources>
                        <Style TargetType="controls1:AnimatedCard">
                            <Setter Property="Margin" Value="0,1,0,0"/>
                        </Style>
                    </controls1:AnimatedCardList.Resources>
                </controls1:AnimatedCardList>
            </Viewbox>
        </DockPanel>
    </Grid>
</UserControl>
