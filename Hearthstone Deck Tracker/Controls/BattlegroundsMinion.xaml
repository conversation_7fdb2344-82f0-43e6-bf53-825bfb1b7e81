﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.BattlegroundsMinion"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             xmlns:s="clr-namespace:System;assembly=mscorlib"
             xmlns:controls="clr-namespace:Hearthstone_Deck_Tracker.Controls"
             xmlns:battlegrounds="clr-namespace:Hearthstone_Deck_Tracker.Controls.Overlay.Battlegrounds"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance controls:BattlegroundsMinionViewModel}"
             d:DesignHeight="256" d:DesignWidth="256">
    <Grid HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="11*"/>
            <ColumnDefinition Width="88*"/>
            <ColumnDefinition Width="11*"/>
        </Grid.ColumnDefinitions>
        <Grid.Style>
            <Style TargetType="Grid">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsAvailable}" Value="False">
                        <Setter Property="OpacityMask">
                            <Setter.Value>
                                <SolidColorBrush Color="#808080"/>
                            </Setter.Value>
                        </Setter>
                        <Setter Property="Opacity" Value="0.5"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </Grid.Style>
        <Ellipse IsHitTestVisible="True" Fill="Black" Opacity="0.01" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Column="1"/>
            <Viewbox IsHitTestVisible="False" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Column="0" Grid.ColumnSpan="3">
                <Canvas Width="256" Height="256">
                    <Canvas.Resources>
                        <Style TargetType="Image">
                            <Setter Property="RenderOptions.BitmapScalingMode" Value="Fant"/>
                        </Style>
                    </Canvas.Resources>
                    <Image Source="{StaticResource Taunt}" Canvas.Left="-24" Canvas.Top="-36">
                        <Image.Visibility>
                            <MultiBinding Converter="{StaticResource BoolToVisibility}">
                                <MultiBinding.ConverterParameter>
                                    <x:Array Type="{x:Type s:Boolean}">
                                        <s:Boolean>False</s:Boolean>
                                        <s:Boolean>True</s:Boolean>
                                    </x:Array>
                                </MultiBinding.ConverterParameter>
                                <Binding Path="HasTaunt" />
                                <Binding Path="IsPremium" />
                            </MultiBinding>
                        </Image.Visibility>
                    </Image>

                    <Image Source="{StaticResource PremiumTaunt}" Canvas.Left="-24" Canvas.Top="-36" d:Visibility="Hidden">
                        <Image.Visibility>
                            <MultiBinding Converter="{StaticResource BoolToVisibility}">
                                <Binding Path="HasTaunt" />
                                <Binding Path="IsPremium" />
                            </MultiBinding>
                        </Image.Visibility>
                    </Image>

                    <Image HorizontalAlignment="Center" Width="256" Height="256" Source="{Binding CardPortrait.Asset}">
                        <Image.Clip>
                            <EllipseGeometry RadiusX="87" RadiusY="120" Center="128,128"/>
                        </Image.Clip>
                    </Image>

                    <Image Source="{StaticResource MinionBorder}" Visibility="{Binding IsPremium, Converter={StaticResource InverseBoolToVisibility}}" Canvas.Left="-24" Canvas.Top="-36" Height="350" Width="300"/>
                    <Image Source="{StaticResource PremiumMinionBorder}" Visibility="{Binding IsPremium, Converter={StaticResource BoolToVisibility}}" Canvas.Left="-24" Canvas.Top="-36" Height="350" Width="300" d:Visibility="Hidden"/>
                    <Image Source="{StaticResource Reborn}" Visibility="{Binding HasReborn, Converter={StaticResource BoolToVisibility}}" Canvas.Left="-24" Canvas.Top="-36" Height="350" Width="300"/>

                    <Image Source="{StaticResource Legendary}" Canvas.Left="-24" Canvas.Top="-36" Height="350" Width="300">
                        <Image.Visibility>
                            <MultiBinding Converter="{StaticResource BoolToVisibility}">
                                <MultiBinding.ConverterParameter>
                                    <x:Array Type="{x:Type s:Boolean}">
                                        <s:Boolean>False</s:Boolean>
                                        <s:Boolean>True</s:Boolean>
                                    </x:Array>
                                </MultiBinding.ConverterParameter>
                                <Binding Path="IsLegendary" />
                                <Binding Path="IsPremium" />
                            </MultiBinding>
                        </Image.Visibility>
                    </Image>
                    <Image Source="{StaticResource PremiumLegendary}" Canvas.Left="-24" Canvas.Top="-36" Height="350" Width="300" d:Visibility="Hidden">
                        <Image.Visibility>
                            <MultiBinding Converter="{StaticResource BoolToVisibility}">
                                <Binding Path="IsLegendary" />
                                <Binding Path="IsPremium" />
                            </MultiBinding>
                        </Image.Visibility>
                    </Image>

                    <Image Source="{StaticResource Deathrattle}" Visibility="{Binding HasDeathrattle, Converter={StaticResource BoolToVisibility}}" Canvas.Left="-24" Canvas.Top="-36" Height="350" Width="300"/>
                    <Image Source="{StaticResource Poisonous}" Visibility="{Binding HasPoisonous, Converter={StaticResource BoolToVisibility}}" Canvas.Left="-24" Canvas.Top="-36" Height="350" Width="300"/>
                    <Image Source="{StaticResource Venomous}" Visibility="{Binding HasVenomous, Converter={StaticResource BoolToVisibility}}" Canvas.Left="-24" Canvas.Top="-36" Height="350" Width="300"/>
                    <Image Source="{StaticResource MinionStats}" Visibility="{Binding IsPremium, Converter={StaticResource InverseBoolToVisibility}}" Canvas.Left="-24" Canvas.Top="-36" Height="350" Width="300"/>
                    <Image Source="{StaticResource PremiumMinionStats}" Visibility="{Binding IsPremium, Converter={StaticResource BoolToVisibility}}" Canvas.Left="-24" Canvas.Top="-36" Height="350" Width="300" d:Visibility="Hidden"/>
                    <Image Source="{StaticResource DivineShield}" Visibility="{Binding HasDivineShield, Converter={StaticResource BoolToVisibility}}" Canvas.Left="-36" Canvas.Top="-24" Height="311" Width="325"/>

                <hearthstoneDeckTracker:HearthstoneTextBlock Text="{Binding Attack}" Fill="{Binding AttackBrush}" Width="75" Height="75" FontSize="45" TextAlignment="Center" FontWeight="Bold" Canvas.Left="29" Canvas.Top="170" />
                <hearthstoneDeckTracker:HearthstoneTextBlock Text="{Binding Health}" Fill="{Binding HealthBrush}" Width="75" Height="75" FontSize="45" TextAlignment="Center" FontWeight="Bold" Canvas.Left="151" Canvas.Top="170" />
                <battlegrounds:BattlegroundsTier Visibility="{Binding HasTier, Converter={StaticResource BoolToVisibility}}" Tier="{Binding Tier}" d:Tier="6" Canvas.Left="87" Canvas.Top="-37" HorizontalAlignment="Left" VerticalAlignment="Top">
                    <battlegrounds:BattlegroundsTier.LayoutTransform>
                        <ScaleTransform ScaleX="3" ScaleY="3"/>
                    </battlegrounds:BattlegroundsTier.LayoutTransform>
                </battlegrounds:BattlegroundsTier>
            </Canvas>
        </Viewbox>
    </Grid>
</UserControl>
