﻿<UserControl x:Class="Hearthstone_Deck_Tracker.Controls.GridCardImages"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hearthstoneDeckTracker="clr-namespace:Hearthstone_Deck_Tracker"
             xmlns:controls="clr-namespace:Hearthstone_Deck_Tracker.Controls"
             mc:Ignorable="d"
             d:DesignHeight="300" d:DesignWidth="300">
    <Border Background="#CC2E3235" CornerRadius="10">
        <DockPanel DataContext="{Binding ViewModel, RelativeSource={RelativeSource AncestorType=controls:GridCardImages}}">
            <Border DockPanel.Dock="Top" Background="#1C2022" CornerRadius="{Binding TitleCornerRadius}">
                <hearthstoneDeckTracker:HearthstoneTextBlock
                    Text="{Binding Title, RelativeSource={RelativeSource AncestorType=controls:GridCardImages}}"
                    FontSize="17" TextAlignment="Center"
                    Margin="10"
                />
            </Border>
            <ItemsControl ItemsSource="{Binding Cards}" Margin="5,0"
                          MaxWidth="{Binding MaxGridWidth, RelativeSource={RelativeSource AncestorType=controls:GridCardImages}}"
                          MaxHeight="{Binding MaxCardGridHeight, RelativeSource={RelativeSource AncestorType=controls:GridCardImages}}"
                          HorizontalAlignment="Center">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <WrapPanel Orientation="Horizontal" LayoutTransform="{Binding ViewModel.CardScale, RelativeSource={RelativeSource AncestorType=controls:GridCardImages}}" />
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Image Source="{Binding Asset}"
                               Margin="{Binding CardMargin, RelativeSource={RelativeSource AncestorType=controls:GridCardImages}}"
                               Width="{Binding CardWidth, RelativeSource={RelativeSource AncestorType=controls:GridCardImages}}"
                               Height="{Binding CardHeight, RelativeSource={RelativeSource AncestorType=controls:GridCardImages}}"/>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </DockPanel>
    </Border>
</UserControl>
